<!DOCTYPE html>
<html>
<head>
    <title>Cookie Test</title>
    <script src="https://cdn.jsdelivr.net/npm/js-cookie@3.0.5/dist/js.cookie.min.js"></script>
</head>
<body>
    <h1>Cookie Test</h1>
    <button onclick="setCookie()">Set Cookie</button>
    <button onclick="getCookie()">Get Cookie</button>
    <button onclick="clearCookie()">Clear Cookie</button>
    <div id="result"></div>

    <script>
        function setCookie() {
            Cookies.set('table_1_time_limit', '30', { 
                expires: 7,
                sameSite: 'strict'
            });
            document.getElementById('result').innerHTML = '✅ Cookie set: table_1_time_limit = 30';
            console.log('✅ Cookie set: table_1_time_limit = 30');
        }

        function getCookie() {
            const value = Cookies.get('table_1_time_limit');
            document.getElementById('result').innerHTML = `📋 Cookie value: ${value || 'not found'}`;
            console.log(`📋 Cookie value: ${value || 'not found'}`);
            
            // Also test getting all cookies
            const allCookies = Cookies.get();
            console.log('🍪 All cookies:', allCookies);
            
            // Find table time limit cookies
            const tableCookies = {};
            Object.keys(allCookies).forEach(cookieName => {
                if (cookieName.startsWith('table_') && cookieName.endsWith('_time_limit')) {
                    const tableNumberMatch = cookieName.match(/table_(\d+)_time_limit/);
                    if (tableNumberMatch) {
                        const tableNumber = parseInt(tableNumberMatch[1]);
                        const limit = allCookies[cookieName] === 'unlimited' ? 'unlimited' : parseInt(allCookies[cookieName]);
                        tableCookies[tableNumber] = limit;
                    }
                }
            });
            console.log('🎱 Table time limit cookies:', tableCookies);
        }

        function clearCookie() {
            Cookies.remove('table_1_time_limit');
            document.getElementById('result').innerHTML = '🗑️ Cookie cleared';
            console.log('🗑️ Cookie cleared');
        }

        // Test on page load
        window.onload = function() {
            getCookie();
        };
    </script>
</body>
</html>
