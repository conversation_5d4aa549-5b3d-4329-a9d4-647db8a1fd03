import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  // Handle Chrome DevTools well-known requests
  if (request.nextUrl.pathname.startsWith('/.well-known/appspecific/')) {
    return new NextResponse(
      JSON.stringify({
        name: "BBM Web App",
        version: "1.0.0",
        description: "Billiard club management system"
      }),
      {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
        },
      }
    )
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    '/.well-known/appspecific/:path*',
  ],
}
