-- Add time_limit column to games table for time limit persistence
-- This fixes the console error: "time_limit column does not exist in games table"

-- Check if column exists (for information only)
SELECT 
    column_name, 
    data_type, 
    is_nullable 
FROM information_schema.columns 
WHERE table_name = 'games' AND column_name = 'time_limit';

-- Add time_limit column to games table
-- NULL means unlimited, integer value is time limit in minutes
ALTER TABLE games 
ADD COLUMN IF NOT EXISTS time_limit INTEGER;

-- Add index for better performance when querying by time_limit
CREATE INDEX IF NOT EXISTS idx_games_time_limit ON games(time_limit);

-- Add comment for documentation
COMMENT ON COLUMN games.time_limit IS 'Time limit for the game in minutes. NULL means unlimited.';

-- Update existing active games to have unlimited time limit if not set
UPDATE games 
SET time_limit = NULL 
WHERE time_limit IS NULL AND status = 'active';

-- Add a check constraint to ensure time_limit is positive when set
ALTER TABLE games 
ADD CONSTRAINT IF NOT EXISTS check_games_time_limit_positive 
CHECK (time_limit IS NULL OR time_limit > 0);

-- Verify the column was added successfully
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'games' AND column_name = 'time_limit';

-- Show sample of games table structure
\d games;

-- Optional: Show current active games (if any)
SELECT id, table_number, start_time, status, time_limit 
FROM games 
WHERE status = 'active' 
LIMIT 5;
