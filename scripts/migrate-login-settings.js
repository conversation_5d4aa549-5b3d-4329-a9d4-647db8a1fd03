#!/usr/bin/env node

/**
 * Migration script to update login_settings table for dynamic waiters
 * Run this script to migrate your PostgreSQL database to support the new dynamic waiter structure
 */

const { Pool } = require('pg')
const fs = require('fs')
const path = require('path')

// Database configuration - using your specific settings
const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://postgres:postgres123@localhost:5431/postgres',
  // Fallback to individual config
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'postgres',
  password: process.env.DB_PASSWORD || 'postgres123',
  port: process.env.DB_PORT || 5431,
})

async function runMigration() {
  const client = await pool.connect()
  
  try {
    console.log('🚀 Starting login_settings migration...')
    
    // Read the migration SQL file
    const migrationPath = path.join(__dirname, '..', 'database', 'migrations', 'update_login_settings_dynamic_waiters.sql')
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8')
    
    // Execute the migration
    await client.query(migrationSQL)
    
    console.log('✅ Migration completed successfully!')
    console.log('📋 Summary of changes:')
    console.log('   - Added "waiters" JSONB column to login_settings table')
    console.log('   - Migrated existing waiter1_*/waiter2_* data to new waiters array')
    console.log('   - Added GIN index for better performance')
    console.log('   - Preserved old columns for rollback (can be removed later)')
    
    // Verify the migration worked
    const result = await client.query('SELECT waiters_section_enabled, waiters_section_title, waiters FROM login_settings ORDER BY id DESC LIMIT 1')
    
    if (result.rows.length > 0) {
      console.log('\n🔍 Verification - Current login settings:')
      console.log('   Section enabled:', result.rows[0].waiters_section_enabled)
      console.log('   Section title:', result.rows[0].waiters_section_title)
      console.log('   Waiters count:', Array.isArray(result.rows[0].waiters) ? result.rows[0].waiters.length : 0)
      
      if (Array.isArray(result.rows[0].waiters)) {
        result.rows[0].waiters.forEach((waiter, index) => {
          console.log(`   Waiter ${index + 1}: ${waiter.display_name} (${waiter.username}) - ${waiter.enabled ? 'Enabled' : 'Disabled'}`)
        })
      }
    }
    
  } catch (error) {
    console.error('❌ Migration failed:', error.message)
    console.error('Full error:', error)
    process.exit(1)
  } finally {
    client.release()
    await pool.end()
  }
}

// Check if this script is being run directly
if (require.main === module) {
  console.log('🔧 BBM Login Settings Migration Tool')
  console.log('=====================================')
  console.log('This will update your PostgreSQL database to support dynamic waiters.')
  console.log('Make sure your database is running and accessible.\n')
  
  runMigration().catch(error => {
    console.error('❌ Unexpected error:', error)
    process.exit(1)
  })
}

module.exports = { runMigration }
