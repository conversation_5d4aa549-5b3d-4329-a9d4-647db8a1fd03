#!/bin/bash

# BBM Web App - Development Cache Cleaner
# Fixes static asset 404 errors and development issues

echo "🧹 Cleaning BBM Web App development cache..."

# Stop any running development servers
echo "⏹️  Stopping any running development servers..."
pkill -f "next dev" 2>/dev/null || true

# Clean Next.js build cache
echo "🗑️  Removing .next directory..."
rm -rf .next

# Clean npm cache (optional, uncomment if needed)
# echo "🗑️  Cleaning npm cache..."
# npm cache clean --force

# Clean node_modules if requested
if [ "$1" = "--full" ]; then
    echo "🗑️  Removing node_modules (full clean)..."
    rm -rf node_modules package-lock.json
    echo "📦 Reinstalling dependencies..."
    npm install
fi

echo "✅ Cache cleaned successfully!"
echo "🚀 Starting development server..."

# Start development server
npm run dev
