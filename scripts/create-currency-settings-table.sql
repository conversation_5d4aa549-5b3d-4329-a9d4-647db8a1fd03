-- Create currency_settings table
CREATE TABLE IF NOT EXISTS currency_settings (
    id SERIAL PRIMARY KEY,
    currency VARCHAR(50) NOT NULL DEFAULT 'Albanian Lek',
    symbol VARCHAR(10) NOT NULL DEFAULT 'Leke',
    show_decimals BOOLEAN NOT NULL DEFAULT false,
    tax_included BOOLEAN NOT NULL DEFAULT false,
    tax_enabled BOOLEAN NOT NULL DEFAULT true,
    tax_rate DECIMAL(5,2) NOT NULL DEFAULT 20.00,
    qr_code_enabled BOOLEAN NOT NULL DEFAULT false,
    qr_code_url TEXT DEFAULT '',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default currency settings if table is empty
INSERT INTO currency_settings (currency, symbol, show_decimals, tax_included, tax_enabled, tax_rate, qr_code_enabled, qr_code_url)
SELECT 'Albanian Lek', 'L', false, false, true, 20.00, false, ''
WHERE NOT EXISTS (SELECT 1 FROM currency_settings);

-- Create index for faster queries
CREATE INDEX IF NOT EXISTS idx_currency_settings_updated_at ON currency_settings(updated_at DESC);
