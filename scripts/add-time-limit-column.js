#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to add time_limit column to games table
 * Run this script to fix the time limit persistence issue
 */

const { Pool } = require('pg')
require('dotenv').config()

async function addTimeLimitColumn() {
  const pool = new Pool({
    connectionString: process.env.DATABASE_URL,
  })

  try {
    console.log('🔄 Connecting to PostgreSQL database...')
    
    // Test connection
    await pool.query('SELECT NOW()')
    console.log('✅ Database connection successful')

    // Check if column already exists
    console.log('🔍 Checking if time_limit column exists...')
    const columnCheck = await pool.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'games' AND column_name = 'time_limit'
    `)

    if (columnCheck.rows.length > 0) {
      console.log('✅ time_limit column already exists!')
      return
    }

    console.log('📝 Adding time_limit column to games table...')
    
    // Add time_limit column
    await pool.query(`
      ALTER TABLE games 
      ADD COLUMN time_limit INTEGER
    `)
    console.log('✅ time_limit column added successfully')

    // Add index for performance
    console.log('📊 Adding index for better performance...')
    await pool.query(`
      CREATE INDEX IF NOT EXISTS idx_games_time_limit ON games(time_limit)
    `)
    console.log('✅ Index created successfully')

    // Add comment for documentation
    await pool.query(`
      COMMENT ON COLUMN games.time_limit IS 'Time limit for the game in minutes. NULL means unlimited.'
    `)
    console.log('✅ Column comment added')

    // Add check constraint
    console.log('🔒 Adding check constraint...')
    await pool.query(`
      ALTER TABLE games 
      ADD CONSTRAINT check_games_time_limit_positive 
      CHECK (time_limit IS NULL OR time_limit > 0)
    `)
    console.log('✅ Check constraint added')

    // Update existing active games to have unlimited time limit
    console.log('🔄 Updating existing active games...')
    const updateResult = await pool.query(`
      UPDATE games 
      SET time_limit = NULL 
      WHERE time_limit IS NULL AND status = 'active'
    `)
    console.log(`✅ Updated ${updateResult.rowCount} existing games`)

    // Verify the column was added
    console.log('🔍 Verifying column was added...')
    const verifyCheck = await pool.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_name = 'games' AND column_name = 'time_limit'
    `)

    if (verifyCheck.rows.length > 0) {
      console.log('✅ Verification successful!')
      console.log('📋 Column details:', verifyCheck.rows[0])
    } else {
      console.log('❌ Verification failed - column not found')
    }

    console.log('\n🎉 Migration completed successfully!')
    console.log('📝 What was added:')
    console.log('   • time_limit column (INTEGER, nullable)')
    console.log('   • Performance index on time_limit')
    console.log('   • Check constraint for positive values')
    console.log('   • Documentation comment')
    console.log('\n✅ Time limits will now persist across page refreshes!')

  } catch (error) {
    console.error('❌ Migration failed:', error.message)
    console.error('🔍 Full error:', error)
    process.exit(1)
  } finally {
    await pool.end()
    console.log('🔌 Database connection closed')
  }
}

// Run the migration
addTimeLimitColumn()
  .then(() => {
    console.log('\n🚀 You can now test time limit persistence in your BBM app!')
    process.exit(0)
  })
  .catch((error) => {
    console.error('💥 Script failed:', error)
    process.exit(1)
  })
