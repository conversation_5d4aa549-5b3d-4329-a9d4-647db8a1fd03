/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true,
  },
  // Performance optimizations (compatible with Next.js 15)
  experimental: {
    optimizePackageImports: ['lucide-react', '@radix-ui/react-dialog', '@radix-ui/react-select'],
  },
  // Compression and static optimization
  compress: true,
  trailingSlash: false,
  // Reduce bundle size with modular imports
  modularizeImports: {
    'lucide-react': {
      transform: 'lucide-react/dist/esm/icons/{{member}}',
    },
  },
  // Simple webpack optimization (avoiding conflicts)
  webpack: (config, { dev, isServer }) => {
    // Only apply optimizations in production
    if (!dev && !isServer) {
      // Simple tree shaking without conflicting options
      config.optimization = {
        ...config.optimization,
        sideEffects: false,
        // Use Next.js default splitChunks configuration
        splitChunks: {
          ...config.optimization.splitChunks,
          maxSize: 200000, // 200KB max chunk size
        },
      }
    }

    return config
  },
}

export default nextConfig
