/**
 * Fast Storage Utility
 * Optimized for performance-critical reads with background sync
 */

import { getStorageManager } from './StorageManagerFactory'
import { performanceMonitor } from '../performance/PerformanceMonitor'

interface CacheEntry<T> {
  value: T
  timestamp: number
  ttl: number
}

class FastStorage {
  private static instance: FastStorage
  private memoryCache = new Map<string, CacheEntry<any>>()
  private readonly DEFAULT_TTL = 5 * 60 * 1000 // 5 minutes
  private readonly BACKGROUND_SYNC_DELAY = 100 // 100ms delay for background operations

  public static getInstance(): FastStorage {
    if (!FastStorage.instance) {
      FastStorage.instance = new FastStorage()
    }
    return FastStorage.instance
  }

  /**
   * Fast get with memory cache and background database sync
   */
  async getFast<T>(key: string, defaultValue?: T): Promise<T | null> {
    const startTime = performance.now()

    try {
      // 1. Check memory cache first (fastest)
      const cached = this.getFromCache<T>(key)
      if (cached !== null) {
        const duration = performance.now() - startTime
        console.log(`⚡ Fast cache hit for ${key}: ${duration.toFixed(2)}ms`)
        return cached
      }

      // 2. Try localStorage (fast)
      const localStorage = getStorageManager('local')
      const localValue = await localStorage.get(key)
      
      if (localValue !== null) {
        let parsedValue: T
        try {
          parsedValue = JSON.parse(localValue)
        } catch {
          parsedValue = localValue as T
        }

        // Cache in memory for next time
        this.setCache(key, parsedValue)

        const duration = performance.now() - startTime
        console.log(`⚡ Fast localStorage hit for ${key}: ${duration.toFixed(2)}ms`)

        // Background sync from database (non-blocking)
        this.backgroundSync(key, parsedValue)

        return parsedValue
      }

      // 3. If not found locally, try database in background and return default
      this.backgroundDatabaseLoad(key, defaultValue)
      
      const duration = performance.now() - startTime
      console.log(`⚡ Fast fallback for ${key}: ${duration.toFixed(2)}ms`)
      
      return defaultValue || null

    } catch (error) {
      console.warn(`⚠️ Fast storage read failed for ${key}:`, error)
      return defaultValue || null
    }
  }

  /**
   * Fast set with immediate localStorage and background database sync
   */
  async setFast<T>(key: string, value: T): Promise<boolean> {
    try {
      // 1. Update memory cache immediately
      this.setCache(key, value)

      // 2. Update localStorage immediately (fast)
      const localStorage = getStorageManager('local')
      const serialized = JSON.stringify(value)
      await localStorage.set(key, serialized)

      // 3. Background sync to database (non-blocking)
      setTimeout(async () => {
        try {
          const { getUserSetting, setUserSetting } = await import('./index')
          if (key.startsWith('setting_')) {
            const settingKey = key.replace('setting_', '')
            await setUserSetting(settingKey, value)
          }
        } catch (error) {
          console.warn(`⚠️ Background database sync failed for ${key}:`, error)
        }
      }, this.BACKGROUND_SYNC_DELAY)

      return true
    } catch (error) {
      console.error(`❌ Fast storage write failed for ${key}:`, error)
      return false
    }
  }

  /**
   * Get from memory cache
   */
  private getFromCache<T>(key: string): T | null {
    const entry = this.memoryCache.get(key)
    if (!entry) return null

    // Check if expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.memoryCache.delete(key)
      return null
    }

    return entry.value
  }

  /**
   * Set to memory cache
   */
  private setCache<T>(key: string, value: T, ttl: number = this.DEFAULT_TTL): void {
    this.memoryCache.set(key, {
      value,
      timestamp: Date.now(),
      ttl
    })
  }

  /**
   * Background sync from database (non-blocking)
   */
  private backgroundSync<T>(key: string, currentValue: T): void {
    setTimeout(async () => {
      try {
        const { getUserSetting } = await import('./index')
        if (key.startsWith('setting_')) {
          const settingKey = key.replace('setting_', '')
          const dbValue = await getUserSetting<T>(settingKey)
          
          if (dbValue !== null && JSON.stringify(dbValue) !== JSON.stringify(currentValue)) {
            console.log(`🔄 Background sync found newer value for ${key}`)
            
            // Update cache and localStorage with database value
            this.setCache(key, dbValue)
            const localStorage = getStorageManager('local')
            await localStorage.set(key, JSON.stringify(dbValue))
            
            // Emit event for components to update
            if (typeof window !== 'undefined') {
              window.dispatchEvent(new CustomEvent('fastStorageUpdate', {
                detail: { key, value: dbValue }
              }))
            }
          }
        }
      } catch (error) {
        console.warn(`⚠️ Background sync failed for ${key}:`, error)
      }
    }, this.BACKGROUND_SYNC_DELAY)
  }

  /**
   * Background database load (non-blocking)
   */
  private backgroundDatabaseLoad<T>(key: string, defaultValue?: T): void {
    setTimeout(async () => {
      try {
        const { getUserSetting } = await import('./index')
        if (key.startsWith('setting_')) {
          const settingKey = key.replace('setting_', '')
          const dbValue = await getUserSetting<T>(settingKey)
          
          if (dbValue !== null) {
            console.log(`🔄 Background load found value for ${key}`)
            
            // Update cache and localStorage
            this.setCache(key, dbValue)
            const localStorage = getStorageManager('local')
            await localStorage.set(key, JSON.stringify(dbValue))
            
            // Emit event for components to update
            if (typeof window !== 'undefined') {
              window.dispatchEvent(new CustomEvent('fastStorageUpdate', {
                detail: { key, value: dbValue }
              }))
            }
          }
        }
      } catch (error) {
        console.warn(`⚠️ Background database load failed for ${key}:`, error)
      }
    }, this.BACKGROUND_SYNC_DELAY)
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.memoryCache.clear()
  }

  /**
   * Get cache stats
   */
  getCacheStats() {
    return {
      size: this.memoryCache.size,
      keys: Array.from(this.memoryCache.keys()),
      memoryUsage: JSON.stringify(Array.from(this.memoryCache.entries())).length
    }
  }
}

// Export singleton instance
export const fastStorage = FastStorage.getInstance()

/**
 * React hook for fast storage with automatic updates
 */
export function useFastStorage<T>(key: string, defaultValue?: T) {
  const [value, setValue] = React.useState<T | null>(defaultValue || null)
  const [loading, setLoading] = React.useState(true)

  React.useEffect(() => {
    // Load initial value
    fastStorage.getFast<T>(key, defaultValue).then(result => {
      setValue(result)
      setLoading(false)
    })

    // Listen for background updates
    const handleUpdate = (event: CustomEvent) => {
      if (event.detail.key === key) {
        setValue(event.detail.value)
      }
    }

    if (typeof window !== 'undefined') {
      window.addEventListener('fastStorageUpdate', handleUpdate as EventListener)
      return () => {
        window.removeEventListener('fastStorageUpdate', handleUpdate as EventListener)
      }
    }
  }, [key, defaultValue])

  const updateValue = React.useCallback(async (newValue: T) => {
    setValue(newValue)
    await fastStorage.setFast(key, newValue)
  }, [key])

  return {
    value,
    setValue: updateValue,
    loading
  }
}

// Add React import
import React from 'react'
