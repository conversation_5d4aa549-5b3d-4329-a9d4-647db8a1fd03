/**
 * Storage Security Monitor
 * Tamper detection, suspicious activity tracking, and optional encryption
 */

import CryptoJS from 'crypto-js'

export interface SecurityEvent {
  type: 'tamper' | 'suspicious_activity' | 'encryption_error' | 'unauthorized_access'
  timestamp: number
  details: string
  key?: string
  severity: 'low' | 'medium' | 'high' | 'critical'
}

export interface TamperDetectionConfig {
  enabled: boolean
  checksumAlgorithm: 'md5' | 'sha256'
  suspiciousThresholds: {
    rapidWrites: number // writes per minute
    unknownKeys: number // unknown keys per hour
    largeValues: number // bytes
  }
}

class SecurityMonitor {
  private static instance: SecurityMonitor
  private events: SecurityEvent[] = []
  private checksums: Map<string, string> = new Map()
  private writeActivity: Map<string, number[]> = new Map()
  private encryptionKey: string | null = null
  private config: TamperDetectionConfig = {
    enabled: true,
    checksumAlgorithm: 'sha256',
    suspiciousThresholds: {
      rapidWrites: 100, // 100 writes per minute
      unknownKeys: 50,  // 50 unknown keys per hour
      largeValues: 1024 * 1024 // 1MB
    }
  }

  public static getInstance(): SecurityMonitor {
    if (!SecurityMonitor.instance) {
      SecurityMonitor.instance = new SecurityMonitor()
    }
    return SecurityMonitor.instance
  }

  private constructor() {
    this.initializeEncryption()
    this.startPeriodicChecks()
  }

  /**
   * Initialize encryption key from secure source
   */
  private initializeEncryption(): void {
    try {
      // In production, this should come from a secure key management system
      const userAgent = navigator.userAgent
      const timestamp = Date.now().toString()
      this.encryptionKey = CryptoJS.SHA256(userAgent + timestamp).toString()
    } catch (error) {
      console.warn('Failed to initialize encryption:', error)
    }
  }

  /**
   * Configure security monitoring
   */
  configure(config: Partial<TamperDetectionConfig>): void {
    this.config = { ...this.config, ...config }
  }

  /**
   * Generate checksum for value
   */
  private generateChecksum(value: string): string {
    try {
      if (this.config.checksumAlgorithm === 'md5') {
        return CryptoJS.MD5(value).toString()
      } else {
        return CryptoJS.SHA256(value).toString()
      }
    } catch (error) {
      console.error('Failed to generate checksum:', error)
      return ''
    }
  }

  /**
   * Encrypt sensitive value
   */
  encryptValue(value: string, key: string): string {
    if (!this.encryptionKey || !this.shouldEncrypt(key)) {
      return value
    }

    try {
      return CryptoJS.AES.encrypt(value, this.encryptionKey).toString()
    } catch (error) {
      this.recordEvent({
        type: 'encryption_error',
        timestamp: Date.now(),
        details: `Failed to encrypt value for key: ${key}`,
        key,
        severity: 'medium'
      })
      return value // Return original value if encryption fails
    }
  }

  /**
   * Decrypt sensitive value
   */
  decryptValue(encryptedValue: string, key: string): string {
    if (!this.encryptionKey || !this.shouldEncrypt(key)) {
      return encryptedValue
    }

    try {
      const bytes = CryptoJS.AES.decrypt(encryptedValue, this.encryptionKey)
      return bytes.toString(CryptoJS.enc.Utf8)
    } catch (error: unknown) {
      this.recordEvent({
        type: 'encryption_error',
        timestamp: Date.now(),
        details: `Failed to decrypt value for key: ${key}`,
        key,
        severity: 'medium'
      })
      return encryptedValue // Return encrypted value if decryption fails
    }
  }

  /**
   * Check if key should be encrypted
   */
  private shouldEncrypt(key: string): boolean {
    const sensitivePatterns = [
      /password/i,
      /token/i,
      /secret/i,
      /private/i,
      /auth/i,
      /credential/i,
      /api_key/i
    ]

    return sensitivePatterns.some(pattern => pattern.test(key))
  }

  /**
   * Record value for tamper detection
   */
  recordValue(key: string, value: string): void {
    if (!this.config.enabled) return

    try {
      const checksum = this.generateChecksum(value)
      this.checksums.set(key, checksum)

      // Track write activity
      this.trackWriteActivity(key)

      // Check for suspicious activity
      this.checkSuspiciousActivity(key, value)
    } catch (error) {
      console.error('Failed to record value for security monitoring:', error)
    }
  }

  /**
   * Verify value integrity
   */
  verifyIntegrity(key: string, value: string): boolean {
    if (!this.config.enabled) return true

    try {
      const storedChecksum = this.checksums.get(key)
      if (!storedChecksum) {
        // No checksum stored, consider it valid but record as unknown
        this.recordEvent({
          type: 'suspicious_activity',
          timestamp: Date.now(),
          details: `No checksum found for key: ${key}`,
          key,
          severity: 'low'
        })
        return true
      }

      const currentChecksum = this.generateChecksum(value)
      const isValid = storedChecksum === currentChecksum

      if (!isValid) {
        this.recordEvent({
          type: 'tamper',
          timestamp: Date.now(),
          details: `Checksum mismatch for key: ${key}. Expected: ${storedChecksum}, Got: ${currentChecksum}`,
          key,
          severity: 'high'
        })
      }

      return isValid
    } catch (error: unknown) {
      console.error('Failed to verify integrity:', error)
      return true // Assume valid if verification fails
    }
  }

  /**
   * Track write activity for rate limiting detection
   */
  private trackWriteActivity(key: string): void {
    const now = Date.now()
    const activity = this.writeActivity.get(key) || []
    
    // Add current timestamp
    activity.push(now)
    
    // Remove timestamps older than 1 hour
    const oneHourAgo = now - (60 * 60 * 1000)
    const recentActivity = activity.filter(timestamp => timestamp > oneHourAgo)
    
    this.writeActivity.set(key, recentActivity)
  }

  /**
   * Check for suspicious activity patterns
   */
  private checkSuspiciousActivity(key: string, value: string): void {
    const now = Date.now()

    // Check for rapid writes
    const activity = this.writeActivity.get(key) || []
    const oneMinuteAgo = now - (60 * 1000)
    const recentWrites = activity.filter(timestamp => timestamp > oneMinuteAgo).length

    if (recentWrites > this.config.suspiciousThresholds.rapidWrites) {
      this.recordEvent({
        type: 'suspicious_activity',
        timestamp: now,
        details: `Rapid writes detected for key: ${key} (${recentWrites} writes in 1 minute)`,
        key,
        severity: 'medium'
      })
    }

    // Check for large values
    if (value.length > this.config.suspiciousThresholds.largeValues) {
      this.recordEvent({
        type: 'suspicious_activity',
        timestamp: now,
        details: `Large value detected for key: ${key} (${value.length} bytes)`,
        key,
        severity: 'low'
      })
    }

    // Check for unknown key patterns
    if (this.isUnknownKeyPattern(key)) {
      this.recordEvent({
        type: 'suspicious_activity',
        timestamp: now,
        details: `Unknown key pattern detected: ${key}`,
        key,
        severity: 'low'
      })
    }
  }

  /**
   * Check if key follows unknown patterns
   */
  private isUnknownKeyPattern(key: string): boolean {
    const knownPatterns = [
      /^ui_/,
      /^cache_/,
      /^temp_/,
      /^auth_/,
      /^user_/,
      /^game_/,
      /^order_/,
      /^analytics_/,
      /^settings_/
    ]

    return !knownPatterns.some(pattern => pattern.test(key))
  }

  /**
   * Record security event
   */
  private recordEvent(event: SecurityEvent): void {
    this.events.push(event)

    // Log critical events immediately
    if (event.severity === 'critical' || event.severity === 'high') {
      console.warn(`🔒 Security Event [${event.severity.toUpperCase()}]:`, event.details)
    }

    // Keep only last 1000 events
    if (this.events.length > 1000) {
      this.events = this.events.slice(-1000)
    }

    // Persist critical events
    if (event.severity === 'critical') {
      this.persistCriticalEvent(event)
    }
  }

  /**
   * Persist critical events to localStorage
   */
  private persistCriticalEvent(event: SecurityEvent): void {
    try {
      const key = '__security_events__'
      const stored = localStorage.getItem(key)
      const events = stored ? JSON.parse(stored) : []
      
      events.push(event)
      
      // Keep only last 100 critical events
      if (events.length > 100) {
        events.splice(0, events.length - 100)
      }
      
      localStorage.setItem(key, JSON.stringify(events))
    } catch (error) {
      console.error('Failed to persist critical security event:', error)
    }
  }

  /**
   * Start periodic security checks
   */
  private startPeriodicChecks(): void {
    // Check for suspicious patterns every 5 minutes
    setInterval(() => {
      this.performPeriodicChecks()
    }, 5 * 60 * 1000)
  }

  /**
   * Perform periodic security checks
   */
  private performPeriodicChecks(): void {
    try {
      // Check for excessive unknown keys
      const oneHourAgo = Date.now() - (60 * 60 * 1000)
      const recentUnknownKeys = this.events.filter(event => 
        event.type === 'suspicious_activity' && 
        event.timestamp > oneHourAgo &&
        event.details.includes('Unknown key pattern')
      ).length

      if (recentUnknownKeys > this.config.suspiciousThresholds.unknownKeys) {
        this.recordEvent({
          type: 'suspicious_activity',
          timestamp: Date.now(),
          details: `Excessive unknown keys detected: ${recentUnknownKeys} in the last hour`,
          severity: 'high'
        })
      }

      // Clean up old activity data
      this.cleanupOldActivity()
    } catch (error: unknown) {
      console.error('Failed to perform periodic security checks:', error)
    }
  }

  /**
   * Clean up old activity data
   */
  private cleanupOldActivity(): void {
    const oneHourAgo = Date.now() - (60 * 60 * 1000)
    
    for (const [key, activity] of this.writeActivity.entries()) {
      const recentActivity = activity.filter(timestamp => timestamp > oneHourAgo)
      if (recentActivity.length === 0) {
        this.writeActivity.delete(key)
      } else {
        this.writeActivity.set(key, recentActivity)
      }
    }
  }

  /**
   * Get security events
   */
  getEvents(severity?: SecurityEvent['severity']): SecurityEvent[] {
    if (severity) {
      return this.events.filter(event => event.severity === severity)
    }
    return [...this.events]
  }

  /**
   * Get security summary
   */
  getSecuritySummary(): {
    totalEvents: number
    criticalEvents: number
    highSeverityEvents: number
    recentTamperAttempts: number
    healthScore: number
  } {
    const oneHourAgo = Date.now() - (60 * 60 * 1000)
    const recentEvents = this.events.filter(event => event.timestamp > oneHourAgo)
    
    const criticalEvents = this.events.filter(event => event.severity === 'critical').length
    const highSeverityEvents = this.events.filter(event => event.severity === 'high').length
    const recentTamperAttempts = recentEvents.filter(event => event.type === 'tamper').length

    // Calculate health score (0-100)
    let healthScore = 100
    if (criticalEvents > 0) healthScore -= 50
    if (highSeverityEvents > 5) healthScore -= 30
    if (recentTamperAttempts > 0) healthScore -= 20

    return {
      totalEvents: this.events.length,
      criticalEvents,
      highSeverityEvents,
      recentTamperAttempts,
      healthScore: Math.max(0, healthScore)
    }
  }

  /**
   * Clear all events
   */
  clearEvents(): void {
    this.events = []
    this.checksums.clear()
    this.writeActivity.clear()
  }

  /**
   * Export security data
   */
  exportSecurityData(): object {
    return {
      events: this.events,
      summary: this.getSecuritySummary(),
      config: this.config,
      exportedAt: new Date().toISOString()
    }
  }
}

export const securityMonitor = SecurityMonitor.getInstance()

// Add to window for debugging
if (typeof window !== 'undefined') {
  (window as any).securityMonitor = securityMonitor
}
