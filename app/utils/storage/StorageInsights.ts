/**
 * Advanced Storage Insights
 * Historical usage tracking, growth trends, and key analytics
 */

import { storageAnalytics } from './StorageAnalytics'
import { debounce } from '../debounce'

export interface HistoricalMetrics {
  date: string
  reads: number
  writes: number
  hits: number
  misses: number
  errors: number
  fallbacks: number
  avgReadTime: number
  avgWriteTime: number
  memoryUsage: number
  localStorageUsage: number
  databaseUsage: number
}

export interface KeyUsageStats {
  key: string
  reads: number
  writes: number
  lastAccessed: number
  size: number
  storageType: string
  frequency: number
}

export interface GrowthTrend {
  period: 'daily' | 'weekly' | 'monthly'
  data: {
    date: string
    operations: number
    storage: number
    performance: number
  }[]
}

class StorageInsights {
  private static instance: StorageInsights
  private historicalData: Map<string, HistoricalMetrics> = new Map()
  private keyUsage: Map<string, KeyUsageStats> = new Map()
  private readonly STORAGE_KEY = '__storage_insights__'
  private readonly MAX_HISTORY_DAYS = 30
  private debouncedPersistToDatabase: (metrics: HistoricalMetrics) => void
  private lastDatabaseWrite: number = 0
  private readonly DATABASE_WRITE_THROTTLE = 300000 // 5 minutes minimum between database writes (further reduced frequency)

  public static getInstance(): StorageInsights {
    if (!StorageInsights.instance) {
      StorageInsights.instance = new StorageInsights()
    }
    return StorageInsights.instance
  }

  private constructor() {
    // Initialize debounced database persistence (60 second delay for maximum performance)
    this.debouncedPersistToDatabase = debounce((metrics: HistoricalMetrics) => {
      // Skip database persistence in development to prevent performance issues
      if (process.env.NODE_ENV === 'development') {
        console.log('⏸️ Skipping database persistence in development mode')
        return
      }

      // Use requestIdleCallback for non-blocking database writes
      if (typeof window !== 'undefined' && 'requestIdleCallback' in window) {
        window.requestIdleCallback(() => {
          this.persistToDatabaseInternal(metrics).catch(error => {
            console.warn('Failed to persist metrics to database:', error)
          })
        }, { timeout: 120000 }) // 2 minute timeout
      } else {
        // Fallback for environments without requestIdleCallback
        setTimeout(() => {
          this.persistToDatabaseInternal(metrics).catch(error => {
            console.warn('Failed to persist metrics to database:', error)
          })
        }, 5000) // 5 second delay
      }
    }, 60000) // Increased from 30s to 60s

    this.loadHistoricalData()
    this.startPeriodicCollection()
  }

  /**
   * Load historical data from localStorage
   */
  private loadHistoricalData(): void {
    try {
      if (typeof window !== 'undefined') {
        const stored = localStorage.getItem(this.STORAGE_KEY)
        if (stored) {
          const data = JSON.parse(stored)
          this.historicalData = new Map(data.historical || [])
          this.keyUsage = new Map(data.keyUsage || [])
        }
      }
    } catch (error) {
      console.error('Failed to load historical storage data:', error)
    }
  }

  /**
   * Save historical data to localStorage
   */
  private saveHistoricalData(): void {
    try {
      if (typeof window !== 'undefined') {
        const data = {
          historical: Array.from(this.historicalData.entries()),
          keyUsage: Array.from(this.keyUsage.entries()),
          lastUpdated: Date.now()
        }
        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(data))
      }
    } catch (error) {
      console.error('Failed to save historical storage data:', error)
    }
  }

  /**
   * Start periodic data collection
   */
  private startPeriodicCollection(): void {
    // Collect metrics every hour
    setInterval(() => {
      this.collectCurrentMetrics()
    }, 60 * 60 * 1000)

    // Also collect metrics every 15 minutes for more granular tracking
    setInterval(() => {
      this.collectCurrentMetrics()
    }, 15 * 60 * 1000)

    // Initial collection
    setTimeout(() => {
      this.collectCurrentMetrics()
    }, 5000)
  }

  /**
   * Collect current metrics and store them
   */
  private collectCurrentMetrics(): void {
    const today = new Date().toISOString().split('T')[0]
    const stats = storageAnalytics.getStats()

    const metrics: HistoricalMetrics = {
      date: today,
      reads: stats.overall.totalOperations,
      writes: stats.memory.writes + stats.localStorage.writes + stats.database.writes,
      hits: stats.memory.hits + stats.localStorage.hits + stats.database.hits,
      misses: stats.memory.misses + stats.localStorage.misses + stats.database.misses,
      errors: stats.memory.errorCount + stats.localStorage.errorCount + stats.database.errorCount,
      fallbacks: stats.memory.fallbackCount + stats.localStorage.fallbackCount + stats.database.fallbackCount,
      avgReadTime: stats.overall.averageReadTime,
      avgWriteTime: stats.overall.averageWriteTime,
      memoryUsage: stats.memory.size,
      localStorageUsage: stats.localStorage.size,
      databaseUsage: stats.database.size
    }

    // Merge with existing data for today
    const existing = this.historicalData.get(today)
    if (existing) {
      metrics.reads = Math.max(metrics.reads, existing.reads)
      metrics.writes = Math.max(metrics.writes, existing.writes)
      metrics.hits = Math.max(metrics.hits, existing.hits)
      metrics.misses = Math.max(metrics.misses, existing.misses)
    }

    this.historicalData.set(today, metrics)
    this.cleanupOldData()
    this.saveHistoricalData()

    // Also persist to database for cross-device analytics (debounced and throttled)
    this.persistToDatabase(metrics)
  }

  /**
   * Track key usage
   */
  trackKeyUsage(key: string, operation: 'read' | 'write', storageType: string, size: number = 0): void {
    const existing = this.keyUsage.get(key) || {
      key,
      reads: 0,
      writes: 0,
      lastAccessed: 0,
      size: 0,
      storageType,
      frequency: 0
    }

    if (operation === 'read') {
      existing.reads++
    } else {
      existing.writes++
      existing.size = size
    }

    existing.lastAccessed = Date.now()
    existing.frequency = existing.reads + existing.writes
    existing.storageType = storageType

    this.keyUsage.set(key, existing)
  }

  /**
   * Remove old historical data
   */
  private cleanupOldData(): void {
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - this.MAX_HISTORY_DAYS)
    const cutoffString = cutoffDate.toISOString().split('T')[0]

    for (const [date] of this.historicalData) {
      if (date < cutoffString) {
        this.historicalData.delete(date)
      }
    }
  }

  /**
   * Get historical metrics for a date range
   */
  getHistoricalMetrics(days: number = 7): HistoricalMetrics[] {
    const result: HistoricalMetrics[] = []
    const today = new Date()

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(today)
      date.setDate(date.getDate() - i)
      const dateString = date.toISOString().split('T')[0]
      
      const metrics = this.historicalData.get(dateString) || {
        date: dateString,
        reads: 0,
        writes: 0,
        hits: 0,
        misses: 0,
        errors: 0,
        fallbacks: 0,
        avgReadTime: 0,
        avgWriteTime: 0,
        memoryUsage: 0,
        localStorageUsage: 0,
        databaseUsage: 0
      }

      result.push(metrics)
    }

    return result
  }

  /**
   * Get top keys by usage
   */
  getTopKeysByUsage(limit: number = 10): KeyUsageStats[] {
    return Array.from(this.keyUsage.values())
      .sort((a, b) => b.frequency - a.frequency)
      .slice(0, limit)
  }

  /**
   * Get growth trends
   */
  getGrowthTrends(period: 'daily' | 'weekly' | 'monthly' = 'daily'): GrowthTrend {
    const data = this.getHistoricalMetrics(period === 'daily' ? 7 : period === 'weekly' ? 28 : 90)
    
    return {
      period,
      data: data.map(metrics => ({
        date: metrics.date,
        operations: metrics.reads + metrics.writes,
        storage: metrics.memoryUsage + metrics.localStorageUsage + metrics.databaseUsage,
        performance: (metrics.avgReadTime + metrics.avgWriteTime) / 2
      }))
    }
  }

  /**
   * Get storage health score (0-100)
   */
  getHealthScore(): number {
    const stats = storageAnalytics.getStats()
    let score = 100

    // Deduct points for high error rate
    if (stats.overall.errorRate > 5) score -= 20
    else if (stats.overall.errorRate > 1) score -= 10

    // Deduct points for high fallback rate
    if (stats.overall.fallbackRate > 20) score -= 15
    else if (stats.overall.fallbackRate > 10) score -= 8

    // Deduct points for slow performance
    if (stats.overall.averageReadTime > 50) score -= 15
    if (stats.overall.averageWriteTime > 200) score -= 15

    // Deduct points for low hit rate
    if (stats.overall.hitRate < 70) score -= 20
    else if (stats.overall.hitRate < 85) score -= 10

    return Math.max(0, score)
  }

  /**
   * Get insights summary
   */
  getInsightsSummary() {
    const historical = this.getHistoricalMetrics(7)
    const topKeys = this.getTopKeysByUsage(5)
    const trends = this.getGrowthTrends('daily')
    const healthScore = this.getHealthScore()

    const totalOperations = historical.reduce((sum, day) => sum + day.reads + day.writes, 0)
    const avgPerformance = historical.reduce((sum, day) => sum + (day.avgReadTime + day.avgWriteTime) / 2, 0) / historical.length

    return {
      healthScore,
      totalOperations,
      avgPerformance: avgPerformance || 0,
      topKeys,
      trends,
      recommendations: this.generateRecommendations(healthScore, trends, topKeys)
    }
  }

  /**
   * Generate performance recommendations
   */
  private generateRecommendations(healthScore: number, trends: GrowthTrend, topKeys: KeyUsageStats[]): string[] {
    const recommendations: string[] = []

    if (healthScore < 70) {
      recommendations.push('Storage health is below optimal. Consider investigating error rates and performance.')
    }

    if (trends.data.length > 1) {
      const latestPerf = trends.data[trends.data.length - 1].performance
      const avgPerf = trends.data.reduce((sum, d) => sum + d.performance, 0) / trends.data.length
      
      if (latestPerf > avgPerf * 1.5) {
        recommendations.push('Performance has degraded recently. Check for network issues or increased load.')
      }
    }

    const heavyKeys = topKeys.filter(key => key.size > 10000) // 10KB+
    if (heavyKeys.length > 0) {
      recommendations.push(`Consider optimizing large keys: ${heavyKeys.map(k => k.key).join(', ')}`)
    }

    if (topKeys.some(key => key.storageType === 'database' && key.frequency > 100)) {
      recommendations.push('High database usage detected. Consider caching frequently accessed data.')
    }

    return recommendations
  }

  /**
   * Export insights data for external analysis
   */
  exportInsights(): object {
    return {
      historical: Array.from(this.historicalData.entries()),
      keyUsage: Array.from(this.keyUsage.entries()),
      summary: this.getInsightsSummary(),
      exportedAt: new Date().toISOString()
    }
  }

  /**
   * Persist metrics to database for cross-device analytics (throttled and debounced)
   */
  private persistToDatabase(metrics: HistoricalMetrics): void {
    const now = Date.now()

    // Throttle database writes to prevent excessive I/O
    if (now - this.lastDatabaseWrite < this.DATABASE_WRITE_THROTTLE) {
      // Skip this write if we've written recently
      return
    }

    // Use debounced version for actual persistence
    this.debouncedPersistToDatabase(metrics)

    this.lastDatabaseWrite = now
  }

  /**
   * Internal method for actual database persistence (ultra-optimized)
   */
  private async persistToDatabaseInternal(metrics: HistoricalMetrics): Promise<void> {
    try {
      // Skip database writes if tab is not visible (performance optimization)
      if (typeof document !== 'undefined' && document.hidden) {
        console.log('⏸️ Skipping database write - tab not visible')
        return
      }

      // Skip in development mode to prevent performance issues
      if (process.env.NODE_ENV === 'development') {
        console.log('⏸️ Skipping database write - development mode')
        return
      }

      const { getStorageManager } = await import('./StorageManagerFactory')
      const dbManager = getStorageManager('database')

      const key = `storage_metrics_${metrics.date}`

      // Use very short timeout for development performance
      const controller = new AbortController()
      const timeoutId = setTimeout(() => {
        controller.abort()
      }, 2000) // Reduced from 5s to 2s

      try {
        // Create minimal metrics for fastest writes
        const minimalMetrics = {
          date: metrics.date,
          reads: metrics.reads,
          writes: metrics.writes,
          timestamp: Date.now()
          // Removed errors and other heavy data
        }

        await dbManager.setJSON(key, minimalMetrics)
        clearTimeout(timeoutId)
        console.log(`✅ Persisted minimal metrics for ${metrics.date}`)
      } catch (error) {
        clearTimeout(timeoutId)
        if (error.name === 'AbortError') {
          console.log('⏸️ Database write aborted due to timeout (expected)')
        } else {
          throw error
        }
      }
    } catch (error) {
      // Silently fail to prevent blocking UI
      console.log('⏸️ Metrics persistence skipped:', error.message)
    }
  }

  /**
   * Load historical metrics from database
   */
  async loadFromDatabase(days: number = 30): Promise<HistoricalMetrics[]> {
    try {
      const { getStorageManager } = await import('./StorageManagerFactory')
      const dbManager = getStorageManager('database')

      const metrics: HistoricalMetrics[] = []
      const today = new Date()

      for (let i = days - 1; i >= 0; i--) {
        const date = new Date(today)
        date.setDate(date.getDate() - i)
        const dateString = date.toISOString().split('T')[0]
        const key = `storage_metrics_${dateString}`

        const data = await dbManager.getJSON<HistoricalMetrics>(key)
        if (data) {
          metrics.push(data)
          // Also update local cache
          this.historicalData.set(dateString, data)
        }
      }

      return metrics
    } catch (error) {
      console.warn('Failed to load metrics from database:', error)
      return []
    }
  }

  /**
   * Reset all insights data
   */
  reset(): void {
    this.historicalData.clear()
    this.keyUsage.clear()
    this.saveHistoricalData()
  }
}

export const storageInsights = StorageInsights.getInstance()

// Add to window for debugging
if (typeof window !== 'undefined') {
  (window as any).storageInsights = storageInsights
}
