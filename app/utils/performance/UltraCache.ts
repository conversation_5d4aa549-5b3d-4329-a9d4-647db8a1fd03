/**
 * Ultra-optimized caching system for BBM Web App
 * Provides intelligent caching with automatic cleanup and performance monitoring
 */

interface CacheItem<T = any> {
  data: T
  timestamp: number
  ttl: number
  accessCount: number
  lastAccessed: number
  size: number
}

interface CacheStats {
  totalItems: number
  totalSize: number
  hitRate: number
  missRate: number
  evictions: number
}

class UltraCache {
  private static instance: UltraCache
  private cache = new Map<string, CacheItem>()
  private maxSize = 50 * 1024 * 1024 // 50MB max cache size
  private maxItems = 1000
  private hits = 0
  private misses = 0
  private evictions = 0
  private cleanupInterval: NodeJS.Timeout | null = null

  static getInstance(): UltraCache {
    if (!UltraCache.instance) {
      UltraCache.instance = new UltraCache()
    }
    return UltraCache.instance
  }

  private constructor() {
    this.startCleanup()
    console.log('⚡ UltraCache: Intelligent caching system initialized')
  }

  /**
   * Set cache item with automatic size calculation
   */
  set<T>(key: string, data: T, ttl = 30000): void {
    const size = this.calculateSize(data)
    const now = Date.now()

    // Check if we need to make space
    this.ensureSpace(size)

    this.cache.set(key, {
      data,
      timestamp: now,
      ttl,
      accessCount: 0,
      lastAccessed: now,
      size
    })
  }

  /**
   * Get cache item with access tracking
   */
  get<T>(key: string): T | null {
    const item = this.cache.get(key)
    
    if (!item) {
      this.misses++
      return null
    }

    const now = Date.now()
    
    // Check if expired
    if (now - item.timestamp > item.ttl) {
      this.cache.delete(key)
      this.misses++
      return null
    }

    // Update access statistics
    item.accessCount++
    item.lastAccessed = now
    this.hits++

    return item.data as T
  }

  /**
   * Batch get multiple keys
   */
  batchGet<T>(keys: string[]): Array<{ key: string; data: T | null }> {
    return keys.map(key => ({
      key,
      data: this.get<T>(key)
    }))
  }

  /**
   * Batch set multiple items
   */
  batchSet<T>(items: Array<{ key: string; data: T; ttl?: number }>): void {
    items.forEach(({ key, data, ttl }) => {
      this.set(key, data, ttl)
    })
  }

  /**
   * Check if key exists and is valid
   */
  has(key: string): boolean {
    const item = this.cache.get(key)
    if (!item) return false

    const now = Date.now()
    if (now - item.timestamp > item.ttl) {
      this.cache.delete(key)
      return false
    }

    return true
  }

  /**
   * Delete cache item
   */
  delete(key: string): boolean {
    return this.cache.delete(key)
  }

  /**
   * Clear all cache
   */
  clear(): void {
    this.cache.clear()
    this.hits = 0
    this.misses = 0
    this.evictions = 0
    console.log('🧹 UltraCache: Cache cleared')
  }

  /**
   * Get or set with factory function
   */
  async getOrSet<T>(
    key: string,
    factory: () => Promise<T> | T,
    ttl = 30000
  ): Promise<T> {
    const cached = this.get<T>(key)
    if (cached !== null) {
      return cached
    }

    const data = await factory()
    this.set(key, data, ttl)
    return data
  }

  /**
   * Calculate approximate size of data
   */
  private calculateSize(data: any): number {
    try {
      return new Blob([JSON.stringify(data)]).size
    } catch {
      // Fallback for non-serializable data
      return 1024 // 1KB estimate
    }
  }

  /**
   * Ensure we have space for new item
   */
  private ensureSpace(requiredSize: number): void {
    const currentSize = this.getTotalSize()
    
    // Check item count limit
    if (this.cache.size >= this.maxItems) {
      this.evictLeastUsed(Math.floor(this.maxItems * 0.1)) // Remove 10%
    }

    // Check size limit
    if (currentSize + requiredSize > this.maxSize) {
      const targetSize = this.maxSize * 0.8 // Target 80% of max size
      this.evictToSize(targetSize)
    }
  }

  /**
   * Evict least recently used items
   */
  private evictLeastUsed(count: number): void {
    const items = Array.from(this.cache.entries())
      .sort((a, b) => {
        // Sort by access count and last accessed time
        const scoreA = a[1].accessCount * 0.7 + (Date.now() - a[1].lastAccessed) * 0.3
        const scoreB = b[1].accessCount * 0.7 + (Date.now() - b[1].lastAccessed) * 0.3
        return scoreA - scoreB
      })

    for (let i = 0; i < Math.min(count, items.length); i++) {
      this.cache.delete(items[i][0])
      this.evictions++
    }
  }

  /**
   * Evict items until target size is reached
   */
  private evictToSize(targetSize: number): void {
    const items = Array.from(this.cache.entries())
      .sort((a, b) => a[1].lastAccessed - b[1].lastAccessed) // Oldest first

    let currentSize = this.getTotalSize()
    
    for (const [key] of items) {
      if (currentSize <= targetSize) break
      
      const item = this.cache.get(key)
      if (item) {
        currentSize -= item.size
        this.cache.delete(key)
        this.evictions++
      }
    }
  }

  /**
   * Get total cache size
   */
  private getTotalSize(): number {
    let total = 0
    for (const item of this.cache.values()) {
      total += item.size
    }
    return total
  }

  /**
   * Start automatic cleanup
   */
  private startCleanup(): void {
    this.cleanupInterval = setInterval(() => {
      this.cleanup()
    }, 60000) // Cleanup every minute
  }

  /**
   * Clean up expired items
   */
  private cleanup(): void {
    const now = Date.now()
    let cleaned = 0

    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > item.ttl) {
        this.cache.delete(key)
        cleaned++
      }
    }

    if (cleaned > 0) {
      console.log(`🧹 UltraCache: Cleaned ${cleaned} expired items`)
    }
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    const totalRequests = this.hits + this.misses
    
    return {
      totalItems: this.cache.size,
      totalSize: this.getTotalSize(),
      hitRate: totalRequests > 0 ? (this.hits / totalRequests) * 100 : 0,
      missRate: totalRequests > 0 ? (this.misses / totalRequests) * 100 : 0,
      evictions: this.evictions
    }
  }

  /**
   * Log cache statistics
   */
  logStats(): void {
    const stats = this.getStats()
    console.log('📊 UltraCache Stats:')
    console.log(`  Items: ${stats.totalItems}`)
    console.log(`  Size: ${(stats.totalSize / 1024 / 1024).toFixed(2)} MB`)
    console.log(`  Hit Rate: ${stats.hitRate.toFixed(1)}%`)
    console.log(`  Miss Rate: ${stats.missRate.toFixed(1)}%`)
    console.log(`  Evictions: ${stats.evictions}`)
  }

  /**
   * Destroy cache and cleanup
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
      this.cleanupInterval = null
    }
    this.clear()
    console.log('💥 UltraCache: Destroyed')
  }
}

// Export singleton instance
export const ultraCache = UltraCache.getInstance()

// React hook for using UltraCache
export function useUltraCache() {
  const React = require('react')
  
  const cachedValue = React.useCallback(<T>(
    key: string,
    factory: () => T,
    ttl = 30000
  ): T => {
    const cached = ultraCache.get<T>(key)
    if (cached !== null) {
      return cached
    }

    const value = factory()
    ultraCache.set(key, value, ttl)
    return value
  }, [])

  const asyncCachedValue = React.useCallback(async <T>(
    key: string,
    factory: () => Promise<T>,
    ttl = 30000
  ): Promise<T> => {
    return ultraCache.getOrSet(key, factory, ttl)
  }, [])

  return {
    get: ultraCache.get.bind(ultraCache),
    set: ultraCache.set.bind(ultraCache),
    has: ultraCache.has.bind(ultraCache),
    delete: ultraCache.delete.bind(ultraCache),
    clear: ultraCache.clear.bind(ultraCache),
    cachedValue,
    asyncCachedValue,
    getStats: ultraCache.getStats.bind(ultraCache),
    logStats: ultraCache.logStats.bind(ultraCache)
  }
}

// Global access for debugging
if (typeof window !== 'undefined') {
  (window as any).ultraCache = ultraCache
}
