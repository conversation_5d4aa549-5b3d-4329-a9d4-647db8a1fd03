/**
 * Ultra-optimized lazy loading system for BBM Web App components
 * Provides intelligent code splitting with performance monitoring
 */

import React, { Suspense, lazy, ComponentType } from 'react'
import { ultraCache } from './UltraCache'

// Loading fallback component
const LoadingFallback: React.FC<{ component?: string }> = ({ component }) => (
  <div className="flex items-center justify-center p-8">
    <div className="flex flex-col items-center space-y-2">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      <p className="text-sm text-gray-600">
        {component ? `Loading ${component}...` : 'Loading...'}
      </p>
    </div>
  </div>
)

// Error fallback component
const ErrorFallback: React.FC<{ error: Error; component?: string }> = ({ error, component }) => (
  <div className="flex items-center justify-center p-8">
    <div className="text-center">
      <div className="text-red-500 mb-2">⚠️</div>
      <p className="text-sm text-gray-600">
        Failed to load {component || 'component'}
      </p>
      <p className="text-xs text-gray-400 mt-1">
        {error.message}
      </p>
    </div>
  </div>
)

/**
 * Create a lazy component with intelligent loading
 */
function createLazyComponent<T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  componentName: string,
  fallback?: ComponentType
): ComponentType<React.ComponentProps<T>> {
  const LazyComponent = lazy(async () => {
    const startTime = performance.now()
    
    try {
      // Check cache first
      const cacheKey = `lazy-component-${componentName}`
      const cached = ultraCache.get<{ default: T }>(cacheKey)
      
      if (cached) {
        console.log(`⚡ LazyComponents: ${componentName} loaded from cache`)
        return cached
      }

      // Load component
      const result = await importFn()
      
      // Cache the result
      ultraCache.set(cacheKey, result, 300000) // 5 minutes cache
      
      const loadTime = performance.now() - startTime
      console.log(`📦 LazyComponents: ${componentName} loaded in ${loadTime.toFixed(2)}ms`)
      
      return result
    } catch (error) {
      console.error(`❌ LazyComponents: Failed to load ${componentName}:`, error)
      throw error
    }
  })

  // Return wrapped component with error boundary
  return React.memo((props: React.ComponentProps<T>) => (
    <Suspense fallback={fallback ? React.createElement(fallback) : <LoadingFallback component={componentName} />}>
      <LazyComponent {...props} />
    </Suspense>
  ))
}

/**
 * Preload a component for better performance
 */
async function preloadComponent(
  importFn: () => Promise<{ default: ComponentType<any> }>,
  componentName: string
): Promise<void> {
  try {
    const cacheKey = `lazy-component-${componentName}`
    
    // Check if already cached
    if (ultraCache.has(cacheKey)) {
      console.log(`⚡ LazyComponents: ${componentName} already preloaded`)
      return
    }

    console.log(`🔄 LazyComponents: Preloading ${componentName}...`)
    const result = await importFn()
    ultraCache.set(cacheKey, result, 300000) // 5 minutes cache
    console.log(`✅ LazyComponents: ${componentName} preloaded`)
  } catch (error) {
    console.warn(`⚠️ LazyComponents: Failed to preload ${componentName}:`, error)
  }
}

// Lazy load heavy components
export const LazyAnalytics = createLazyComponent(
  () => import('../../components/Analytics'),
  'Analytics'
)

export const LazySettings = createLazyComponent(
  () => import('../../components/Settings'),
  'Settings'
)

export const LazyBarMenu = createLazyComponent(
  () => import('../../components/BarMenu'),
  'BarMenu'
)

export const LazyReceiptHistory = createLazyComponent(
  () => import('../../components/ReceiptHistory'),
  'ReceiptHistory'
)

export const LazyUserManagement = createLazyComponent(
  () => import('../../components/UserManagement'),
  'UserManagement'
)

export const LazyBarSettings = createLazyComponent(
  () => import('../../components/BarSettings'),
  'BarSettings'
)

export const LazyPermissions = createLazyComponent(
  () => import('../../components/Permissions'),
  'Permissions'
)

export const LazyStorageConfigPanel = createLazyComponent(
  () => import('../../components/StorageConfigPanel'),
  'StorageConfigPanel'
)

export const LazyAdminStorageDashboard = createLazyComponent(
  () => import('../../components/AdminStorageDashboard'),
  'AdminStorageDashboard'
)

/**
 * Preload critical components based on user role
 */
export function preloadCriticalComponents(userRole: 'admin' | 'waiter'): void {
  console.log(`🚀 LazyComponents: Preloading components for ${userRole}...`)
  
  // Always preload these
  const commonComponents = [
    { fn: () => import('../../components/BarMenu'), name: 'BarMenu' },
    { fn: () => import('../../components/Analytics'), name: 'Analytics' },
  ]

  // Admin-specific components
  const adminComponents = [
    { fn: () => import('../../components/Settings'), name: 'Settings' },
    { fn: () => import('../../components/UserManagement'), name: 'UserManagement' },
    { fn: () => import('../../components/Permissions'), name: 'Permissions' },
  ]

  const componentsToPreload = userRole === 'admin' 
    ? [...commonComponents, ...adminComponents]
    : commonComponents

  // Preload with staggered timing to avoid blocking
  componentsToPreload.forEach(({ fn, name }, index) => {
    setTimeout(() => {
      preloadComponent(fn, name)
    }, index * 100) // 100ms delay between each
  })
}

/**
 * Preload components on user interaction
 */
export function preloadOnHover(componentName: string): void {
  const preloadMap: Record<string, () => Promise<{ default: ComponentType<any> }>> = {
    'Analytics': () => import('../../components/Analytics'),
    'Settings': () => import('../../components/Settings'),
    'BarMenu': () => import('../../components/BarMenu'),
    'ReceiptHistory': () => import('../../components/ReceiptHistory'),
    'UserManagement': () => import('../../components/UserManagement'),
  }

  const importFn = preloadMap[componentName]
  if (importFn) {
    preloadComponent(importFn, componentName)
  }
}

/**
 * React hook for lazy loading with preloading
 */
export function useLazyComponent<T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  componentName: string,
  preload = false
) {
  const [isPreloaded, setIsPreloaded] = React.useState(false)

  React.useEffect(() => {
    if (preload && !isPreloaded) {
      preloadComponent(importFn, componentName).then(() => {
        setIsPreloaded(true)
      })
    }
  }, [preload, isPreloaded, importFn, componentName])

  const LazyComponent = React.useMemo(
    () => createLazyComponent(importFn, componentName),
    [importFn, componentName]
  )

  return {
    Component: LazyComponent,
    isPreloaded,
    preload: () => preloadComponent(importFn, componentName)
  }
}

/**
 * Higher-order component for adding lazy loading to any component
 */
export function withLazyLoading<P extends object>(
  importFn: () => Promise<{ default: ComponentType<P> }>,
  componentName: string,
  fallback?: ComponentType
) {
  return createLazyComponent(importFn, componentName, fallback)
}

/**
 * Get lazy loading statistics
 */
export function getLazyLoadingStats(): {
  cachedComponents: number
  totalPreloaded: number
} {
  const stats = ultraCache.getStats()
  const cachedComponents = Array.from(ultraCache['cache'].keys())
    .filter(key => key.startsWith('lazy-component-')).length

  return {
    cachedComponents,
    totalPreloaded: cachedComponents
  }
}

// Global access for debugging
if (typeof window !== 'undefined') {
  (window as any).lazyComponents = {
    preloadCriticalComponents,
    preloadOnHover,
    getLazyLoadingStats,
    preloadComponent
  }
}
