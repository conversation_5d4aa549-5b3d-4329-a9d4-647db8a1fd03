/**
 * Smart Performance Mode
 * Automatically disables heavy operations in development for better performance
 */

class SmartPerformanceMode {
  private static instance: SmartPerformanceMode
  private isDevelopment: boolean
  private isEnabled: boolean

  private constructor() {
    this.isDevelopment = process.env.NODE_ENV === 'development'
    this.isEnabled = true
    
    if (this.isDevelopment) {
      console.log('🚀 Smart Performance Mode: Development optimizations enabled')
      this.applyDevelopmentOptimizations()
    }
  }

  public static getInstance(): SmartPerformanceMode {
    if (!SmartPerformanceMode.instance) {
      SmartPerformanceMode.instance = new SmartPerformanceMode()
    }
    return SmartPerformanceMode.instance
  }

  /**
   * Apply development-specific optimizations
   */
  private applyDevelopmentOptimizations(): void {
    // Disable heavy database operations
    this.disableHeavyDatabaseOps()
    
    // Optimize timers
    this.optimizeTimers()
    
    // Reduce analytics overhead
    this.reduceAnalyticsOverhead()
  }

  /**
   * Check if operation should be skipped for performance
   */
  shouldSkipOperation(operationType: 'database-write' | 'analytics' | 'metrics' | 'heavy-computation'): boolean {
    if (!this.isDevelopment) return false

    switch (operationType) {
      case 'database-write':
        return true // Skip all database writes in development
      case 'analytics':
        return true // Skip analytics in development
      case 'metrics':
        return true // Skip metrics persistence in development
      case 'heavy-computation':
        return true // Skip heavy computations in development
      default:
        return false
    }
  }

  /**
   * Get optimized interval for timers
   */
  getOptimizedInterval(originalInterval: number, purpose: 'timer' | 'polling' | 'sync'): number {
    if (!this.isDevelopment) return originalInterval

    switch (purpose) {
      case 'timer':
        return Math.max(originalInterval * 2, 5000) // At least 5 seconds
      case 'polling':
        return Math.max(originalInterval * 3, 10000) // At least 10 seconds
      case 'sync':
        return Math.max(originalInterval * 5, 30000) // At least 30 seconds
      default:
        return originalInterval
    }
  }

  /**
   * Disable heavy database operations
   */
  private disableHeavyDatabaseOps(): void {
    // Override database write methods to be no-ops in development
    if (typeof window !== 'undefined') {
      (window as any).__SKIP_DB_WRITES__ = true
    }
  }

  /**
   * Optimize timers for development
   */
  private optimizeTimers(): void {
    // Increase all timer intervals in development
    if (typeof window !== 'undefined') {
      (window as any).__DEV_TIMER_MULTIPLIER__ = 3
    }
  }

  /**
   * Reduce analytics overhead
   */
  private reduceAnalyticsOverhead(): void {
    // Disable analytics collection in development
    if (typeof window !== 'undefined') {
      (window as any).__DISABLE_ANALYTICS__ = true
    }
  }

  /**
   * Smart debounce that adjusts delay based on environment
   */
  smartDebounce<T extends (...args: any[]) => any>(
    func: T,
    delay: number,
    options: {
      maxDelay?: number
      immediate?: boolean
      skipInDev?: boolean
    } = {}
  ): (...args: Parameters<T>) => void {
    const { maxDelay = delay * 5, immediate = false, skipInDev = false } = options

    // Skip entirely in development if requested
    if (this.isDevelopment && skipInDev) {
      return () => {
        console.log('⏸️ Skipped operation in development mode')
      }
    }

    // Increase delay in development
    const adjustedDelay = this.isDevelopment ? Math.min(delay * 3, maxDelay) : delay

    let timeoutId: NodeJS.Timeout | null = null

    return (...args: Parameters<T>) => {
      if (timeoutId) {
        clearTimeout(timeoutId)
      }

      if (immediate && !timeoutId) {
        func(...args)
      }

      timeoutId = setTimeout(() => {
        if (!immediate) {
          func(...args)
        }
        timeoutId = null
      }, adjustedDelay)
    }
  }

  /**
   * Smart throttle that adjusts frequency based on environment
   */
  smartThrottle<T extends (...args: any[]) => any>(
    func: T,
    limit: number,
    skipInDev: boolean = false
  ): (...args: Parameters<T>) => void {
    // Skip entirely in development if requested
    if (this.isDevelopment && skipInDev) {
      return () => {
        console.log('⏸️ Skipped throttled operation in development mode')
      }
    }

    // Increase limit in development
    const adjustedLimit = this.isDevelopment ? limit * 2 : limit

    let inThrottle = false

    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func(...args)
        inThrottle = true
        setTimeout(() => {
          inThrottle = false
        }, adjustedLimit)
      }
    }
  }

  /**
   * Check if we're in development mode
   */
  isDev(): boolean {
    return this.isDevelopment
  }

  /**
   * Get performance recommendations
   */
  getPerformanceRecommendations(): string[] {
    const recommendations: string[] = []

    if (this.isDevelopment) {
      recommendations.push('🚀 Development mode optimizations active')
      recommendations.push('⏸️ Database writes disabled for performance')
      recommendations.push('📊 Analytics collection disabled')
      recommendations.push('⏱️ Timer intervals increased')
      recommendations.push('🔧 Heavy operations skipped')
    } else {
      recommendations.push('🏭 Production mode - all features enabled')
      recommendations.push('💾 Database persistence active')
      recommendations.push('📊 Analytics collection active')
      recommendations.push('⏱️ Normal timer intervals')
    }

    return recommendations
  }

  /**
   * Log current performance mode
   */
  logPerformanceMode(): void {
    console.group('🚀 Smart Performance Mode')
    console.log('Environment:', this.isDevelopment ? 'Development' : 'Production')
    console.log('Optimizations:', this.isDevelopment ? 'Enabled' : 'Disabled')
    
    const recommendations = this.getPerformanceRecommendations()
    recommendations.forEach(rec => console.log(rec))
    
    console.groupEnd()
  }
}

// Export singleton instance
export const smartPerformanceMode = SmartPerformanceMode.getInstance()

// Auto-log performance mode on import
if (typeof window !== 'undefined') {
  setTimeout(() => {
    smartPerformanceMode.logPerformanceMode()
  }, 1000)
}

// Export utility functions
export const shouldSkipOperation = (type: Parameters<SmartPerformanceMode['shouldSkipOperation']>[0]) => 
  smartPerformanceMode.shouldSkipOperation(type)

export const getOptimizedInterval = (interval: number, purpose: Parameters<SmartPerformanceMode['getOptimizedInterval']>[1]) =>
  smartPerformanceMode.getOptimizedInterval(interval, purpose)

export const smartDebounce = <T extends (...args: any[]) => any>(
  func: T,
  delay: number,
  options?: Parameters<SmartPerformanceMode['smartDebounce']>[2]
) => smartPerformanceMode.smartDebounce(func, delay, options)

export const smartThrottle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number,
  skipInDev?: boolean
) => smartPerformanceMode.smartThrottle(func, limit, skipInDev)
