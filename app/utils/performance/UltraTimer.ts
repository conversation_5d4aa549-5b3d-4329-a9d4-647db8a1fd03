/**
 * Ultra-optimized unified timer system for BBM Web App
 * Replaces multiple timer systems with a single, efficient coordinator
 */

type TimerCallback = () => void | Promise<void>
type TimerPriority = 'critical' | 'high' | 'normal' | 'low'

interface TimerTask {
  id: string
  callback: TimerCallback
  interval: number
  priority: TimerPriority
  lastRun: number
  enabled: boolean
  component?: string
}

class UltraTimer {
  private static instance: UltraTimer
  private tasks = new Map<string, TimerTask>()
  private rafId: number | null = null
  private isRunning = false
  private lastFrame = 0
  private frameCount = 0
  private isDevelopment = process.env.NODE_ENV === 'development'

  // Performance thresholds
  private readonly MAX_FRAME_TIME = 16 // 60 FPS target
  private readonly BATCH_SIZE = 3 // Max tasks per frame
  private readonly DEV_MULTIPLIER = 10 // Slower intervals in development

  static getInstance(): UltraTimer {
    if (!UltraTimer.instance) {
      UltraTimer.instance = new UltraTimer()
    }
    return UltraTimer.instance
  }

  private constructor() {
    this.start()
    console.log('⚡ UltraTimer: Unified timer system initialized')
  }

  /**
   * Register a timer task
   */
  register(
    id: string,
    callback: TimerCallback,
    interval: number,
    priority: TimerPriority = 'normal',
    component?: string
  ): void {
    // Apply development mode multiplier for performance
    const adjustedInterval = this.isDevelopment ? interval * this.DEV_MULTIPLIER : interval

    this.tasks.set(id, {
      id,
      callback,
      interval: adjustedInterval,
      priority,
      lastRun: 0,
      enabled: true,
      component
    })

    console.log(`⏱️ UltraTimer: Registered ${id} (${adjustedInterval}ms, ${priority})`)
  }

  /**
   * Unregister a timer task
   */
  unregister(id: string): void {
    if (this.tasks.delete(id)) {
      console.log(`🗑️ UltraTimer: Unregistered ${id}`)
    }
  }

  /**
   * Enable/disable a timer task
   */
  setEnabled(id: string, enabled: boolean): void {
    const task = this.tasks.get(id)
    if (task) {
      task.enabled = enabled
      console.log(`${enabled ? '▶️' : '⏸️'} UltraTimer: ${id} ${enabled ? 'enabled' : 'disabled'}`)
    }
  }

  /**
   * Batch update multiple callbacks
   */
  batchUpdate(callbacks: Array<{ id: string; callback: TimerCallback }>): void {
    requestIdleCallback(() => {
      callbacks.forEach(({ callback }) => {
        try {
          callback()
        } catch (error) {
          console.warn('UltraTimer: Batch callback error:', error)
        }
      })
    })
  }

  /**
   * Main timer loop using requestAnimationFrame
   */
  private start(): void {
    if (this.isRunning) return

    this.isRunning = true
    this.tick()
  }

  private tick = (timestamp: number = performance.now()): void => {
    const deltaTime = timestamp - this.lastFrame
    this.lastFrame = timestamp
    this.frameCount++

    // Skip frame if we're running too fast
    if (deltaTime < this.MAX_FRAME_TIME) {
      this.rafId = requestAnimationFrame(this.tick)
      return
    }

    // Get tasks that need to run, sorted by priority
    const tasksToRun = this.getTasksToRun(timestamp)
    
    if (tasksToRun.length > 0) {
      this.executeTasks(tasksToRun, timestamp)
    }

    // Performance monitoring
    if (this.frameCount % 300 === 0) { // Every ~5 seconds at 60fps
      this.logPerformanceStats()
    }

    this.rafId = requestAnimationFrame(this.tick)
  }

  /**
   * Get tasks that need to run this frame
   */
  private getTasksToRun(timestamp: number): TimerTask[] {
    const tasks: TimerTask[] = []
    
    for (const task of this.tasks.values()) {
      if (!task.enabled) continue
      
      const timeSinceLastRun = timestamp - task.lastRun
      if (timeSinceLastRun >= task.interval) {
        tasks.push(task)
      }
    }

    // Sort by priority: critical > high > normal > low
    return tasks.sort((a, b) => {
      const priorityOrder = { critical: 0, high: 1, normal: 2, low: 3 }
      return priorityOrder[a.priority] - priorityOrder[b.priority]
    }).slice(0, this.BATCH_SIZE) // Limit tasks per frame
  }

  /**
   * Execute tasks with performance monitoring
   */
  private executeTasks(tasks: TimerTask[], timestamp: number): void {
    const startTime = performance.now()

    for (const task of tasks) {
      try {
        const result = task.callback()
        
        // Handle async callbacks
        if (result instanceof Promise) {
          result.catch(error => {
            console.warn(`UltraTimer: Async task ${task.id} failed:`, error)
          })
        }
        
        task.lastRun = timestamp
      } catch (error) {
        console.warn(`UltraTimer: Task ${task.id} failed:`, error)
        // Disable failing tasks to prevent spam
        task.enabled = false
      }
    }

    const executionTime = performance.now() - startTime
    if (executionTime > this.MAX_FRAME_TIME) {
      console.warn(`⚠️ UltraTimer: Frame budget exceeded: ${executionTime.toFixed(2)}ms`)
    }
  }

  /**
   * Stop the timer system
   */
  stop(): void {
    if (this.rafId) {
      cancelAnimationFrame(this.rafId)
      this.rafId = null
    }
    this.isRunning = false
    console.log('⏹️ UltraTimer: Stopped')
  }

  /**
   * Get performance statistics
   */
  getStats(): {
    activeTasks: number
    totalTasks: number
    frameCount: number
    averageFPS: number
  } {
    const activeTasks = Array.from(this.tasks.values()).filter(t => t.enabled).length
    const averageFPS = this.frameCount / ((performance.now() - this.lastFrame) / 1000)

    return {
      activeTasks,
      totalTasks: this.tasks.size,
      frameCount: this.frameCount,
      averageFPS: Math.round(averageFPS)
    }
  }

  /**
   * Log performance statistics
   */
  private logPerformanceStats(): void {
    const stats = this.getStats()
    console.log(`📊 UltraTimer Stats: ${stats.activeTasks}/${stats.totalTasks} tasks, ${stats.averageFPS} FPS`)
  }

  /**
   * Log current status
   */
  logStatus(): void {
    console.log('🔍 UltraTimer Status:')
    console.log(`  Running: ${this.isRunning}`)
    console.log(`  Tasks: ${this.tasks.size}`)
    console.log(`  Development mode: ${this.isDevelopment}`)
    
    for (const [id, task] of this.tasks) {
      console.log(`  - ${id}: ${task.enabled ? '✅' : '❌'} (${task.interval}ms, ${task.priority})`)
    }
  }

  /**
   * Emergency stop all timers
   */
  emergencyStop(): void {
    this.stop()
    this.tasks.clear()
    console.log('🚨 UltraTimer: Emergency stop - all timers cleared')
  }
}

// Export singleton instance
export const ultraTimer = UltraTimer.getInstance()

// React hook for using UltraTimer
export function useUltraTimer() {
  const React = require('react')
  
  const registerTimer = React.useCallback((
    id: string,
    callback: TimerCallback,
    interval: number,
    priority: TimerPriority = 'normal',
    component?: string
  ) => {
    ultraTimer.register(id, callback, interval, priority, component)
    
    return () => {
      ultraTimer.unregister(id)
    }
  }, [])

  const batchUpdate = React.useCallback((callbacks: Array<{ id: string; callback: TimerCallback }>) => {
    ultraTimer.batchUpdate(callbacks)
  }, [])

  return {
    registerTimer,
    batchUpdate,
    setEnabled: ultraTimer.setEnabled.bind(ultraTimer),
    getStats: ultraTimer.getStats.bind(ultraTimer),
    logStatus: ultraTimer.logStatus.bind(ultraTimer)
  }
}

// Global access for debugging
if (typeof window !== 'undefined') {
  (window as any).ultraTimer = ultraTimer
}
