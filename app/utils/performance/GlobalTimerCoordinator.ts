/**
 * Global Timer Coordinator
 * Prevents multiple timers from running simultaneously and causing performance issues
 */

interface TimerTask {
  id: string
  callback: () => void
  interval: number
  lastRun: number
  priority: 'high' | 'medium' | 'low'
  enabled: boolean
}

class GlobalTimerCoordinator {
  private static instance: GlobalTimerCoordinator
  private tasks: Map<string, TimerTask> = new Map()
  private masterTimer: NodeJS.Timeout | null = null
  private readonly MASTER_INTERVAL = 5000 // 5 seconds master timer
  private isRunning = false
  private isDevelopment = process.env.NODE_ENV === 'development'

  private constructor() {
    console.log('🎯 Global Timer Coordinator initialized')
    this.startMasterTimer()
  }

  public static getInstance(): GlobalTimerCoordinator {
    if (!GlobalTimerCoordinator.instance) {
      GlobalTimerCoordinator.instance = new GlobalTimerCoordinator()
    }
    return GlobalTimerCoordinator.instance
  }

  /**
   * Register a timer task
   */
  registerTask(
    id: string,
    callback: () => void,
    interval: number,
    priority: 'high' | 'medium' | 'low' = 'medium'
  ): void {
    // In development, increase intervals for better performance
    const adjustedInterval = this.isDevelopment ? Math.max(interval * 2, 10000) : interval

    this.tasks.set(id, {
      id,
      callback,
      interval: adjustedInterval,
      lastRun: 0,
      priority,
      enabled: true
    })

    console.log(`⏰ Registered timer task: ${id} (${adjustedInterval}ms, ${priority} priority)`)
    
    if (!this.isRunning) {
      this.startMasterTimer()
    }
  }

  /**
   * Unregister a timer task
   */
  unregisterTask(id: string): void {
    if (this.tasks.delete(id)) {
      console.log(`⏹️ Unregistered timer task: ${id}`)
    }

    // Stop master timer if no tasks
    if (this.tasks.size === 0) {
      this.stopMasterTimer()
    }
  }

  /**
   * Enable/disable a task
   */
  setTaskEnabled(id: string, enabled: boolean): void {
    const task = this.tasks.get(id)
    if (task) {
      task.enabled = enabled
      console.log(`${enabled ? '▶️' : '⏸️'} Task ${id} ${enabled ? 'enabled' : 'disabled'}`)
    }
  }

  /**
   * Start the master timer
   */
  private startMasterTimer(): void {
    if (this.masterTimer || this.isRunning) return

    this.isRunning = true
    console.log(`🚀 Starting master timer (${this.MASTER_INTERVAL}ms interval)`)

    this.masterTimer = setInterval(() => {
      // Use requestAnimationFrame for non-blocking execution
      requestAnimationFrame(() => {
        this.executeTasks()
      })
    }, this.MASTER_INTERVAL)

    // Handle tab visibility
    if (typeof document !== 'undefined') {
      document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this))
    }
  }

  /**
   * Stop the master timer
   */
  private stopMasterTimer(): void {
    if (this.masterTimer) {
      clearInterval(this.masterTimer)
      this.masterTimer = null
      this.isRunning = false
      console.log('⏹️ Stopped master timer')
    }
  }

  /**
   * Handle tab visibility changes
   */
  private handleVisibilityChange(): void {
    if (typeof document === 'undefined') return

    if (document.hidden) {
      console.log('⏸️ Tab hidden - pausing timer execution')
    } else {
      console.log('▶️ Tab visible - resuming timer execution')
    }
  }

  /**
   * Execute tasks based on their intervals and priorities
   */
  private executeTasks(): void {
    // Skip execution if tab is hidden (performance optimization)
    if (typeof document !== 'undefined' && document.hidden) {
      return
    }

    const now = Date.now()
    const tasksToRun: TimerTask[] = []

    // Collect tasks that need to run
    for (const task of this.tasks.values()) {
      if (!task.enabled) continue

      const timeSinceLastRun = now - task.lastRun
      if (timeSinceLastRun >= task.interval) {
        tasksToRun.push(task)
      }
    }

    if (tasksToRun.length === 0) return

    // Sort by priority (high -> medium -> low)
    tasksToRun.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 }
      return priorityOrder[b.priority] - priorityOrder[a.priority]
    })

    // In development, limit concurrent task execution to prevent performance issues
    const maxConcurrentTasks = this.isDevelopment ? 2 : tasksToRun.length

    console.log(`⚡ Executing ${Math.min(maxConcurrentTasks, tasksToRun.length)}/${tasksToRun.length} timer tasks`)

    // Execute tasks with performance monitoring
    for (let i = 0; i < Math.min(maxConcurrentTasks, tasksToRun.length); i++) {
      const task = tasksToRun[i]
      
      try {
        const startTime = performance.now()
        task.callback()
        task.lastRun = now
        
        const duration = performance.now() - startTime
        if (duration > 100) {
          console.warn(`🐌 Slow timer task: ${task.id} took ${duration.toFixed(2)}ms`)
        }
      } catch (error) {
        console.error(`❌ Timer task error (${task.id}):`, error)
      }
    }
  }

  /**
   * Get coordinator statistics
   */
  getStats() {
    const enabledTasks = Array.from(this.tasks.values()).filter(t => t.enabled)
    const tasksByPriority = {
      high: enabledTasks.filter(t => t.priority === 'high').length,
      medium: enabledTasks.filter(t => t.priority === 'medium').length,
      low: enabledTasks.filter(t => t.priority === 'low').length
    }

    return {
      totalTasks: this.tasks.size,
      enabledTasks: enabledTasks.length,
      tasksByPriority,
      isRunning: this.isRunning,
      masterInterval: this.MASTER_INTERVAL,
      isDevelopment: this.isDevelopment
    }
  }

  /**
   * Log coordinator status
   */
  logStatus(): void {
    const stats = this.getStats()
    console.group('🎯 Global Timer Coordinator Status')
    console.log('Running:', stats.isRunning)
    console.log('Total tasks:', stats.totalTasks)
    console.log('Enabled tasks:', stats.enabledTasks)
    console.log('By priority:', stats.tasksByPriority)
    console.log('Master interval:', stats.masterInterval + 'ms')
    console.log('Development mode:', stats.isDevelopment)
    console.groupEnd()
  }

  /**
   * Cleanup on app shutdown
   */
  cleanup(): void {
    this.stopMasterTimer()
    this.tasks.clear()
    
    if (typeof document !== 'undefined') {
      document.removeEventListener('visibilitychange', this.handleVisibilityChange.bind(this))
    }
    
    console.log('🧹 Global Timer Coordinator cleaned up')
  }
}

// Export singleton instance
export const globalTimerCoordinator = GlobalTimerCoordinator.getInstance()

/**
 * React hook for coordinated timers
 */
export function useCoordinatedTimer(
  id: string,
  callback: () => void,
  interval: number,
  priority: 'high' | 'medium' | 'low' = 'medium',
  enabled: boolean = true
) {
  React.useEffect(() => {
    globalTimerCoordinator.registerTask(id, callback, interval, priority)
    globalTimerCoordinator.setTaskEnabled(id, enabled)

    return () => {
      globalTimerCoordinator.unregisterTask(id)
    }
  }, [id, callback, interval, priority])

  React.useEffect(() => {
    globalTimerCoordinator.setTaskEnabled(id, enabled)
  }, [id, enabled])

  return {
    enable: () => globalTimerCoordinator.setTaskEnabled(id, true),
    disable: () => globalTimerCoordinator.setTaskEnabled(id, false),
    unregister: () => globalTimerCoordinator.unregisterTask(id)
  }
}

// Auto-log status on import
if (typeof window !== 'undefined') {
  setTimeout(() => {
    globalTimerCoordinator.logStatus()
  }, 2000)
}

// Add React import
import React from 'react'
