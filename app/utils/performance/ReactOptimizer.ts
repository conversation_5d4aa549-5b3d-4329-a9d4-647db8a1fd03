import React, { useCallback, useMemo, useRef, useEffect } from 'react'
import { performanceMonitor } from './PerformanceMonitor'

/**
 * React Performance Optimizer
 * Collection of utilities to optimize React component performance
 */

/**
 * Optimized useCallback that only recreates when dependencies actually change
 */
export function useStableCallback<T extends (...args: any[]) => any>(
  callback: T,
  deps: React.DependencyList
): T {
  const callbackRef = useRef<T>(callback)
  const depsRef = useRef<React.DependencyList>(deps)

  // Only update if dependencies have actually changed
  const hasChanged = useMemo(() => {
    if (depsRef.current.length !== deps.length) return true
    return deps.some((dep, index) => dep !== depsRef.current[index])
  }, deps)

  if (hasChanged) {
    callbackRef.current = callback
    depsRef.current = deps
  }

  return callbackRef.current
}

/**
 * Optimized useMemo that prevents unnecessary recalculations
 */
export function useStableMemo<T>(
  factory: () => T,
  deps: React.DependencyList
): T {
  const valueRef = useRef<T>()
  const depsRef = useRef<React.DependencyList>(deps)

  const hasChanged = useMemo(() => {
    if (!depsRef.current || depsRef.current.length !== deps.length) return true
    return deps.some((dep, index) => dep !== depsRef.current[index])
  }, deps)

  if (hasChanged || valueRef.current === undefined) {
    valueRef.current = factory()
    depsRef.current = deps
  }

  return valueRef.current
}

/**
 * Hook for batching state updates to prevent excessive re-renders
 */
export function useBatchedState<T>(
  initialState: T,
  batchDelay: number = 16 // One frame at 60fps
): [T, (newState: T | ((prev: T) => T)) => void] {
  const [state, setState] = React.useState<T>(initialState)
  const pendingStateRef = useRef<T | null>(null)
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)

  const setBatchedState = useCallback((newState: T | ((prev: T) => T)) => {
    const nextState = typeof newState === 'function' 
      ? (newState as (prev: T) => T)(pendingStateRef.current || state)
      : newState

    pendingStateRef.current = nextState

    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }

    timeoutRef.current = setTimeout(() => {
      if (pendingStateRef.current !== null) {
        setState(pendingStateRef.current)
        pendingStateRef.current = null
      }
    }, batchDelay)
  }, [state, batchDelay])

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])

  return [state, setBatchedState]
}

/**
 * Hook for lazy loading components with performance monitoring
 */
export function useLazyComponent<T extends React.ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  fallback?: React.ComponentType
) {
  const [Component, setComponent] = React.useState<T | null>(null)
  const [loading, setLoading] = React.useState(false)
  const [error, setError] = React.useState<Error | null>(null)

  const loadComponent = useCallback(async () => {
    if (Component || loading) return

    setLoading(true)
    setError(null)

    try {
      const result = await performanceMonitor.measureAsync('lazy-component-load', importFn)
      setComponent(() => result.default)
    } catch (err) {
      setError(err as Error)
    } finally {
      setLoading(false)
    }
  }, [Component, loading, importFn])

  return {
    Component: Component || fallback,
    loading,
    error,
    loadComponent
  }
}

/**
 * Hook for optimizing expensive computations with Web Workers
 */
export function useWebWorker<T, R>(
  workerScript: string,
  dependencies: T[]
): {
  result: R | null
  loading: boolean
  error: Error | null
  compute: (data: T) => void
} {
  const [result, setResult] = React.useState<R | null>(null)
  const [loading, setLoading] = React.useState(false)
  const [error, setError] = React.useState<Error | null>(null)
  const workerRef = useRef<Worker | null>(null)

  const compute = useCallback((data: T) => {
    if (typeof Worker === 'undefined') {
      setError(new Error('Web Workers not supported'))
      return
    }

    setLoading(true)
    setError(null)

    try {
      if (workerRef.current) {
        workerRef.current.terminate()
      }

      workerRef.current = new Worker(workerScript)
      
      workerRef.current.onmessage = (event) => {
        setResult(event.data)
        setLoading(false)
      }

      workerRef.current.onerror = (event) => {
        setError(new Error(event.message))
        setLoading(false)
      }

      workerRef.current.postMessage(data)
    } catch (err) {
      setError(err as Error)
      setLoading(false)
    }
  }, [workerScript])

  useEffect(() => {
    return () => {
      if (workerRef.current) {
        workerRef.current.terminate()
      }
    }
  }, [])

  return { result, loading, error, compute }
}

/**
 * Hook for optimizing list rendering with virtualization
 */
export function useVirtualList<T>(
  items: T[],
  itemHeight: number,
  containerHeight: number,
  overscan: number = 5
) {
  const [scrollTop, setScrollTop] = React.useState(0)

  const visibleRange = useMemo(() => {
    const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan)
    const endIndex = Math.min(
      items.length - 1,
      Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
    )

    return { startIndex, endIndex }
  }, [scrollTop, itemHeight, containerHeight, items.length, overscan])

  const visibleItems = useMemo(() => {
    return items.slice(visibleRange.startIndex, visibleRange.endIndex + 1)
      .map((item, index) => ({
        item,
        index: visibleRange.startIndex + index
      }))
  }, [items, visibleRange])

  const totalHeight = items.length * itemHeight

  return {
    visibleItems,
    totalHeight,
    setScrollTop,
    visibleRange
  }
}

/**
 * Higher-order component for performance monitoring
 */
export function withPerformanceMonitoring<P extends object>(
  Component: React.ComponentType<P>,
  componentName?: string
) {
  const name = componentName || Component.displayName || Component.name || 'Unknown'
  
  return React.memo(function PerformanceMonitoredComponent(props: P) {
    const renderStartTime = useRef<number>(0)

    useEffect(() => {
      renderStartTime.current = performance.now()
      performanceMonitor.start(`${name}-render`, 'render')

      return () => {
        const duration = performanceMonitor.end(`${name}-render`)
        if (duration && duration > 100) {
          console.warn(`🐌 Slow component render: ${name} took ${duration.toFixed(2)}ms`)
        }
      }
    })

    return React.createElement(Component, props)
  })
}

/**
 * Utility for preventing unnecessary re-renders
 */
export function shallowEqual(obj1: any, obj2: any): boolean {
  const keys1 = Object.keys(obj1)
  const keys2 = Object.keys(obj2)

  if (keys1.length !== keys2.length) {
    return false
  }

  for (const key of keys1) {
    if (obj1[key] !== obj2[key]) {
      return false
    }
  }

  return true
}

/**
 * Custom memo with shallow comparison
 */
export function memoShallow<P extends object>(
  Component: React.ComponentType<P>
): React.ComponentType<P> {
  return React.memo(Component, shallowEqual)
}
