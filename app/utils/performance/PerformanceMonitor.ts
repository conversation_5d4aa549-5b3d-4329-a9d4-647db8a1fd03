import React from 'react'

/**
 * Performance Monitor for React Components
 * Helps identify and fix performance bottlenecks
 */

interface PerformanceMetric {
  name: string
  startTime: number
  endTime?: number
  duration?: number
  type: 'render' | 'effect' | 'timer' | 'api' | 'custom'
}

class PerformanceMonitor {
  private static instance: PerformanceMonitor
  private metrics: Map<string, PerformanceMetric> = new Map()
  private isEnabled: boolean = process.env.NODE_ENV === 'development'

  public static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor()
    }
    return PerformanceMonitor.instance
  }

  /**
   * Start measuring a performance metric
   */
  start(name: string, type: PerformanceMetric['type'] = 'custom'): void {
    if (!this.isEnabled) return

    this.metrics.set(name, {
      name,
      startTime: performance.now(),
      type
    })
  }

  /**
   * End measuring a performance metric
   */
  end(name: string): number | null {
    if (!this.isEnabled) return null

    const metric = this.metrics.get(name)
    if (!metric) {
      console.warn(`Performance metric "${name}" not found`)
      return null
    }

    const endTime = performance.now()
    const duration = endTime - metric.startTime

    metric.endTime = endTime
    metric.duration = duration

    // Log slow operations
    if (duration > 100) {
      console.warn(`🐌 Slow operation detected: ${name} took ${duration.toFixed(2)}ms`)
    }

    return duration
  }

  /**
   * Measure a function execution time
   */
  measure<T>(name: string, fn: () => T, type: PerformanceMetric['type'] = 'custom'): T {
    if (!this.isEnabled) return fn()

    this.start(name, type)
    try {
      const result = fn()
      this.end(name)
      return result
    } catch (error) {
      this.end(name)
      throw error
    }
  }

  /**
   * Measure async function execution time
   */
  async measureAsync<T>(
    name: string, 
    fn: () => Promise<T>, 
    type: PerformanceMetric['type'] = 'custom'
  ): Promise<T> {
    if (!this.isEnabled) return fn()

    this.start(name, type)
    try {
      const result = await fn()
      this.end(name)
      return result
    } catch (error) {
      this.end(name)
      throw error
    }
  }

  /**
   * Get performance summary
   */
  getSummary(): {
    totalMetrics: number
    slowOperations: PerformanceMetric[]
    averageDuration: number
    byType: Record<string, PerformanceMetric[]>
  } {
    const completedMetrics = Array.from(this.metrics.values()).filter(m => m.duration !== undefined)
    const slowOperations = completedMetrics.filter(m => m.duration! > 100)
    const totalDuration = completedMetrics.reduce((sum, m) => sum + (m.duration || 0), 0)
    const averageDuration = completedMetrics.length > 0 ? totalDuration / completedMetrics.length : 0

    const byType = completedMetrics.reduce((acc, metric) => {
      if (!acc[metric.type]) acc[metric.type] = []
      acc[metric.type].push(metric)
      return acc
    }, {} as Record<string, PerformanceMetric[]>)

    return {
      totalMetrics: completedMetrics.length,
      slowOperations,
      averageDuration,
      byType
    }
  }

  /**
   * Clear all metrics
   */
  clear(): void {
    this.metrics.clear()
  }

  /**
   * Enable/disable monitoring
   */
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled
  }

  /**
   * Log performance summary to console
   */
  logSummary(): void {
    if (!this.isEnabled) return

    const summary = this.getSummary()
    console.group('🚀 Performance Summary')
    console.log(`Total metrics: ${summary.totalMetrics}`)
    console.log(`Average duration: ${summary.averageDuration.toFixed(2)}ms`)
    console.log(`Slow operations: ${summary.slowOperations.length}`)
    
    if (summary.slowOperations.length > 0) {
      console.group('🐌 Slow Operations')
      summary.slowOperations.forEach(op => {
        console.log(`${op.name}: ${op.duration?.toFixed(2)}ms (${op.type})`)
      })
      console.groupEnd()
    }

    console.group('📊 By Type')
    Object.entries(summary.byType).forEach(([type, metrics]) => {
      const avgDuration = metrics.reduce((sum, m) => sum + (m.duration || 0), 0) / metrics.length
      console.log(`${type}: ${metrics.length} operations, avg ${avgDuration.toFixed(2)}ms`)
    })
    console.groupEnd()
    console.groupEnd()
  }
}

// Export singleton instance
export const performanceMonitor = PerformanceMonitor.getInstance()

// React hook for component performance monitoring
export function usePerformanceMonitor(componentName: string) {
  const monitor = PerformanceMonitor.getInstance()

  const startRender = () => monitor.start(`${componentName}-render`, 'render')
  const endRender = () => monitor.end(`${componentName}-render`)
  
  const startEffect = (effectName: string) => 
    monitor.start(`${componentName}-${effectName}`, 'effect')
  const endEffect = (effectName: string) => 
    monitor.end(`${componentName}-${effectName}`)

  const measureRender = <T>(fn: () => T): T => 
    monitor.measure(`${componentName}-render`, fn, 'render')

  const measureEffect = <T>(effectName: string, fn: () => T): T => 
    monitor.measure(`${componentName}-${effectName}`, fn, 'effect')

  return {
    startRender,
    endRender,
    startEffect,
    endEffect,
    measureRender,
    measureEffect,
    logSummary: () => monitor.logSummary()
  }
}

// Utility for measuring React component re-renders
export function withPerformanceMonitoring<P extends object>(
  Component: React.ComponentType<P>,
  componentName?: string
) {
  const name = componentName || Component.displayName || Component.name || 'Unknown'
  
  return function PerformanceMonitoredComponent(props: P) {
    const monitor = usePerformanceMonitor(name)
    
    React.useEffect(() => {
      monitor.startRender()
      return () => monitor.endRender()
    })

    return React.createElement(Component, props)
  }
}
