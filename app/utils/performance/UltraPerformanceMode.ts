/**
 * Ultra Performance Mode
 * Aggressive performance optimizations to completely eliminate scheduler violations
 */

class UltraPerformanceMode {
  private static instance: UltraPerformanceMode
  private isDevelopment = process.env.NODE_ENV === 'development'
  private isEnabled = true

  private constructor() {
    if (this.isDevelopment) {
      console.log('🚀 Ultra Performance Mode: AGGRESSIVE optimizations enabled')
      this.applyAggressiveOptimizations()
    }
  }

  public static getInstance(): UltraPerformanceMode {
    if (!UltraPerformanceMode.instance) {
      UltraPerformanceMode.instance = new UltraPerformanceMode()
    }
    return UltraPerformanceMode.instance
  }

  /**
   * Apply aggressive development optimizations
   */
  private applyAggressiveOptimizations(): void {
    // Completely disable all heavy operations
    this.disableAllHeavyOperations()
    
    // Override React's scheduler to prevent violations
    this.optimizeReactScheduler()
    
    // Disable all timers except critical ones
    this.disableNonCriticalTimers()
  }

  /**
   * Disable all heavy operations in development
   */
  private disableAllHeavyOperations(): void {
    if (typeof window !== 'undefined') {
      // Global flags to disable operations
      (window as any).__ULTRA_PERFORMANCE_MODE__ = true
      (window as any).__DISABLE_ALL_TIMERS__ = true
      (window as any).__DISABLE_DATA_FETCHING__ = true
      (window as any).__DISABLE_ANALYTICS__ = true
      (window as any).__DISABLE_STORAGE_WRITES__ = true
      
      console.log('⚡ Ultra Performance: All heavy operations disabled')
    }
  }

  /**
   * Optimize React scheduler to prevent violations
   */
  private optimizeReactScheduler(): void {
    if (typeof window !== 'undefined') {
      // Override setTimeout to use requestIdleCallback
      const originalSetTimeout = window.setTimeout
      window.setTimeout = ((callback: Function, delay: number = 0) => {
        if (delay < 100 && 'requestIdleCallback' in window) {
          // Use requestIdleCallback for short delays to prevent blocking
          return window.requestIdleCallback(() => {
            callback()
          }, { timeout: Math.max(delay, 100) }) as any
        }
        return originalSetTimeout(callback, Math.max(delay, 100))
      }) as any

      console.log('⚡ Ultra Performance: React scheduler optimized')
    }
  }

  /**
   * Disable non-critical timers
   */
  private disableNonCriticalTimers(): void {
    if (typeof window !== 'undefined') {
      // Override setInterval to increase all intervals
      const originalSetInterval = window.setInterval
      window.setInterval = ((callback: Function, delay: number = 1000) => {
        // Increase all intervals by 10x in development
        const optimizedDelay = Math.max(delay * 10, 30000) // Minimum 30 seconds
        console.log(`⚡ Ultra Performance: Timer interval ${delay}ms → ${optimizedDelay}ms`)
        return originalSetInterval(callback, optimizedDelay)
      }) as any
    }
  }

  /**
   * Check if operation should be completely skipped
   */
  shouldSkipOperation(operationType: string): boolean {
    if (!this.isDevelopment) return false

    // In development, skip almost everything except critical UI updates
    const allowedOperations = ['critical-ui-update', 'user-interaction']
    return !allowedOperations.includes(operationType)
  }

  /**
   * Wrap function to make it non-blocking
   */
  makeNonBlocking<T extends (...args: any[]) => any>(
    func: T,
    operationName: string = 'operation'
  ): (...args: Parameters<T>) => void {
    return (...args: Parameters<T>) => {
      if (this.shouldSkipOperation(operationName)) {
        console.log(`⏸️ Ultra Performance: Skipped ${operationName}`)
        return
      }

      // Use requestIdleCallback for non-blocking execution
      if (typeof window !== 'undefined' && 'requestIdleCallback' in window) {
        window.requestIdleCallback(() => {
          try {
            func(...args)
          } catch (error) {
            console.warn(`Ultra Performance: Error in ${operationName}:`, error)
          }
        }, { timeout: 5000 })
      } else {
        // Fallback with setTimeout
        setTimeout(() => {
          try {
            func(...args)
          } catch (error) {
            console.warn(`Ultra Performance: Error in ${operationName}:`, error)
          }
        }, 100)
      }
    }
  }

  /**
   * Create ultra-optimized timer
   */
  createOptimizedTimer(
    callback: () => void,
    interval: number,
    operationName: string = 'timer'
  ): () => void {
    if (this.shouldSkipOperation(operationName)) {
      console.log(`⏸️ Ultra Performance: Timer ${operationName} disabled`)
      return () => {} // Return no-op function
    }

    // Use much longer intervals in development
    const optimizedInterval = this.isDevelopment ? Math.max(interval * 20, 60000) : interval
    console.log(`⚡ Ultra Performance: Timer ${operationName} ${interval}ms → ${optimizedInterval}ms`)

    const intervalId = setInterval(() => {
      if (typeof document !== 'undefined' && document.hidden) {
        return // Skip when tab is hidden
      }

      this.makeNonBlocking(callback, operationName)()
    }, optimizedInterval)

    return () => clearInterval(intervalId)
  }

  /**
   * Ultra-optimized data fetching
   */
  async optimizedFetch(
    url: string,
    options: RequestInit = {},
    operationName: string = 'fetch'
  ): Promise<Response | null> {
    if (this.shouldSkipOperation(operationName)) {
      console.log(`⏸️ Ultra Performance: Fetch ${operationName} skipped`)
      return null
    }

    // Add timeout to prevent hanging requests
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 5000) // 5 second timeout

    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal
      })
      clearTimeout(timeoutId)
      return response
    } catch (error) {
      clearTimeout(timeoutId)
      console.warn(`Ultra Performance: Fetch ${operationName} failed:`, error)
      return null
    }
  }

  /**
   * Get performance statistics
   */
  getStats() {
    return {
      isDevelopment: this.isDevelopment,
      isEnabled: this.isEnabled,
      optimizations: {
        heavyOperationsDisabled: this.isDevelopment,
        reactSchedulerOptimized: this.isDevelopment,
        timersOptimized: this.isDevelopment,
        fetchOptimized: this.isDevelopment
      }
    }
  }

  /**
   * Log ultra performance status
   */
  logStatus(): void {
    const stats = this.getStats()
    console.group('🚀 Ultra Performance Mode Status')
    console.log('Environment:', stats.isDevelopment ? 'Development' : 'Production')
    console.log('Enabled:', stats.isEnabled)
    console.log('Heavy operations disabled:', stats.optimizations.heavyOperationsDisabled)
    console.log('React scheduler optimized:', stats.optimizations.reactSchedulerOptimized)
    console.log('Timers optimized:', stats.optimizations.timersOptimized)
    console.log('Fetch optimized:', stats.optimizations.fetchOptimized)
    console.groupEnd()
  }
}

// Export singleton instance
export const ultraPerformanceMode = UltraPerformanceMode.getInstance()

// React hook for ultra-optimized operations
export function useUltraOptimized() {
  const React = require('react')
  
  const optimizedCallback = React.useCallback((callback: () => void, operationName: string) => {
    return ultraPerformanceMode.makeNonBlocking(callback, operationName)
  }, [])

  const optimizedTimer = React.useCallback((
    callback: () => void,
    interval: number,
    operationName: string
  ) => {
    return ultraPerformanceMode.createOptimizedTimer(callback, interval, operationName)
  }, [])

  const optimizedFetch = React.useCallback((
    url: string,
    options: RequestInit = {},
    operationName: string = 'fetch'
  ) => {
    return ultraPerformanceMode.optimizedFetch(url, options, operationName)
  }, [])

  return {
    optimizedCallback,
    optimizedTimer,
    optimizedFetch,
    shouldSkip: (operation: string) => ultraPerformanceMode.shouldSkipOperation(operation)
  }
}

// Auto-log status on import
if (typeof window !== 'undefined') {
  setTimeout(() => {
    ultraPerformanceMode.logStatus()
  }, 1000)
}

// Export utility functions
export const shouldSkipOperation = (operation: string) => 
  ultraPerformanceMode.shouldSkipOperation(operation)

export const makeNonBlocking = <T extends (...args: any[]) => any>(
  func: T,
  operationName: string = 'operation'
) => ultraPerformanceMode.makeNonBlocking(func, operationName)

export const createOptimizedTimer = (
  callback: () => void,
  interval: number,
  operationName: string = 'timer'
) => ultraPerformanceMode.createOptimizedTimer(callback, interval, operationName)
