import { NextResponse } from "next/server"
import pool from '@/lib/db'

// POST /api/cleanup-duplicate-tables - Remove duplicate game table entries
export async function POST() {
  try {
    console.log('🔍 Checking for duplicate game tables...')
    const results = []
    
    // Step 1: Find duplicate tables (same number + same user assignment)
    const duplicatesQuery = `
      SELECT 
        number, 
        assigned_user_id, 
        assigned_username,
        COUNT(*) as count,
        array_agg(id ORDER BY id) as ids
      FROM gametables 
      WHERE assigned_user_id IS NOT NULL
      GROUP BY number, assigned_user_id, assigned_username
      HAVING COUNT(*) > 1
      ORDER BY number, assigned_user_id
    `
    
    const duplicates = await pool.query(duplicatesQuery)
    
    if (duplicates.rows.length === 0) {
      results.push({ step: 'Check duplicates', status: 'success', message: 'No duplicate tables found!' })
    } else {
      results.push({ 
        step: 'Check duplicates', 
        status: 'info', 
        message: `Found ${duplicates.rows.length} sets of duplicate tables`,
        details: duplicates.rows.map(d => ({
          table: d.number,
          user: d.assigned_username,
          count: d.count,
          ids: d.ids
        }))
      })
      
      // Step 2: Clean up duplicates
      let totalDeleted = 0
      
      for (const duplicate of duplicates.rows) {
        const { number, assigned_user_id, assigned_username, count, ids } = duplicate
        
        // Keep the first one (lowest ID) and delete the rest
        const idsToDelete = ids.slice(1) // Remove first element, keep the rest for deletion
        
        if (idsToDelete.length > 0) {
          await pool.query(
            `DELETE FROM gametables WHERE id = ANY($1)`,
            [idsToDelete]
          )
          
          totalDeleted += idsToDelete.length
          
          results.push({
            step: 'Delete duplicates',
            status: 'success',
            message: `Deleted ${idsToDelete.length} duplicate entries for Table ${number} (User: ${assigned_username})`,
            details: { deletedIds: idsToDelete }
          })
        }
      }
      
      results.push({
        step: 'Cleanup summary',
        status: 'success',
        message: `Total deleted: ${totalDeleted} duplicate table entries`
      })
    }
    
    // Step 3: Get final statistics
    const finalCount = await pool.query('SELECT COUNT(*) as count FROM gametables')
    
    const tablesByUser = await pool.query(`
      SELECT 
        COALESCE(assigned_username, 'Unassigned') as username,
        COUNT(*) as table_count,
        array_agg(DISTINCT number ORDER BY number) as table_numbers
      FROM gametables 
      GROUP BY assigned_username
      ORDER BY assigned_username
    `)
    
    return NextResponse.json({
      success: true,
      message: 'Duplicate table cleanup completed successfully!',
      results,
      finalStats: {
        totalTables: finalCount.rows[0].count,
        tablesByUser: tablesByUser.rows.map(row => ({
          username: row.username,
          tableCount: row.table_count,
          tableNumbers: row.table_numbers
        }))
      }
    })

  } catch (error: unknown) {
    console.error('Duplicate cleanup error:', error)
    return NextResponse.json({
      error: "Failed to cleanup duplicate tables",
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
