import { type NextRequest, NextResponse } from "next/server"
import pool from '@/lib/db'
import { requireAuth, requireAdmin } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    // Get current user from authentication
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: "Authentication required" }, { status: 401 })
    }

    const currentUser = authResult.user

    // Admin users can see all tables (for management purposes)
    // Regular users can only see tables assigned to them
    let result
    if (currentUser.role === 'admin') {
      result = await pool.query('SELECT * FROM gametables ORDER BY number')
    } else {
      result = await pool.query(
        'SELECT * FROM gametables WHERE assigned_user_id = $1 ORDER BY number',
        [currentUser.id]
      )
    }

    return NextResponse.json(result.rows)
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: "Failed to fetch game tables" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    // Require admin authentication
    const authResult = await requireAdmin(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 })
    }

    const body = await request.json()
    const { number, name, isActive, hourlyRate, tableType, assignedUserId, assignedUsername, customSoundUrl } = body

    // Validate required fields
    if (!number || !name || hourlyRate === undefined) {
      return NextResponse.json({ error: "Number, name, and hourly rate are required" }, { status: 400 })
    }

    try {
      // First, ensure the custom_sound_url column exists
      await pool.query(`
        ALTER TABLE gametables
        ADD COLUMN IF NOT EXISTS custom_sound_url TEXT
      `)

      const result = await pool.query(
        'INSERT INTO gametables (number, name, is_active, hourly_rate, table_type, assigned_user_id, assigned_username, custom_sound_url) VALUES ($1, $2, $3, $4, $5, $6, $7, $8) RETURNING *',
        [number, name, isActive !== undefined ? isActive : true, hourlyRate, tableType || 'billiard', assignedUserId, assignedUsername, customSoundUrl]
      )

      return NextResponse.json(result.rows[0], { status: 201 })
    } catch (insertError: unknown) {
      // If we get a unique constraint violation, try to fix the database schema
      if (insertError instanceof Error && insertError.message.includes('duplicate key value violates unique constraint "gametables_number_key"')) {
        console.log('🔄 Detected old unique constraint on game tables, attempting to fix database schema...')

        try {
          // Remove old constraint and add new one
          await pool.query('ALTER TABLE gametables DROP CONSTRAINT IF EXISTS gametables_number_key')
          await pool.query(`
            ALTER TABLE gametables ADD CONSTRAINT unique_gametable_number_per_user
            UNIQUE (number, assigned_user_id)
          `)

          console.log('✅ Fixed game tables database constraint, retrying insert...')

          // Retry the insert
          const result = await pool.query(
            'INSERT INTO gametables (number, name, is_active, hourly_rate, table_type, assigned_user_id, assigned_username, custom_sound_url) VALUES ($1, $2, $3, $4, $5, $6, $7, $8) RETURNING *',
            [number, name, isActive !== undefined ? isActive : true, hourlyRate, tableType || 'billiard', assignedUserId, assignedUsername, customSoundUrl]
          )

          return NextResponse.json(result.rows[0], { status: 201 })
        } catch (migrationError: unknown) {
          console.error('Failed to fix game tables database constraint:', migrationError)
          return NextResponse.json({
            error: "Database constraint issue - please contact administrator",
            details: migrationError instanceof Error ? migrationError.message : 'Unknown error'
          }, { status: 500 })
        }
      } else {
        throw insertError
      }
    }
  } catch (error: unknown) {
    console.error('Database error:', error)
    return NextResponse.json({ error: "Failed to create game table" }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Require admin authentication
    const authResult = await requireAdmin(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 })
    }

    const body = await request.json()
    const { id, number, name, isActive, hourlyRate, tableType, assignedUserId, assignedUsername, customSoundUrl } = body

    // Validate required fields
    if (!id || !number || !name || hourlyRate === undefined) {
      return NextResponse.json({ error: "ID, number, name, and hourly rate are required" }, { status: 400 })
    }

    try {
      // Ensure the custom_sound_url column exists
      await pool.query(`
        ALTER TABLE gametables
        ADD COLUMN IF NOT EXISTS custom_sound_url TEXT
      `)

      const result = await pool.query(
        'UPDATE gametables SET number = $1, name = $2, is_active = $3, hourly_rate = $4, table_type = $5, assigned_user_id = $6, assigned_username = $7, custom_sound_url = $8 WHERE id = $9 RETURNING *',
        [number, name, isActive, hourlyRate, tableType || 'billiard', assignedUserId, assignedUsername, customSoundUrl, id]
      )

      if (result.rows.length === 0) {
        return NextResponse.json({ error: "Game table not found" }, { status: 404 })
      }

      return NextResponse.json(result.rows[0])
    } catch (updateError: unknown) {
      // If we get a unique constraint violation during update, try to fix the database schema
      if (updateError instanceof Error && updateError.message.includes('duplicate key value violates unique constraint "gametables_number_key"')) {
        console.log('🔄 Detected old unique constraint on game tables during update, attempting to fix database schema...')

        try {
          // Remove old constraint and add new one
          await pool.query('ALTER TABLE gametables DROP CONSTRAINT IF EXISTS gametables_number_key')
          await pool.query(`
            ALTER TABLE gametables ADD CONSTRAINT unique_gametable_number_per_user
            UNIQUE (number, assigned_user_id)
          `)

          console.log('✅ Fixed game tables database constraint during update, retrying update...')

          // Retry the update
          const result = await pool.query(
            'UPDATE gametables SET number = $1, name = $2, is_active = $3, hourly_rate = $4, table_type = $5, assigned_user_id = $6, assigned_username = $7, custom_sound_url = $8 WHERE id = $9 RETURNING *',
            [number, name, isActive, hourlyRate, tableType || 'billiard', assignedUserId, assignedUsername, customSoundUrl, id]
          )

          if (result.rows.length === 0) {
            return NextResponse.json({ error: "Game table not found" }, { status: 404 })
          }

          return NextResponse.json(result.rows[0])
        } catch (migrationError: unknown) {
          console.error('Failed to fix game tables database constraint during update:', migrationError)
          return NextResponse.json({
            error: "Database constraint issue during update - please contact administrator",
            details: migrationError instanceof Error ? migrationError.message : 'Unknown error'
          }, { status: 500 })
        }
      } else {
        throw updateError
      }
    }
  } catch (error: unknown) {
    console.error('Database error:', error)
    return NextResponse.json({ error: "Failed to update game table" }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Require admin authentication
    const authResult = await requireAdmin(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json({ error: "ID is required" }, { status: 400 })
    }

    const result = await pool.query('DELETE FROM gametables WHERE id = $1 RETURNING *', [id])

    if (result.rows.length === 0) {
      return NextResponse.json({ error: "Game table not found" }, { status: 404 })
    }

    return NextResponse.json({ message: "Game table deleted successfully" })
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: "Failed to delete game table" }, { status: 500 })
  }
}
