import { NextRequest, NextResponse } from 'next/server'
import pool from '@/lib/db'

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 Simple migration: Adding time_limit column...')

    // Check if column already exists
    const columnCheck = await pool.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'games' AND column_name = 'time_limit'
    `)

    if (columnCheck.rows.length > 0) {
      console.log('✅ time_limit column already exists')
      return NextResponse.json({ 
        success: true, 
        message: 'time_limit column already exists',
        alreadyExists: true
      })
    }

    console.log('📝 Adding time_limit column to games table...')
    
    // Add the column
    await pool.query(`ALTER TABLE games ADD COLUMN time_limit INTEGER`)
    
    console.log('✅ time_limit column added successfully')

    // Verify it was added
    const verifyCheck = await pool.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'games' AND column_name = 'time_limit'
    `)

    if (verifyCheck.rows.length > 0) {
      console.log('✅ Column verification successful')
      return NextResponse.json({
        success: true,
        message: 'time_limit column added successfully',
        alreadyExists: false
      })
    } else {
      console.error('❌ Column verification failed')
      return NextResponse.json({
        success: false,
        message: 'Column was not added properly'
      }, { status: 500 })
    }

  } catch (error) {
    console.error('❌ Simple migration failed:', error)
    
    // Check if it's a "column already exists" error
    const errorMessage = error instanceof Error ? error.message : String(error)
    if (errorMessage.includes('already exists') || errorMessage.includes('duplicate column')) {
      console.log('✅ Column already exists (caught from error)')
      return NextResponse.json({
        success: true,
        message: 'time_limit column already exists',
        alreadyExists: true
      })
    }
    
    return NextResponse.json({
      success: false,
      message: 'Failed to add time_limit column',
      error: errorMessage
    }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    // Just check if column exists
    const columnCheck = await pool.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'games' AND column_name = 'time_limit'
    `)

    const hasColumn = columnCheck.rows.length > 0

    return NextResponse.json({
      success: true,
      hasColumn: hasColumn,
      message: hasColumn ? 'time_limit column exists' : 'time_limit column missing'
    })

  } catch (error) {
    console.error('❌ Column check failed:', error)
    return NextResponse.json({
      success: false,
      message: 'Failed to check column',
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 })
  }
}
