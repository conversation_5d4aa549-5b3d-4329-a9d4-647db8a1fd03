import { type NextRequest, NextResponse } from "next/server"
import pool from '@/lib/db'
import bcrypt from 'bcryptjs'
import { requireAuth } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const userId = authResult.user.id

    const result = await pool.query(
      'SELECT id, username, full_name, role, profile_picture, created_at FROM users WHERE id = $1',
      [userId]
    )

    if (result.rows.length === 0) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    return NextResponse.json(result.rows[0])
  } catch (error: unknown) {
    console.error('Database error:', error)
    return NextResponse.json({ error: "Failed to fetch user profile" }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const userId = authResult.user.id
    const body = await request.json()
    const { fullName, username, currentPassword, newPassword, profilePicture } = body

    // Validate required fields
    if (!fullName) {
      return NextResponse.json({ error: "Full name is required" }, { status: 400 })
    }

    if (!username) {
      return NextResponse.json({ error: "Username is required" }, { status: 400 })
    }

    // Check if username is already taken by another user
    const existingUser = await pool.query(
      'SELECT id FROM users WHERE username = $1 AND id != $2',
      [username, userId]
    )

    if (existingUser.rows.length > 0) {
      return NextResponse.json({ error: "Username is already taken" }, { status: 400 })
    }

    // Get current user data
    const currentUser = await pool.query(
      'SELECT password_hash FROM users WHERE id = $1',
      [userId]
    )

    if (currentUser.rows.length === 0) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    // If changing password, verify current password
    if (newPassword) {
      if (!currentPassword) {
        return NextResponse.json({ error: "Current password is required to change password" }, { status: 400 })
      }

      const isCurrentPasswordValid = await bcrypt.compare(currentPassword, currentUser.rows[0].password_hash)
      if (!isCurrentPasswordValid) {
        return NextResponse.json({ error: "Current password is incorrect" }, { status: 400 })
      }

      if (newPassword.length < 6) {
        return NextResponse.json({ error: "New password must be at least 6 characters long" }, { status: 400 })
      }

      // Hash new password and update
      const hashedNewPassword = await bcrypt.hash(newPassword, 10)
      const result = await pool.query(
        `UPDATE users
         SET username = $1, full_name = $2, password_hash = $3, profile_picture = $4, updated_at = CURRENT_TIMESTAMP
         WHERE id = $5
         RETURNING id, username, full_name, role, profile_picture`,
        [username, fullName, hashedNewPassword, profilePicture || null, userId]
      )

      return NextResponse.json(result.rows[0])
    } else {
      // Update only username, name and profile picture
      const result = await pool.query(
        `UPDATE users
         SET username = $1, full_name = $2, profile_picture = $3, updated_at = CURRENT_TIMESTAMP
         WHERE id = $4
         RETURNING id, username, full_name, role, profile_picture`,
        [username, fullName, profilePicture || null, userId]
      )

      return NextResponse.json(result.rows[0])
    }
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: "Failed to update user profile" }, { status: 500 })
  }
}
