import { NextRequest, NextResponse } from 'next/server'
import { requireAuth } from '@/lib/auth'
import { optimizedStorage } from '@/app/utils/storage/OptimizedStorageManager'

// GET /api/cache - Get cache statistics and health
export async function GET(request: NextRequest) {
  try {
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const url = new URL(request.url)
    const action = url.searchParams.get('action')

    switch (action) {
      case 'stats':
        const stats = optimizedStorage.getStats()
        return NextResponse.json({
          success: true,
          stats,
          timestamp: new Date().toISOString()
        })

      case 'health':
        const health = await optimizedStorage.healthCheck()
        return NextResponse.json({
          success: true,
          health,
          timestamp: new Date().toISOString()
        })

      default:
        // Default: return basic info
        const basicStats = optimizedStorage.getStats()
        return NextResponse.json({
          success: true,
          stats: basicStats,
          cacheFlow: {
            initialized: true,
            layers: ['memory', 'localStorage']
          },
          timestamp: new Date().toISOString()
        })
    }

  } catch (error: unknown) {
    console.error('Cache API GET error:', error)
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// POST /api/cache - Cache management operations
export async function POST(request: NextRequest) {
  try {
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { action, key, keys, value, options } = body

    switch (action) {
      case 'get':
        if (!key) {
          return NextResponse.json({ error: 'Key is required' }, { status: 400 })
        }
        
        const result = await optimizedStorage.getJSON(key)
        return NextResponse.json({
          success: true,
          data: result,
          key
        })

      case 'set':
        if (!key || value === undefined) {
          return NextResponse.json({ error: 'Key and value are required' }, { status: 400 })
        }
        
        const setSuccess = await optimizedStorage.setJSON(key, value)
        return NextResponse.json({
          success: setSuccess,
          message: setSuccess ? 'Value cached successfully' : 'Failed to cache value',
          key
        })

      case 'delete':
        if (!key) {
          return NextResponse.json({ error: 'Key is required' }, { status: 400 })
        }

        const deleteSuccess = await optimizedStorage.delete(key)
        return NextResponse.json({
          success: deleteSuccess,
          message: deleteSuccess ? 'Value deleted successfully' : 'Failed to delete value',
          key
        })

      case 'getBulk':
        if (!keys || !Array.isArray(keys)) {
          return NextResponse.json({ error: 'Keys array is required' }, { status: 400 })
        }

        const bulkResult = await optimizedStorage.getBulk(keys)
        return NextResponse.json({
          success: true,
          data: bulkResult,
          keys
        })

      case 'setBulk':
        if (!value || typeof value !== 'object') {
          return NextResponse.json({ error: 'Value object is required' }, { status: 400 })
        }

        const bulkSetSuccess = await optimizedStorage.setBulk(value)
        return NextResponse.json({
          success: bulkSetSuccess,
          message: bulkSetSuccess ? 'Bulk values cached successfully' : 'Failed to cache bulk values',
          count: Object.keys(value).length
        })

      case 'invalidate':
        const invalidateKeys = Array.isArray(keys) ? keys : [key]
        if (!invalidateKeys.length) {
          return NextResponse.json({ error: 'Keys are required' }, { status: 400 })
        }

        optimizedStorage.invalidate(invalidateKeys)
        return NextResponse.json({
          success: true,
          message: 'Cache invalidated successfully',
          keys: invalidateKeys
        })

      case 'warmUp':
        if (!keys || !Array.isArray(keys)) {
          return NextResponse.json({ error: 'Keys array is required' }, { status: 400 })
        }

        await optimizedStorage.warmUp(keys)
        return NextResponse.json({
          success: true,
          message: 'Cache warmed up successfully',
          keys
        })

      case 'flush':
        await optimizedStorage.flush()
        return NextResponse.json({
          success: true,
          message: 'Optimized storage flushed successfully'
        })

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
    }

  } catch (error: unknown) {
    console.error('Cache API POST error:', error)
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// PUT /api/cache - Update cache configuration
export async function PUT(request: NextRequest) {
  try {
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { action, config } = body

    switch (action) {
      case 'configure':
        // This would update cache configuration
        // For now, just return success
        return NextResponse.json({
          success: true,
          message: 'Cache configuration updated',
          config
        })

      default:
        return NextResponse.json({ error: 'Invalid action' }, { status: 400 })
    }

  } catch (error) {
    console.error('Cache API PUT error:', error)
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// DELETE /api/cache - Clear cache (use with caution)
export async function DELETE(request: NextRequest) {
  try {
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Only allow cache clearing in development
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json({ 
        error: 'Cache clearing not allowed in production' 
      }, { status: 403 })
    }

    const url = new URL(request.url)
    const confirm = url.searchParams.get('confirm')

    if (confirm !== 'true') {
      return NextResponse.json({ 
        error: 'Confirmation required. Add ?confirm=true to clear cache' 
      }, { status: 400 })
    }

    // Clear cache (this would need to be implemented)
    return NextResponse.json({
      success: true,
      message: 'Cache cleared successfully (development only)',
      warning: 'This operation is not available in production'
    })

  } catch (error) {
    console.error('Cache API DELETE error:', error)
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
