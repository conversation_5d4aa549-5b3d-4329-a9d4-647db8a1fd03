import { NextResponse, type NextRequest } from 'next/server'
import pool from '@/lib/db'
import { requireAuth } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = authResult.user
    // Determine if user can see all data or only their own
    const isAdmin = user.role === 'admin'
    const userFilter = isAdmin ? '' : 'AND user_id = $1'
    const userParams = isAdmin ? [] : [user.id]

    // Get daily revenue for the last 7 days
    const dailyRevenueQuery = isAdmin ? `
      WITH date_series AS (
        SELECT generate_series(
          CURRENT_DATE - INTERVAL '6 days',
          CURRENT_DATE,
          '1 day'::interval
        )::date as day
      ),
      daily_games AS (
        SELECT
          DATE(start_time) as day,
          COALESCE(SUM(cost), 0) as games_revenue
        FROM games
        WHERE start_time >= CURRENT_DATE - INTERVAL '6 days'
        GROUP BY DATE(start_time)
      ),
      daily_orders AS (
        SELECT
          DATE(created_at) as day,
          COALESCE(SUM(total), 0) as bar_revenue
        FROM orders
        WHERE created_at >= CURRENT_DATE - INTERVAL '6 days'
        GROUP BY DATE(created_at)
      )
      SELECT
        ds.day,
        to_char(ds.day, 'MM/DD') as day_formatted,
        COALESCE(g.games_revenue, 0) as games,
        COALESCE(o.bar_revenue, 0) as bar,
        COALESCE(g.games_revenue, 0) + COALESCE(o.bar_revenue, 0) as total
      FROM date_series ds
      LEFT JOIN daily_games g ON ds.day = g.day
      LEFT JOIN daily_orders o ON ds.day = o.day
      ORDER BY ds.day ASC
    ` : `
      WITH date_series AS (
        SELECT generate_series(
          CURRENT_DATE - INTERVAL '6 days',
          CURRENT_DATE,
          '1 day'::interval
        )::date as day
      ),
      daily_games AS (
        SELECT
          DATE(start_time) as day,
          COALESCE(SUM(cost), 0) as games_revenue
        FROM games
        WHERE start_time >= CURRENT_DATE - INTERVAL '6 days' AND created_by = $1
        GROUP BY DATE(start_time)
      ),
      daily_orders AS (
        SELECT
          DATE(created_at) as day,
          COALESCE(SUM(total), 0) as bar_revenue
        FROM orders
        WHERE created_at >= CURRENT_DATE - INTERVAL '6 days' AND created_by = $1
        GROUP BY DATE(created_at)
      )
      SELECT
        ds.day,
        to_char(ds.day, 'MM/DD') as day_formatted,
        COALESCE(g.games_revenue, 0) as games,
        COALESCE(o.bar_revenue, 0) as bar,
        COALESCE(g.games_revenue, 0) + COALESCE(o.bar_revenue, 0) as total
      FROM date_series ds
      LEFT JOIN daily_games g ON ds.day = g.day
      LEFT JOIN daily_orders o ON ds.day = o.day
      ORDER BY ds.day ASC
    `

    const dailyRevenue = await pool.query(dailyRevenueQuery, userParams)

    // Get hourly table usage for today (including current hour) - separate for games and bar orders
    const hourlyUsageQuery = isAdmin ? `
      WITH hours AS (
        SELECT generate_series(
          date_trunc('hour', CURRENT_DATE),
          date_trunc('hour', NOW() + INTERVAL '1 hour'),
          '1 hour'::interval
        ) as hour
      ),
      hourly_games AS (
        SELECT
          date_trunc('hour', g.start_time) as hour,
          COUNT(DISTINCT g.table_number) as game_tables
        FROM games g
        WHERE DATE(g.start_time) = CURRENT_DATE
        GROUP BY date_trunc('hour', g.start_time)
      ),
      hourly_orders AS (
        SELECT
          date_trunc('hour', o.created_at) as hour,
          COUNT(DISTINCT o.table_number) as bar_tables
        FROM orders o
        WHERE DATE(o.created_at) = CURRENT_DATE
        GROUP BY date_trunc('hour', o.created_at)
      )
      SELECT
        to_char(h.hour, 'HH24:00') as hour,
        COALESCE(hg.game_tables, 0) as games,
        COALESCE(ho.bar_tables, 0) as bar,
        COALESCE(hg.game_tables, 0) + COALESCE(ho.bar_tables, 0) as total
      FROM hours h
      LEFT JOIN hourly_games hg ON h.hour = hg.hour
      LEFT JOIN hourly_orders ho ON h.hour = ho.hour
      GROUP BY h.hour, hg.game_tables, ho.bar_tables
      ORDER BY h.hour
    ` : `
      WITH hours AS (
        SELECT generate_series(
          date_trunc('hour', CURRENT_DATE),
          date_trunc('hour', NOW() + INTERVAL '1 hour'),
          '1 hour'::interval
        ) as hour
      ),
      hourly_games AS (
        SELECT
          date_trunc('hour', g.start_time) as hour,
          COUNT(DISTINCT g.table_number) as game_tables
        FROM games g
        WHERE DATE(g.start_time) = CURRENT_DATE AND g.created_by = $1
        GROUP BY date_trunc('hour', g.start_time)
      ),
      hourly_orders AS (
        SELECT
          date_trunc('hour', o.created_at) as hour,
          COUNT(DISTINCT o.table_number) as bar_tables
        FROM orders o
        WHERE DATE(o.created_at) = CURRENT_DATE AND o.created_by = $1
        GROUP BY date_trunc('hour', o.created_at)
      )
      SELECT
        to_char(h.hour, 'HH24:00') as hour,
        COALESCE(hg.game_tables, 0) as games,
        COALESCE(ho.bar_tables, 0) as bar,
        COALESCE(hg.game_tables, 0) + COALESCE(ho.bar_tables, 0) as total
      FROM hours h
      LEFT JOIN hourly_games hg ON h.hour = hg.hour
      LEFT JOIN hourly_orders ho ON h.hour = ho.hour
      GROUP BY h.hour, hg.game_tables, ho.bar_tables
      ORDER BY h.hour
    `

    const hourlyUsage = await pool.query(hourlyUsageQuery, userParams)



    // Get enhanced performance metrics
    const performanceMetricsQuery = isAdmin ? `
      WITH active_game_tables AS (
        SELECT COUNT(DISTINCT table_number) as active_count
        FROM games
        WHERE status = 'active'
      ),
      total_game_tables AS (
        SELECT COUNT(*) as total_count
        FROM gametables
        WHERE is_active = true
      ),
      table_utilization AS (
        SELECT
          CASE
            WHEN tgt.total_count > 0 THEN (agt.active_count::float / tgt.total_count::float) * 100
            ELSE 0
          END as utilization
        FROM active_game_tables agt
        CROSS JOIN total_game_tables tgt
      ),
      avg_order_value AS (
        SELECT COALESCE(AVG(total), 0) as avg_value
        FROM orders
        WHERE created_at >= CURRENT_DATE
        AND status = 'completed'
      ),
      peak_hours AS (
        SELECT
          to_char(date_trunc('hour', start_time), 'HH24:00') as hour,
          COUNT(*) as game_count
        FROM games
        WHERE start_time >= NOW() - INTERVAL '7 days'
        GROUP BY date_trunc('hour', start_time)
        ORDER BY game_count DESC
        LIMIT 1
      ),
      most_used_table AS (
        SELECT table_number, COUNT(*) as usage_count
        FROM games
        WHERE DATE(start_time) = CURRENT_DATE
        GROUP BY table_number
        ORDER BY usage_count DESC
        LIMIT 1
      ),
      top_drink AS (
        SELECT
          (jsonb_array_elements(items)->>'name') as drink_name,
          COUNT(*) as order_count
        FROM orders
        WHERE DATE(created_at) = CURRENT_DATE
        AND status = 'completed'
        GROUP BY drink_name
        ORDER BY order_count DESC
        LIMIT 1
      ),
      bar_revenue_today AS (
        SELECT COALESCE(SUM(total), 0) as revenue
        FROM orders
        WHERE DATE(created_at) = CURRENT_DATE
        AND status = 'completed'
      ),
      games_today AS (
        SELECT COUNT(*) as count
        FROM games
        WHERE DATE(start_time) = CURRENT_DATE
        AND status = 'completed'
      ),
      monthly_games_revenue AS (
        SELECT COALESCE(SUM(cost), 0) as revenue
        FROM games
        WHERE start_time >= DATE_TRUNC('month', CURRENT_DATE)
        AND status = 'completed'
      ),
      monthly_bar_revenue AS (
        SELECT COALESCE(SUM(total), 0) as revenue
        FROM orders
        WHERE created_at >= DATE_TRUNC('month', CURRENT_DATE)
        AND status = 'completed'
      ),
      bestseller AS (
        SELECT
          (jsonb_array_elements(items)->>'name') as item_name,
          COUNT(*) as sales_count
        FROM orders
        WHERE created_at >= NOW() - INTERVAL '7 days'
        AND status = 'completed'
        GROUP BY item_name
        ORDER BY sales_count DESC
        LIMIT 1
      )
      SELECT
        (SELECT active_count FROM active_game_tables) as active_tables,
        (SELECT utilization FROM table_utilization) as table_utilization,
        (SELECT avg_value FROM avg_order_value) as avg_order_value,
        (SELECT hour FROM peak_hours) as peak_hour,
        (SELECT table_number FROM most_used_table) as most_used_table,
        (SELECT drink_name FROM top_drink) as top_drink,
        (SELECT revenue FROM bar_revenue_today) as bar_revenue_today,
        (SELECT count FROM games_today) as games_today,
        (SELECT revenue FROM monthly_games_revenue) + (SELECT revenue FROM monthly_bar_revenue) as monthly_revenue,
        (SELECT revenue FROM monthly_games_revenue) as monthly_games_revenue,
        (SELECT revenue FROM monthly_bar_revenue) as monthly_bar_revenue,
        (SELECT item_name FROM bestseller) as bestseller
    ` : `
      WITH active_game_tables AS (
        SELECT COUNT(DISTINCT table_number) as active_count
        FROM games
        WHERE status = 'active' AND created_by = $1
      ),
      total_game_tables AS (
        SELECT COUNT(*) as total_count
        FROM gametables
        WHERE is_active = true
      ),
      table_utilization AS (
        SELECT
          CASE
            WHEN tgt.total_count > 0 THEN (agt.active_count::float / tgt.total_count::float) * 100
            ELSE 0
          END as utilization
        FROM active_game_tables agt
        CROSS JOIN total_game_tables tgt
      ),
      avg_order_value AS (
        SELECT COALESCE(AVG(total), 0) as avg_value
        FROM orders
        WHERE created_at >= CURRENT_DATE
        AND status = 'completed' AND created_by = $1
      ),
      peak_hours AS (
        SELECT
          to_char(date_trunc('hour', start_time), 'HH24:00') as hour,
          COUNT(*) as game_count
        FROM games
        WHERE start_time >= NOW() - INTERVAL '7 days' AND created_by = $1
        GROUP BY date_trunc('hour', start_time)
        ORDER BY game_count DESC
        LIMIT 1
      ),
      most_used_table AS (
        SELECT table_number, COUNT(*) as usage_count
        FROM games
        WHERE DATE(start_time) = CURRENT_DATE AND created_by = $1
        GROUP BY table_number
        ORDER BY usage_count DESC
        LIMIT 1
      ),
      top_drink AS (
        SELECT
          (jsonb_array_elements(items)->>'name') as drink_name,
          COUNT(*) as order_count
        FROM orders
        WHERE DATE(created_at) = CURRENT_DATE
        AND status = 'completed' AND created_by = $1
        GROUP BY drink_name
        ORDER BY order_count DESC
        LIMIT 1
      ),
      bar_revenue_today AS (
        SELECT COALESCE(SUM(total), 0) as revenue
        FROM orders
        WHERE DATE(created_at) = CURRENT_DATE
        AND status = 'completed' AND created_by = $1
      ),
      games_today AS (
        SELECT COUNT(*) as count
        FROM games
        WHERE DATE(start_time) = CURRENT_DATE
        AND status = 'completed' AND created_by = $1
      ),
      monthly_games_revenue AS (
        SELECT COALESCE(SUM(cost), 0) as revenue
        FROM games
        WHERE start_time >= DATE_TRUNC('month', CURRENT_DATE)
        AND status = 'completed' AND created_by = $1
      ),
      monthly_bar_revenue AS (
        SELECT COALESCE(SUM(total), 0) as revenue
        FROM orders
        WHERE created_at >= DATE_TRUNC('month', CURRENT_DATE)
        AND status = 'completed' AND created_by = $1
      ),
      bestseller AS (
        SELECT
          (jsonb_array_elements(items)->>'name') as item_name,
          COUNT(*) as sales_count
        FROM orders
        WHERE created_at >= NOW() - INTERVAL '7 days'
        AND status = 'completed' AND created_by = $1
        GROUP BY item_name
        ORDER BY sales_count DESC
        LIMIT 1
      )
      SELECT
        (SELECT active_count FROM active_game_tables) as active_tables,
        (SELECT utilization FROM table_utilization) as table_utilization,
        (SELECT avg_value FROM avg_order_value) as avg_order_value,
        (SELECT hour FROM peak_hours) as peak_hour,
        (SELECT table_number FROM most_used_table) as most_used_table,
        (SELECT drink_name FROM top_drink) as top_drink,
        (SELECT revenue FROM bar_revenue_today) as bar_revenue_today,
        (SELECT count FROM games_today) as games_today,
        (SELECT revenue FROM monthly_games_revenue) + (SELECT revenue FROM monthly_bar_revenue) as monthly_revenue,
        (SELECT revenue FROM monthly_games_revenue) as monthly_games_revenue,
        (SELECT revenue FROM monthly_bar_revenue) as monthly_bar_revenue,
        (SELECT item_name FROM bestseller) as bestseller
    `

    const performanceMetrics = await pool.query(performanceMetricsQuery, userParams)

    // Get today's stats
    const todayStatsQuery = isAdmin ? `
      WITH today_games AS (
        SELECT
          COUNT(*) as game_count,
          COALESCE(SUM(cost), 0) as games_revenue,
          AVG(duration) as avg_duration
        FROM games
        WHERE DATE(start_time) = CURRENT_DATE
        AND status = 'completed'
      ),
      today_orders AS (
        SELECT
          COUNT(*) as order_count,
          COALESCE(SUM(total), 0) as bar_revenue
        FROM orders
        WHERE DATE(created_at) = CURRENT_DATE
        AND status = 'completed'
      )
      SELECT
        COALESCE(g.games_revenue, 0) +
        COALESCE(o.bar_revenue, 0) as today_revenue,
        COALESCE(g.games_revenue, 0) as games_revenue,
        COALESCE(o.bar_revenue, 0) as bar_revenue,
        COALESCE(g.avg_duration, 0) as avg_duration,
        COALESCE(o.order_count, 0) as order_count
      FROM today_games g
      CROSS JOIN today_orders o
    ` : `
      WITH today_games AS (
        SELECT
          COUNT(*) as game_count,
          COALESCE(SUM(cost), 0) as games_revenue,
          AVG(duration) as avg_duration
        FROM games
        WHERE DATE(start_time) = CURRENT_DATE
        AND status = 'completed' AND created_by = $1
      ),
      today_orders AS (
        SELECT
          COUNT(*) as order_count,
          COALESCE(SUM(total), 0) as bar_revenue
        FROM orders
        WHERE DATE(created_at) = CURRENT_DATE
        AND status = 'completed' AND created_by = $1
      )
      SELECT
        COALESCE(g.games_revenue, 0) +
        COALESCE(o.bar_revenue, 0) as today_revenue,
        COALESCE(g.games_revenue, 0) as games_revenue,
        COALESCE(o.bar_revenue, 0) as bar_revenue,
        COALESCE(g.avg_duration, 0) as avg_duration,
        COALESCE(o.order_count, 0) as order_count
      FROM today_games g
      CROSS JOIN today_orders o
    `

    const todayStats = await pool.query(todayStatsQuery, userParams)

    return NextResponse.json({
      dailyRevenue: dailyRevenue.rows || [],
      hourlyUsage: hourlyUsage.rows || [],
      performanceMetrics: performanceMetrics.rows[0] || {
        active_tables: 0,
        table_utilization: 0,
        avg_order_value: 0,
        peak_hour: 'N/A',
        most_used_table: 'N/A',
        top_drink: 'N/A',
        bar_revenue_today: 0,
        games_today: 0,
        monthly_revenue: 0,
        monthly_games_revenue: 0,
        monthly_bar_revenue: 0,
        bestseller: 'N/A'
      },
      todayStats: todayStats.rows[0] || {
        today_revenue: 0,
        games_revenue: 0,
        bar_revenue: 0,
        avg_duration: 0,
        order_count: 0
      }
    })
  } catch (error: unknown) {
    console.error('Analytics error:', error)
    return NextResponse.json({ error: 'Failed to fetch analytics data' }, { status: 500 })
  }
}