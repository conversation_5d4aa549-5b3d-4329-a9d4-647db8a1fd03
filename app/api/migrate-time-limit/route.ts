import { NextRequest, NextResponse } from 'next/server'
import pool from '@/lib/db'
import { requireAuth } from '@/lib/auth'

export async function POST(request: NextRequest) {
  try {
    // Require admin authentication
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user || authResult.user.role !== 'admin') {
      return NextResponse.json({ error: "Admin authentication required" }, { status: 401 })
    }

    console.log('🔄 Starting time_limit column migration...')

    // Check if column already exists
    const columnCheck = await pool.query(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'games' AND column_name = 'time_limit'
    `)

    if (columnCheck.rows.length > 0) {
      console.log('✅ time_limit column already exists, skipping column creation')
    } else {
      console.log('📝 Adding time_limit column to games table...')
      // Add time_limit column to games table
      await pool.query(`
        ALTER TABLE games
        ADD COLUMN time_limit INTEGER
      `)
      console.log('✅ time_limit column added successfully')
    }

    // Add index for better performance
    await pool.query(`
      CREATE INDEX IF NOT EXISTS idx_games_time_limit ON games(time_limit)
    `)

    // Add comment for documentation
    await pool.query(`
      COMMENT ON COLUMN games.time_limit IS 'Time limit for the game in minutes. NULL means unlimited.'
    `)

    // Update existing active games to have unlimited time limit if not set
    await pool.query(`
      UPDATE games 
      SET time_limit = NULL 
      WHERE time_limit IS NULL AND status = 'active'
    `)

    // Add check constraint
    await pool.query(`
      ALTER TABLE games 
      ADD CONSTRAINT IF NOT EXISTS check_games_time_limit_positive 
      CHECK (time_limit IS NULL OR time_limit > 0)
    `)

    // Create helper functions
    await pool.query(`
      CREATE OR REPLACE FUNCTION get_game_time_remaining(game_id INTEGER)
      RETURNS INTEGER AS $$
      DECLARE
          game_start TIMESTAMP;
          game_limit INTEGER;
          elapsed_minutes INTEGER;
          remaining_minutes INTEGER;
      BEGIN
          SELECT start_time, time_limit 
          INTO game_start, game_limit
          FROM games 
          WHERE id = game_id AND status = 'active';
          
          IF game_start IS NULL OR game_limit IS NULL THEN
              RETURN NULL;
          END IF;
          
          elapsed_minutes := EXTRACT(EPOCH FROM (NOW() - game_start)) / 60;
          remaining_minutes := game_limit - elapsed_minutes;
          
          RETURN remaining_minutes;
      END;
      $$ LANGUAGE plpgsql
    `)

    await pool.query(`
      CREATE OR REPLACE FUNCTION is_game_over_time_limit(game_id INTEGER)
      RETURNS BOOLEAN AS $$
      DECLARE
          remaining_time INTEGER;
      BEGIN
          remaining_time := get_game_time_remaining(game_id);
          
          IF remaining_time IS NULL OR remaining_time > 0 THEN
              RETURN FALSE;
          END IF;
          
          RETURN TRUE;
      END;
      $$ LANGUAGE plpgsql
    `)

    // Create view for active games with time information
    await pool.query(`
      CREATE OR REPLACE VIEW active_games_with_time AS
      SELECT 
          g.*,
          EXTRACT(EPOCH FROM (NOW() - g.start_time)) / 60 AS elapsed_minutes,
          get_game_time_remaining(g.id) AS remaining_minutes,
          is_game_over_time_limit(g.id) AS is_over_limit,
          u.username as created_by_username,
          u.full_name as created_by_name,
          u.role as created_by_role
      FROM games g
      LEFT JOIN users u ON g.created_by = u.id
      WHERE g.status = 'active'
      ORDER BY g.start_time DESC
    `)

    console.log('✅ Time limit migration completed successfully')

    return NextResponse.json({ 
      success: true, 
      message: "Time limit column and functions added to games table successfully" 
    })

  } catch (error) {
    console.error('❌ Migration failed:', error)
    return NextResponse.json({ 
      error: "Migration failed", 
      details: error instanceof Error ? error.message : String(error) 
    }, { status: 500 })
  }
}
