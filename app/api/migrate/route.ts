import { NextRequest, NextResponse } from 'next/server'
import pool from '@/lib/db'

// POST /api/migrate - Run table user assignment migration (reservation system removed)
export async function POST(request: NextRequest) {
  try {
    console.log('🔄 Starting table user assignment migration...')

    // Add user assignment columns to gametables table if they don't exist
    await pool.query(`
      ALTER TABLE gametables
      ADD COLUMN IF NOT EXISTS assigned_user_id INTEGER,
      ADD COLUMN IF NOT EXISTS assigned_username VARCHAR(100)
    `)

    console.log('✅ Added user assignment columns to gametables table')

    // Add user assignment columns to tables table (for bar tables) if they don't exist
    await pool.query(`
      ALTER TABLE tables
      ADD COLUMN IF NOT EXISTS assigned_user_id INTEGER,
      ADD COLUMN IF NOT EXISTS assigned_username VARCHAR(100)
    `)

    console.log('✅ Added user assignment columns to tables table')

    // Create a simple index for performance
    try {
      await pool.query(`
        CREATE INDEX IF NOT EXISTS idx_gametables_assigned_user
        ON gametables(assigned_user_id)
        WHERE assigned_user_id IS NOT NULL
      `)
      console.log('✅ Created index on assigned_user_id')
    } catch (indexError) {
      console.log('ℹ️ Index may already exist or creation failed:', indexError)
    }

    return NextResponse.json({
      success: true,
      message: 'Table user assignment migration completed successfully (reservation system removed)',
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('❌ Migration failed:', error)
    return NextResponse.json(
      { 
        error: 'Migration failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// GET /api/migrate - Check migration status
export async function GET() {
  try {
    // Check if the user assignment columns exist in gametables
    const gameTablesResult = await pool.query(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'gametables'
      AND column_name IN ('assigned_user_id', 'assigned_username')
    `)

    // Check if the user assignment columns exist in tables
    const tablesResult = await pool.query(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'tables'
      AND column_name IN ('assigned_user_id', 'assigned_username')
    `)

    const gameTablesColumns = gameTablesResult.rows.map(row => row.column_name)
    const tablesColumns = tablesResult.rows.map(row => row.column_name)

    const requiredGameTablesColumns = ['assigned_user_id', 'assigned_username']
    const requiredTablesColumns = ['assigned_user_id', 'assigned_username']

    const missingGameTablesColumns = requiredGameTablesColumns.filter(col => !gameTablesColumns.includes(col))
    const missingTablesColumns = requiredTablesColumns.filter(col => !tablesColumns.includes(col))

    const allMissingColumns = [...missingGameTablesColumns, ...missingTablesColumns]

    return NextResponse.json({
      migrationNeeded: allMissingColumns.length > 0,
      gametables: {
        existingColumns: gameTablesColumns,
        missingColumns: missingGameTablesColumns
      },
      tables: {
        existingColumns: tablesColumns,
        missingColumns: missingTablesColumns
      },
      status: allMissingColumns.length === 0 ? 'complete' : 'pending'
    })
  } catch (error: unknown) {
    console.error('Error checking migration status:', error)
    return NextResponse.json(
      { error: 'Failed to check migration status' },
      { status: 500 }
    )
  }
}
