import { type NextRequest, NextResponse } from "next/server"
import pool from '@/lib/db'
import { requireAdmin } from '@/lib/auth'

export async function GET() {
  try {
    const result = await pool.query('SELECT * FROM tables ORDER BY number')
    return NextResponse.json(result.rows)
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: "Failed to fetch tables" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    // Require admin authentication
    const user = requireAdmin(request)
    if (!user) {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 })
    }

    const body = await request.json()
    const { number, name, isActive, hourlyRate, assignedUserId, assignedUsername } = body

    // Validate required fields
    if (!number || !name || hourlyRate === undefined) {
      return NextResponse.json({ error: "Number, name, and hourly rate are required" }, { status: 400 })
    }

    try {
      const result = await pool.query(
        'INSERT INTO tables (number, name, is_active, hourly_rate, assigned_user_id, assigned_username) VALUES ($1, $2, $3, $4, $5, $6) RETURNING *',
        [number, name, isActive !== undefined ? isActive : true, hourlyRate, assignedUserId, assignedUsername]
      )

      return NextResponse.json(result.rows[0], { status: 201 })
    } catch (insertError: unknown) {
      // If we get a unique constraint violation, try to fix the database schema
      if (insertError instanceof Error && insertError.message.includes('duplicate key value violates unique constraint "tables_number_key"')) {
        console.log('🔄 Detected old unique constraint, attempting to fix database schema...')

        try {
          // Remove old constraint and add new one
          await pool.query('ALTER TABLE tables DROP CONSTRAINT IF EXISTS tables_number_key')
          await pool.query(`
            ALTER TABLE tables ADD CONSTRAINT unique_table_number_per_user
            UNIQUE (number, assigned_user_id)
          `)

          console.log('✅ Fixed database constraint, retrying insert...')

          // Retry the insert
          const result = await pool.query(
            'INSERT INTO tables (number, name, is_active, hourly_rate, assigned_user_id, assigned_username) VALUES ($1, $2, $3, $4, $5, $6) RETURNING *',
            [number, name, isActive !== undefined ? isActive : true, hourlyRate, assignedUserId, assignedUsername]
          )

          return NextResponse.json(result.rows[0], { status: 201 })
        } catch (migrationError: unknown) {
          console.error('Failed to fix database constraint:', migrationError)
          return NextResponse.json({
            error: "Database constraint issue - please contact administrator",
            details: migrationError instanceof Error ? migrationError.message : 'Unknown error'
          }, { status: 500 })
        }
      } else {
        throw insertError
      }
    }
  } catch (error: unknown) {
    console.error('Database error:', error)
    return NextResponse.json({ error: "Failed to create table" }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Require admin authentication
    const user = requireAdmin(request)
    if (!user) {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 })
    }

    const body = await request.json()
    const { id, number, name, isActive, hourlyRate, assignedUserId, assignedUsername } = body

    // Validate required fields
    if (!id || !number || !name || hourlyRate === undefined) {
      return NextResponse.json({ error: "ID, number, name, and hourly rate are required" }, { status: 400 })
    }

    const result = await pool.query(
      'UPDATE tables SET number = $1, name = $2, is_active = $3, hourly_rate = $4, assigned_user_id = $5, assigned_username = $6 WHERE id = $7 RETURNING *',
      [number, name, isActive, hourlyRate, assignedUserId, assignedUsername, id]
    )

    if (result.rows.length === 0) {
      return NextResponse.json({ error: "Table not found" }, { status: 404 })
    }

    return NextResponse.json(result.rows[0])
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: "Failed to update table" }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Require admin authentication
    const user = requireAdmin(request)
    if (!user) {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 })
    }

    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json({ error: "ID is required" }, { status: 400 })
    }

    const result = await pool.query('DELETE FROM tables WHERE id = $1 RETURNING *', [id])

    if (result.rows.length === 0) {
      return NextResponse.json({ error: "Table not found" }, { status: 404 })
    }

    return NextResponse.json({ message: "Table deleted successfully" })
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: "Failed to delete table" }, { status: 500 })
  }
}