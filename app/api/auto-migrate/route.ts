import { NextRequest, NextResponse } from 'next/server'
import { runPendingMigrations, checkTimeLimitColumnExists } from '@/lib/migrations'

export async function GET(request: NextRequest) {
  try {
    console.log('🔄 Auto-migration check started...')
    
    // Check if time_limit column exists
    const hasColumn = await checkTimeLimitColumnExists()
    
    if (hasColumn) {
      console.log('✅ time_limit column already exists, no migration needed')
      return NextResponse.json({ 
        success: true, 
        message: 'Database is up to date',
        hasColumn: true,
        migrationNeeded: false
      })
    }
    
    console.log('📋 time_limit column missing, running auto-migration...')
    
    // Run pending migrations
    const result = await runPendingMigrations()
    
    if (result.success) {
      console.log('🎉 Auto-migration completed successfully')
      return NextResponse.json({
        success: true,
        message: 'Database migration completed successfully',
        hasColumn: true,
        migrationNeeded: true,
        executed: result.executed,
        failed: result.failed
      })
    } else {
      console.error('❌ Auto-migration failed')
      return NextResponse.json({
        success: false,
        message: 'Database migration failed',
        hasColumn: false,
        migrationNeeded: true,
        executed: result.executed,
        failed: result.failed
      }, { status: 500 })
    }
  } catch (error) {
    console.error('❌ Auto-migration system error:', error)
    return NextResponse.json({
      success: false,
      message: 'Auto-migration system error',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  // Same as GET but for explicit migration requests
  return GET(request)
}
