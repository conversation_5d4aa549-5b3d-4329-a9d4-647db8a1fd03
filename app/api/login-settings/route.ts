import { NextRequest, NextResponse } from 'next/server'
import pool from '@/lib/db'
import { requireAuth } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    // Require authentication
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: "Authentication required" }, { status: 401 })
    }

    // Only admin users can access login settings
    if (authResult.user.role !== 'admin') {
      return NextResponse.json({ error: "Admin access required" }, { status: 403 })
    }

    const result = await pool.query('SELECT * FROM login_settings ORDER BY id DESC LIMIT 1')

    if (result.rows.length === 0) {
      // Return default settings with new dynamic structure
      return NextResponse.json({
        waiters_section_enabled: true,
        waiters_section_title: 'Waiters Accounts:',
        waiters: [
          {
            id: '1',
            display_name: 'Waiter One:',
            username: 'waiter1',
            password: 'waiter1',
            enabled: true
          },
          {
            id: '2',
            display_name: 'Waiter Two:',
            username: 'waiter2',
            password: 'waiter2',
            enabled: true
          }
        ]
      })
    }

    const settings = result.rows[0]

    // Handle both old and new format
    if (settings.waiters && Array.isArray(settings.waiters)) {
      // New format - return only the new structure
      return NextResponse.json({
        waiters_section_enabled: settings.waiters_section_enabled,
        waiters_section_title: settings.waiters_section_title,
        waiters: settings.waiters
      })
    } else {
      // Legacy format - convert to new format
      const waiters = []
      if (settings.waiter1_enabled !== false) {
        waiters.push({
          id: '1',
          display_name: settings.waiter1_display_name || 'Waiter One:',
          username: settings.waiter1_username || 'waiter1',
          password: settings.waiter1_password || 'waiter1',
          enabled: settings.waiter1_enabled ?? true
        })
      }
      if (settings.waiter2_enabled !== false) {
        waiters.push({
          id: '2',
          display_name: settings.waiter2_display_name || 'Waiter Two:',
          username: settings.waiter2_username || 'waiter2',
          password: settings.waiter2_password || 'waiter2',
          enabled: settings.waiter2_enabled ?? true
        })
      }

      return NextResponse.json({
        waiters_section_enabled: settings.waiters_section_enabled ?? true,
        waiters_section_title: settings.waiters_section_title || 'Waiters Accounts:',
        waiters
      })
    }
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: "Failed to fetch login settings" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    // Require authentication
    const authResult = await requireAuth(request)
    console.log('🔐 Login settings POST - Auth result:', {
      success: authResult.success,
      user: authResult.user?.username,
      role: authResult.user?.role,
      error: authResult.error
    })

    if (!authResult.success || !authResult.user) {
      console.error('❌ Authentication failed:', authResult.error)
      return NextResponse.json({ error: authResult.error || "Authentication required" }, { status: 401 })
    }

    // Only admin users can modify login settings
    if (authResult.user.role !== 'admin') {
      console.error('❌ Access denied - user role:', authResult.user.role)
      return NextResponse.json({ error: "Admin access required" }, { status: 403 })
    }

    const body = await request.json()
    const { waiters_section_enabled, waiters_section_title, waiters } = body

    // Validate the new structure
    if (!Array.isArray(waiters)) {
      return NextResponse.json({ error: "Waiters must be an array" }, { status: 400 })
    }

    // Validate each waiter object
    for (const waiter of waiters) {
      if (!waiter.id || !waiter.display_name || !waiter.username || !waiter.password || typeof waiter.enabled !== 'boolean') {
        return NextResponse.json({ error: "Invalid waiter data structure" }, { status: 400 })
      }
    }

    // Check if settings exist
    const existingResult = await pool.query('SELECT id FROM login_settings LIMIT 1')

    if (existingResult.rows.length === 0) {
      // Insert new settings with dynamic waiters
      const result = await pool.query(`
        INSERT INTO login_settings (
          waiters_section_enabled,
          waiters_section_title,
          waiters,
          updated_at
        ) VALUES ($1, $2, $3, NOW())
        RETURNING *
      `, [
        waiters_section_enabled,
        waiters_section_title,
        JSON.stringify(waiters)
      ])
      return NextResponse.json(result.rows[0])
    } else {
      // Update existing settings with dynamic waiters
      const result = await pool.query(`
        UPDATE login_settings SET
          waiters_section_enabled = $1,
          waiters_section_title = $2,
          waiters = $3,
          updated_at = NOW()
        WHERE id = $4
        RETURNING *
      `, [
        waiters_section_enabled,
        waiters_section_title,
        JSON.stringify(waiters),
        existingResult.rows[0].id
      ])
      return NextResponse.json(result.rows[0])
    }
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: "Failed to save login settings" }, { status: 500 })
  }
}
