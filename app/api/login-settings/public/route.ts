import { NextRequest, NextResponse } from 'next/server'
import pool from '@/lib/db'

export async function GET(request: NextRequest) {
  try {
    const result = await pool.query('SELECT * FROM login_settings ORDER BY id DESC LIMIT 1')

    if (result.rows.length === 0) {
      // Return default settings with new dynamic structure
      return NextResponse.json({
        waiters_section_enabled: true,
        waiters_section_title: 'Waiters Accounts:',
        waiters: [
          {
            id: '1',
            display_name: 'Waiter One:',
            username: 'waiter1',
            password: 'waiter1',
            enabled: true
          },
          {
            id: '2',
            display_name: 'Waiter Two:',
            username: 'waiter2',
            password: 'waiter2',
            enabled: true
          }
        ],
        database_online: true
      })
    }

    const settings = result.rows[0]

    // Handle both old and new format
    if (settings.waiters && Array.isArray(settings.waiters)) {
      // New format - return only the new structure
      return NextResponse.json({
        waiters_section_enabled: settings.waiters_section_enabled,
        waiters_section_title: settings.waiters_section_title,
        waiters: settings.waiters,
        database_online: true
      })
    } else {
      // Legacy format - convert to new format
      const waiters = []
      if (settings.waiter1_enabled !== false) {
        waiters.push({
          id: '1',
          display_name: settings.waiter1_display_name || 'Waiter One:',
          username: settings.waiter1_username || 'waiter1',
          password: settings.waiter1_password || 'waiter1',
          enabled: settings.waiter1_enabled ?? true
        })
      }
      if (settings.waiter2_enabled !== false) {
        waiters.push({
          id: '2',
          display_name: settings.waiter2_display_name || 'Waiter Two:',
          username: settings.waiter2_username || 'waiter2',
          password: settings.waiter2_password || 'waiter2',
          enabled: settings.waiter2_enabled ?? true
        })
      }

      return NextResponse.json({
        waiters_section_enabled: settings.waiters_section_enabled ?? true,
        waiters_section_title: settings.waiters_section_title || 'Waiters Accounts:',
        waiters,
        database_online: true
      })
    }
  } catch (error) {
    console.error('Database error:', error)
    // Return settings with waiters section disabled when database is offline
    return NextResponse.json({
      waiters_section_enabled: false, // Hide waiters section when DB is offline
      waiters_section_title: 'Waiters Accounts:',
      waiters: [],
      database_online: false,
      database_error: error instanceof Error ? error.message : 'Database connection failed'
    })
  }
}
