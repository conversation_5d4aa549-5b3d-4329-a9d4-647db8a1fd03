import { type NextRequest, NextResponse } from "next/server"
import pool from '@/lib/db'
import { requireAuth } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    // Require authentication
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: "Authentication required" }, { status: 401 })
    }

    const user = authResult.user
    // Determine if user can see all data or only their own
    const isAdmin = user.role === 'admin'

    // Build query with complete user isolation
    // Each user sees only games from their assigned tables
    const query = isAdmin ? `
      SELECT
        g.*,
        u.username as created_by_username,
        u.full_name as created_by_name,
        u.role as created_by_role
      FROM games g
      LEFT JOIN users u ON g.created_by = u.id
      ORDER BY g.created_at DESC
    ` : `
      SELECT
        g.*,
        u.username as created_by_username,
        u.full_name as created_by_name,
        u.role as created_by_role
      FROM games g
      LEFT JOIN users u ON g.created_by = u.id
      INNER JOIN gametables gt ON g.table_number = gt.number
      WHERE gt.assigned_user_id = $1
      ORDER BY g.created_at DESC
    `

    const params = isAdmin ? [] : [user.id]
    const result = await pool.query(query, params)
    return NextResponse.json(result.rows)
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: "Failed to fetch games" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    // Require authentication
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: "Authentication required" }, { status: 401 })
    }

    const user = authResult.user
    const body = await request.json()
    const { tableNumber, startTime, timeLimit } = body

    // Convert timeLimit to database format (null for unlimited)
    const timeLimitValue = timeLimit === "unlimited" ? null : timeLimit

    const result = await pool.query(
      'INSERT INTO games (table_number, start_time, status, created_by, time_limit) VALUES ($1, $2, $3, $4, $5) RETURNING *',
      [tableNumber, new Date(startTime), 'active', user.id, timeLimitValue]
    )

    console.log(`✅ Game created with time limit: ${timeLimit} for table ${tableNumber}`)
    return NextResponse.json(result.rows[0], { status: 201 })
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: "Failed to create game" }, { status: 500 })
  }
}
