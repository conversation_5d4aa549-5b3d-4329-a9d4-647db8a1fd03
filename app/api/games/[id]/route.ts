import { type NextRequest, NextResponse } from "next/server"
import pool from '@/lib/db'
import { requireAuth } from '@/lib/auth'

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Require authentication
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: "Authentication required" }, { status: 401 })
    }

    const body = await request.json()
    const { endTime, duration, cost, status } = body
    const resolvedParams = await params
    const id = resolvedParams.id

    // Validate required fields
    if (!endTime || duration === undefined || cost === undefined || !status) {
      return NextResponse.json({ error: "End time, duration, cost, and status are required" }, { status: 400 })
    }

    const result = await pool.query(
      'UPDATE games SET end_time = $1, duration = $2, cost = $3, status = $4 WHERE id = $5 RETURNING *',
      [new Date(endTime), duration, cost, status, id]
    )

    if (result.rows.length === 0) {
      return NextResponse.json({ error: "Game not found" }, { status: 404 })
    }

    return NextResponse.json(result.rows[0])
  } catch (error) {
    console.error('Database error:', error)
    return NextResponse.json({ error: "Failed to update game" }, { status: 500 })
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Require authentication
    const authResult = await requireAuth(request)
    if (!authResult.success || !authResult.user) {
      return NextResponse.json({ error: "Authentication required" }, { status: 401 })
    }

    const body = await request.json()
    const { timeLimit } = body
    const resolvedParams = await params
    const id = resolvedParams.id

    console.log(`🔄 PATCH /api/games/${id} - Setting time limit to:`, timeLimit)

    // Validate time limit
    if (timeLimit === undefined) {
      return NextResponse.json({ error: "timeLimit is required" }, { status: 400 })
    }

    // Check if time_limit column exists
    const columnCheck = await pool.query(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'games' AND column_name = 'time_limit'
    `)

    if (columnCheck.rows.length === 0) {
      console.error('❌ time_limit column does not exist')
      return NextResponse.json({
        error: "Database not migrated",
        needsMigration: true
      }, { status: 500 })
    }

    // Update the time limit
    const result = await pool.query(
      'UPDATE games SET time_limit = $1 WHERE id = $2 RETURNING *',
      [timeLimit === "unlimited" ? null : timeLimit, id]
    )

    if (result.rows.length === 0) {
      return NextResponse.json({ error: "Game not found" }, { status: 404 })
    }

    console.log(`✅ Time limit updated successfully for game ${id}`)
    return NextResponse.json(result.rows[0])
  } catch (error) {
    console.error('❌ Database error in PATCH:', error)
    return NextResponse.json({ error: "Failed to update time limit" }, { status: 500 })
  }
}