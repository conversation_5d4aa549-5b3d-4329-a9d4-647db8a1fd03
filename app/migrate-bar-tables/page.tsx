'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'

export default function MigrateBarTablesPage() {
  const [result, setResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  const runMigration = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/migrate-bar-tables', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include'
      })

      const data = await response.json()
      setResult(data)
    } catch (error: unknown) {
      setResult({ error: error instanceof Error ? error.message : 'Unknown error' })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-4">Bar Tables Database Migration</h1>
      
      <div className="space-y-4">
        <p className="text-gray-600">
          This will migrate hardcoded bar tables to the database. 
          Run this once to populate the database with the existing tables.
        </p>
        
        <Button onClick={runMigration} disabled={loading}>
          {loading ? 'Running Migration...' : 'Migrate Bar Tables to Database'}
        </Button>
        
        {result && (
          <div className="mt-4 p-4 bg-gray-100 rounded">
            <h3 className="font-bold">Migration Result:</h3>
            <pre className="mt-2 text-sm overflow-auto">
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
        )}
      </div>
    </div>
  )
}
