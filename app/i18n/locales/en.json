{"common": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "add": "Add", "remove": "Remove", "search": "Search", "clear": "Clear", "all": "All", "active": "Active", "inactive": "Inactive", "yes": "Yes", "no": "No", "loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Info", "added": "Added", "advanced": "Advanced", "always": "Always", "amount": "Amount", "approved": "Approved", "apr": "Apr", "april": "April", "archived": "Archived", "aug": "Aug", "august": "August", "automatic": "Automatic", "available": "Available", "basic": "Basic", "business": "Business", "cancelled": "Cancelled", "changed": "Changed", "close": "Close", "closed": "Closed", "completed": "Completed", "confirm": "Confirm", "connected": "Connected", "created": "Created", "critical": "Critical", "custom": "Custom", "daily": "Daily", "date": "Date", "day": "Day", "days": "Days", "dec": "Dec", "december": "December", "default": "<PERSON><PERSON><PERSON>", "deleted": "Deleted", "description": "Description", "disabled": "Disabled", "disconnected": "Disconnected", "discount": "Discount", "downloaded": "Downloaded", "draft": "Draft", "enabled": "Enabled", "enterprise": "Enterprise", "expired": "Expired", "export": "Export", "failed": "Failed", "feb": "Feb", "february": "February", "filter": "Filter", "finished": "Finished", "free": "Free", "fri": "<PERSON><PERSON>", "friday": "Friday", "hidden": "Hidden", "high": "High", "hour": "Hour", "hourly": "Hourly", "hours": "Hours", "hoursShort": "h", "import": "Import", "invalid": "Invalid", "jan": "Jan", "january": "January", "jul": "Jul", "july": "July", "jun": "Jun", "june": "June", "lastMonth": "Last Month", "lastWeek": "Last Week", "lastYear": "Last Year", "later": "Later", "left": "left", "limited": "Limited", "loaded": "Loaded", "locked": "Locked", "low": "Low", "major": "Major", "manual": "Manual", "mar": "Mar", "march": "March", "may": "May", "medium": "Medium", "minor": "Minor", "minute": "Minute", "minutes": "Minutes", "minutesShort": "m", "modified": "Modified", "mon": "Mon", "monday": "Monday", "month": "Month", "monthly": "Monthly", "months": "Months", "name": "Name", "never": "Never", "new": "New", "nextMonth": "Next Month", "nextWeek": "Next Week", "nextYear": "Next Year", "none": "None", "normal": "Normal", "nov": "Nov", "november": "November", "now": "Now", "oct": "Oct", "october": "October", "of": "of", "offline": "Offline", "often": "Often", "ok": "OK", "online": "Online", "opened": "Opened", "optional": "Optional", "paid": "Paid", "paused": "Paused", "pending": "Pending", "personal": "Personal", "premium": "Premium", "price": "Price", "private": "Private", "processing": "Processing", "professional": "Professional", "public": "Public", "published": "Published", "quantity": "Quantity", "rarely": "Rarely", "received": "Received", "recently": "Recently", "refresh": "Refresh", "rejected": "Rejected", "removed": "Removed", "required": "Required", "reset": "Reset", "resumed": "Resumed", "sat": "Sat", "saturday": "Saturday", "saved": "Saved", "second": "Second", "seconds": "Seconds", "select": "Select", "sent": "<PERSON><PERSON>", "sep": "Sep", "september": "September", "sometimes": "Sometimes", "soon": "Soon", "started": "Started", "status": "Status", "stopped": "Stopped", "submit": "Submit", "subtotal": "Subtotal", "sun": "Sun", "sunday": "Sunday", "tax": "Tax", "thisMonth": "This Month", "thisWeek": "This Week", "thisYear": "This Year", "thu": "<PERSON>hu", "thursday": "Thursday", "time": "Time", "today": "Today", "tomorrow": "Tomorrow", "total": "Total", "tue": "<PERSON><PERSON>", "tuesday": "Tuesday", "unavailable": "Unavailable", "unlimited": "Unlimited", "unlocked": "Unlocked", "updated": "Updated", "uploaded": "Uploaded", "urgent": "<PERSON><PERSON>", "valid": "<PERSON><PERSON>", "visible": "Visible", "wed": "Wed", "wednesday": "Wednesday", "week": "Week", "weekly": "Weekly", "weeks": "Weeks", "year": "Year", "yearly": "Yearly", "years": "Years", "yesterday": "Yesterday"}, "navigation": {"dashboard": "Dashboard", "games": "Games", "bar": "Bar", "analytics": "Analytics", "settings": "Settings", "reports": "Reports", "about": "About", "contact": "Contact", "help": "Help", "home": "Home", "logout": "Logout", "menu": "<PERSON><PERSON>", "profile": "Profile", "support": "Support", "users": "Users"}, "games": {"title": "Games", "activeTables": "Active Tables", "liveControlMonitoring": "Live control & monitoring", "startGame": "Start Game", "stopGame": "Stop Game", "stop": "STOP", "table": "Table", "tableName": "Table Name", "tableNumber": "Table {{number}}", "tableShort": "T", "duration": "Duration", "player": "Player", "price": "Price", "cost": "Cost", "time": "Time", "end": "End", "date": "Date", "startTime": "Start Time", "endTime": "End Time", "now": "Now", "recently": "Recently", "unknown": "Unknown", "defaultTimeLimit": "Default Time Limit", "soundOn": "Sound On", "soundOff": "Sound Off", "makeAllAvailable": "Make All Available", "dailyReport": "Daily Report", "monthlyReport": "Monthly Report", "todaysSessions": "Today's Sessions", "timeLimitReached": "Time Limit Reached", "timeLimitReachedBody": "One or more billiard sessions have reached their time limit.", "noGamesToday": "No billiard sessions today", "gameSessionReceipt": "Game Session Receipt", "subtotalExclTax": "Subtotal (excl. tax)", "tax": "Tax ({{rate}}%)", "total": "Total", "unlimited": "Unlimited", "custom": "Custom", "oneHour": "1 Hour", "twoHours": "2 Hours", "threeHours": "3 Hours", "fourHours": "4 Hours", "fiveHours": "5 Hours", "sixHours": "6 Hours", "tables": "tables", "playing": "playing", "gamesCount": "sessions", "readyToPlay": "Ready to Play", "tapToStart": "Tap to Start", "perHour": "/hr", "change": "Change", "offline": "Offline", "contactStaff": "Contact Staff", "timesUp": "Time's Up!", "secondsLeft": "{{seconds}}s left", "minutesLeft": "{{minutes}}m left", "hoursMinutesLeft": "{{hours}}h {{minutes}}m left", "default": "<PERSON><PERSON><PERSON>", "noLimit": "No limit", "active": "active", "address": "Address", "albanianLek": "Albanian Lek", "auto": "Auto", "avgDurationMin": "Avg Duration (min)", "avgOrder": "Avg Order", "barIcon": "🍺 Bar", "barOrders": "BAR ORDERS", "barOrdersIcon": "🍺 Bar Orders", "barSalesAreLeading": "🍺 Bar sales are leading today", "bestPerformer": "Best Performer", "billiard": "<PERSON><PERSON><PERSON>", "billiardClub": "BILLIARD CLUB", "billiardGames": "BILLIARD GAMES", "businessHealthScore": "Business Health Score", "clearCache": "<PERSON>ache", "completed": "completed", "considerOptimizingGameDurations": "Consider optimizing game durations to improve table turnover.", "dailyTotal": "DAILY TOTAL", "dataWillAppearHere": "Data will appear here once transactions are recorded", "efficiency": "Efficiency", "errorLoadingComponent": "There was an error loading this component. This is often caused by corrupted browser data.", "excellent": "Excellent", "excellentDay": "🔥 Excellent day!", "excellentPerformance": "Excellent Performance!", "failedToInitialize": "Failed to initialize the application", "gamesAreDrivingRevenue": "🎱 Games are driving revenue today", "gamesIcon": "🎱 Games", "gamesSubtotal": "Games Subtotal", "gamesToday": "Games Today", "good": "Good", "growing": "Growing", "growingTrend": "📈 Growing", "high": "High", "insights": "Insights", "items": "items", "lastUpdated": "Last updated", "limit": "limit", "live": "LIVE", "low": "Low", "marketingSuggestion": "Marketing Suggestion", "medium": "Medium", "month": "Month", "monthlyRevenue": "Monthly Revenue", "monthlyTotal": "MONTHLY TOTAL", "mostUsedTable": "Most Used Table", "needsAttention": "📉 Needs attention", "needsImprovement": "Needs Improvement", "noDataAvailableForTimeRange": "No {{timeRange}} data available", "noGamesCompletedThisMonth": "No games completed this month", "noGamesCompletedToday": "No games completed today", "noOrdersCompletedThisMonth": "No orders completed this month", "noOrdersCompletedToday": "No orders completed today", "normalTime": "📊 Normal", "off": "OFF", "opportunity": "Opportunity", "optimalTiming": "⚡ Optimal timing", "optimizationOpportunity": "Optimization Opportunity", "ordersToday": "Orders Today", "overallHealthScore": "Overall Health Score", "overview": "Overview", "peakHour": "Peak Hour", "peakPerformance": "⚡ Peak Performance", "peakTimeNow": "⚡ Peak time now!", "performance": "Performance", "planForPeak": "📈 Plan for peak", "ready": "READY", "realTimeInsights": "Real-time insights", "receiptNumber": "Receipt #", "refresh": "Refresh", "refreshPage": "Refresh Page", "reloadPage": "Reload Page", "reportGenerated": "Report generated", "revenue": "Revenue", "revenueBreakdown": "💰 Revenue Breakdown", "revenueInsights": "Revenue Insights", "revenueIsUp": "Revenue is up {changeText}. Keep up the great work!", "revenueOverview": "Revenue Overview", "revenueTrend": "Revenue Trend", "roomForImprovement": "🎯 Room for improvement", "rushHourNow": "🔥 Rush Hour Now!", "scanForContactInfo": "Scan for contact info", "scanForMoreInfo": "Scan for more info", "servedBy": "Served by", "sine": "sine", "smartAnalytics": "Smart Analytics", "smartRecommendations": "Smart Recommendations", "somethingWentWrong": "Something went wrong", "stable": "Stable", "status": {"free": "Free", "held": "Held", "occupied": "Occupied"}, "tableUtilizationIsLow": "Table utilization is low. Consider running promotions during off-peak hours.", "thankYouForPlaying": "Thank you for playing!", "today": "Today", "todaysRevenueByHour": "Today's Revenue by Hour", "topItem": "Top Item", "utilization": "Utilization", "vat": "VAT", "visitUsAgainSoon": "Visit us again soon", "week": "Week", "weeklyRevenue": "Weekly Revenue"}, "bar": {"title": "Bar", "selectTable": "Select Table", "menu": "<PERSON><PERSON>", "addToOrder": "Add to Order", "placeOrder": "Place Order", "orderTotal": "Order Total", "onHold": "on hold", "clearAll": "Clear All", "pending": "Pending", "clear": "Clear", "selectTableToOrder": "Select a table to start ordering", "tablesOnHold": "tables on hold", "currentOrder": "Current Order", "submitOrderAndPrint": "Submit Order & Print", "todaysOrders": "Today's Orders", "noOrdersToday": "No orders today", "noOrdersCompletedToday": "No orders completed today", "smartBar": "Smart Bar", "activeTables": "active tables", "utilization": "utilization", "smartMenu": "Smart Menu", "items": "items", "reports": "Daily Reports", "monthlyReports": "Monthly Reports", "smartAnalytics": "Smart Analytics", "realTimeInsights": "Real-time insights", "lastUpdated": "Last updated", "todaysRevenueByHour": "Today's Revenue by Hour", "weeklyRevenue": "Weekly Revenue", "searchMenuItems": "Search menu items...", "all": "All", "quick": "Quick", "popular": "Popular", "recent": "Recent", "selectTableToStartOrdering": "Select a Table", "chooseTableToStartOrdering": "Choose a table to start ordering", "chooseFromAvailableTables": "Choose from {{count}} available tables above", "noItemsFound": "No items found", "noItemsMatch": "No items match \"{{query}}\"", "noItemsInCategory": "No items in this category", "clearSearch": "Clear search", "noMenuItems": "No menu items", "addProductsInSettings": "Add products in Settings", "addItemsFromMenu": "Add items from the menu", "tableReady": "Table {{number}} is ready", "each": "each", "avgPerItem": "Avg {{amount}}L/item", "table": "Table", "unknown": "Unknown", "item": "item", "dailyReport": "Daily Report", "monthlyReport": "Monthly Report", "chooseWhatToInclude": "Choose what to include in the {{reportType}} report:", "gamesOnly": "Games Only", "ordersOnly": "Orders Only", "bothGamesAndBar": "Both Games and Bar", "cancel": "Cancel", "generateReport": "Generate Report", "barOrderReceipt": "Bar Order Receipt", "receipt": "Receipt", "date": "Date", "subtotalExclTax": "Subtotal (excl. tax)", "tax": "Tax", "total": "Total", "thankYouForOrder": "Thank you for your order!", "enjoyYourDrinks": "Enjoy your drinks!", "scanForMoreInfo": "Scan for more info", "scanForContactInfo": "Scan for contact info", "drinks": "Drinks", "snacks": "Snacks", "food": "Food", "albanianLek": "Albanian Lek", "completed": "completed", "active": "Active", "inactive": "Inactive", "addFirstMenuItem": "Add your first menu item to get started", "addFirstTableToGetStarted": "Add your first table to get started", "addTable": "Add Table", "address": "Address", "allTablesWillBeCreatedBasedOnSetting": "All tables will be created as active/inactive based on this setting", "allUsers": "All Users", "assignToUser": "Assign to User", "assignedTo": "Assigned to", "avgItems": "avg items", "barMenuUpdated": "Bar Menu (Updated with New Storage System)", "barOrders": "BAR ORDERS", "barReportOnly": "Bar Report Only", "barReportOnlyIcon": "🍺 Bar Report Only", "barTables": "Bar Tables", "billiardGames": "BILLIARD GAMES", "bothGamesBarIcon": "📊 Both (Billiard + Bar)", "category": "Category", "checkTablesInSettings": "Check if you have tables assigned to your user in Settings", "chooseWhatToIncludeDaily": "Choose what to include in your daily report:", "coffee": "Coffee", "configureBilliardGameSettings": "Configure billiard game settings and table management", "confirmDeleteGameTable": "Are you sure you want to delete \"{{name}}\" (Table #{{number}})?\\n\\nThis action cannot be undone.", "confirmDeleteGameTables": "Are you sure you want to delete {{count}} game table(s)?\\n\\nTables: {{tables}}\\n\\nThis action cannot be undone.", "confirmDeleteTable": "Are you sure you want to delete \"{{name}}\"? This action cannot be undone.", "contactAdminForTables": "Contact admin to assign tables to your account", "createTables": "Create {{count}} Tables", "daily": "Daily", "dailyBarReport": "Daily Bar Report", "dailyTotal": "DAILY TOTAL:", "editBarTable": "Edit Bar Table", "ensureTablesActive": "Ensure tables are marked as \"Active\"", "enterTableName": "Enter table name", "failedToAddGameTableWithError": "Failed to add game table: {{error}}", "failedToCreateAnyTables": "Failed to create any tables", "failedToCreateTables": "Failed to create tables", "failedToDeleteGameTable": "Failed to delete game table {{tableId}}: {{error}}", "failedToDeleteGameTableWithError": "Failed to delete game table: {{error}}", "failedToDeleteProductWithError": "Failed to delete product {{productId}}: {{error}}", "failedToDeleteTable": "Failed to delete table: {{error}}", "failedToDeleteTableGeneric": "Failed to delete table", "failedToFixTableNames": "Failed to fix table names. Please try again.", "failedToSaveBusinessInfo": "Failed to save business info", "failedToSaveCurrencySettings": "Failed to save currency settings", "failedToSaveGameSettings": "Failed to save game settings", "failedToSaveLoginSettings": "Failed to save login settings", "failedToUpdateGameTableWithError": "Failed to update game table: {{error}}", "gamesReportOnly": "Billiard Report Only", "gamesReportOnlyIcon": "🎱 Billiard Report Only", "gamesSubtotal": "Games Subtotal:", "loginSettingsSavedSuccessfully": "Login settings saved successfully!", "manageMenuItemsPricingAvailability": "Manage menu items, pricing, and availability", "monthly": "Monthly", "monthlyBarReport": "Monthly Bar Report", "monthlyTotal": "MONTHLY TOTAL:", "noBarTablesFound": "No bar tables found", "noGamesCompletedThisMonth": "No games completed this month", "noGamesCompletedToday": "No games completed today", "noItemsInOrder": "No items in order", "noOrdersCompletedThisMonth": "No orders completed this month", "noProductsFound": "No products found", "noProductsMatch": "No products match your search", "noTablesAssigned": "No tables are assigned to your user or available for use.", "noTablesAvailable": "No Tables Available", "onlyAssignedUserCanSeeTable": "Only assigned user can see this table", "order": "Order", "orderDetails": "Order Details", "ordersSubtotal": "Orders Subtotal:", "printDailyReport": "Print Daily Report", "printMonthlyReport": "Print Monthly Report", "printReceipt": "Print Receipt", "reportGenerated": "Report generated:", "saveChanges": "Save Changes", "search": "Search", "searchItems": "Search items...", "searchProducts": "Search products...", "searchTablesByNameOrNumber": "Search tables by name or number...", "selectReportType": "Select Report Type", "selectTableFirst": "Select a table first", "selectUser": "Select user", "sharedNoSpecificUser": "Shared (no specific user)", "successfullyCreatedGameTables": "Successfully created {{count}} game tables", "successfullyUpdatedTableNames": "Successfully updated {{count}} table names to consistent format!", "tableDeletedSuccessfully": "Table \"{{name}}\" deleted successfully", "tableName": "Table Name", "tableNamePrefix": "Table Name Prefix", "tableNumber": "Table Number", "tableNumberAlreadyInUse": "Table number {{number}} is already in use. Each table must have a unique number globally.", "tel": "Tel", "tryAdjustingSearch": "Try adjusting your search or filters", "unassigned": "Unassigned", "vat": "VAT"}, "settings": {"title": "Settings", "businessInfo": "Business Information", "currency": "<PERSON><PERSON><PERSON><PERSON>", "gameSettings": "Billiard Settings", "loginSettings": "<PERSON><PERSON>", "language": "Language", "selectLanguage": "Select Language", "english": "English", "albanian": "Albanian", "categories": "Categories", "tables": "Tables", "barMenu": "Bar Menu", "permissions": "Permissions", "barCategories": "Bar Categories", "organizeMenuItemsIntoCategories": "Organize your menu items into categories", "organizeMenuItems": "Organize menu items", "addCategory": "Add Category", "active": "Active", "inactive": "Inactive", "edit": "Edit", "businessName": "Business Name", "enterBusinessName": "Enter business name", "businessAddress": "Business Address", "enterBusinessAddress": "Enter business address", "businessPhone": "Business Phone", "enterBusinessPhone": "Enter business phone", "businessEmail": "Business Email", "enterBusinessEmail": "Enter business email", "currencyName": "Currency Name", "currencyUsedInBusiness": "The currency used in your business", "currencySymbol": "Currency Symbol", "symbolDisplayedWithPrices": "Symbol displayed with prices (e.g., L, €, $)", "taxRate": "Tax Rate (%)", "taxPercentageApplied": "Tax percentage applied to all sales", "showDecimals": "Show Decimals", "displayPricesWithDecimals": "Display prices with .00 (e.g., 50.00 L vs 50 L)", "defaultHourlyRate": "Default Hourly Rate (L)", "minimumGameTime": "Minimum Game Time (minutes)", "autoEndAfterHours": "Auto-end After (hours)", "autoPrintReceipts": "Auto-print receipts when games end", "saveSettings": "Save Settings", "saveUpdateAllTables": "Save & Update All Tables", "manageBarTablesAndSeating": "Manage bar tables and seating arrangements", "accessDenied": "Access Denied", "accessSettings": "Access Settings", "accessSettingsDescription": "Can access settings menu", "activate": "Activate", "activeTable": "Active Table", "activeTables": "Active Tables", "add": "Add", "addFirstTableToStart": "Add your first table to get started", "addGameTable": "Add Game Table", "addItem": "Add Item", "addNewBarCategory": "Add New Bar Category", "addNewBarMenuItem": "Add New Bar Menu Item", "addNewGameTable": "Add New Game Table", "addNewMenuItemDescription": "Add a new item to your bar menu", "addNewTable": "Add New Table", "addProduct": "Add Product", "addQrCodeToReceipts": "Add a QR code to all printed receipts", "address": "Address", "adminOnly": "Admin access required", "admins": "Admins", "albanianLek": "Albanian Lek", "all": "All", "allCategories": "All Categories", "allProducts": "All Products", "allTablesWillBeAssigned": "All tables will be assigned to this user. Leave unassigned for shared tables.", "allowed": "Allowed", "analytics": "Analytics", "assignToUser": "Assign to User", "assigned": "Assigned", "autoGeneratedUniqueNumber": "Auto-generated unique number", "availableForGames": "Available for Games", "barCategoriesManagement": "Bar Categories Management", "barMenuManagement": "Bar Menu Management", "barOrders": "Bar Orders", "barTables": "Bar Tables", "billiard": "Billiard", "billiardClub": "BILLIARD CLUB", "briefProductDescription": "Brief description of the product", "britishPound": "British Pound", "bulkCreateTables": "Bulk Create Tables", "businessInfoError": "Failed to save business information", "businessInfoSaved": "Business information saved successfully", "businessInformation": "Business Information", "cacheManagement": "Cache Management", "category": "Category", "categoryKey": "Category Key", "categoryKeyPlaceholder": "Enter category key", "categoryLabel": "Category Label", "categoryLabelPlaceholder": "Enter category label", "categoryManagement": "Category Management", "clear": "Clear", "clickToGenerateNew": "Click to generate new", "clickToSelect": "Click to select this sound", "clickToUse": "Click to use", "configureGameRulesAndPricing": "Configure game rules and pricing", "configureWaiterLoginAccounts": "Configure waiter login accounts", "confirmDeleteProduct": "Are you sure you want to delete \"{{name}}\"?\\n\\nThis action cannot be undone.", "confirmDeleteProducts": "Are you sure you want to delete {{count}} product(s)?\\n\\nProducts: {{products}}\\n\\nThis action cannot be undone.", "costPerHourForTable": "Cost per hour for this table", "createEditManageUserAccounts": "Create, edit, and manage user accounts and their roles. Control access permissions and user settings.", "createMultipleTablesAtOnce": "Create multiple tables at once", "mustBeGloballyUnique": "Must be globally unique", "bulkCountMustBeBetween1And50": "Bulk count must be between 1 and 50", "tablePrefixRequiredForBulkCreation": "Table prefix is required for bulk creation", "failedToUpdateTable": "Failed to update table", "numberOfTables": "Number of Tables", "create1To50TablesMax50": "Create 1-50 tables (max 50)", "generateNewNumber": "Generate new number", "waiters": "Waiters", "addWaiter": "<PERSON><PERSON>", "waiterSettings": "Waiter <PERSON><PERSON>", "waiterNew": "Waiter {number}", "waiterDisplayNamePlaceholder": "Waiter Name", "waiterUsernamePlaceholder": "username", "waiterPasswordPlaceholder": "password", "cannotRemoveLastWaiter": "Cannot remove the last waiter", "confirmRemoveWaiter": "Are you sure you want to remove '{name}'?", "createNewCategory": "Create a new category for organizing menu items", "createNewGameTableDescription": "Create a new game table for billiard sessions", "createNewGames": "Create New Games", "createNewGamesDescription": "Can start new game sessions", "createNewTableDescription": "Create a new table for your establishment", "createOrders": "Create Orders", "createOrdersDescription": "Can create new bar orders", "currencySettings": "<PERSON><PERSON><PERSON><PERSON>", "currencySettingsError": "Failed to save currency settings", "currencySettingsSaved": "Currency settings saved successfully", "currentlyAssigned": "Currently Assigned Sound", "currentlySelected": "currently selected", "customAlertSound": "Custom Alert Sound", "database": "Database", "deactivate": "Deactivate", "default": "<PERSON><PERSON><PERSON>", "deleteSound": "Delete sound", "denied": "Denied", "displayName": "Display Name", "displayNameForTable": "Display name for this table", "displayOrder": "Display Order", "editAnyOrder": "Edit Any Order", "editAnyOrderDescription": "Can modify any bar order", "editCategory": "Edit Category", "editGameTable": "Edit Game Table", "editProduct": "Edit Product", "email": "Email", "emailAddress": "<EMAIL>", "enableTax": "Enable Tax", "enableWaitersAccountsSection": "Enable Waiters Accounts Section", "enterEmailAddress": "Enter email address", "enterPhoneNumber": "Enter phone number", "enterVatNumber": "Enter VAT number", "euro": "Euro", "failedToAddTable": "Failed to add table", "failedToDeleteProduct": "Failed to delete product", "failedToDeleteSound": "Failed to delete sound file", "failedToDeleteTable": "Failed to delete table", "failedToPlaySound": "Failed to play sound. Please check the file", "failedToUploadSound": "Failed to upload sound file", "fix": "Fix", "gameManagement": "Game Management", "gameSettingsError": "Failed to save billiard settings", "gameSettingsSaved": "Billiard settings saved successfully", "gameTableNamePlaceholder": "Enter table name", "gameTables": "Game Tables", "gameTablesCount": "Game Tables", "generalGameSettings": "General Game Settings", "hideSoundLibrary": "Hide Sound Library", "hourlyRate": "Hourly Rate", "hourlyRateLabel": "Hourly Rate:", "includeQrCodeOnReceipts": "Include QR Code on Receipts", "invalidSoundFile": "Invalid sound file. Please upload an audio file (MP3, WAV, OGG, M4A, WebM, AAC)", "itemsCount": "{{count}} items", "localStorage": "Local Storage", "loginSettingsError": "Failed to save login settings", "loginSettingsSaved": "Login settings saved successfully", "loginSettingsTitle": "<PERSON><PERSON>", "manageBilliardTables": "Manage billiard tables", "managePermissions": "Manage Permissions", "managePermissionsDescription": "Can modify user permissions", "manageUserAccountsAndRoles": "Manage user accounts and roles", "manageUsers": "Manage Users", "manageUsersDescription": "Can create, edit, and delete users", "noGameTablesFound": "No game tables found", "noGameTablesMatch": "No game tables match the current filters", "noPermission": "You don't have permission to access settings", "noProductsFound": "No products found", "noProductsMatch": "No products match the current filters", "noSoundFilesUploaded": "No sound files uploaded yet. Upload your first sound file above", "noSpecificUserShared": "No specific user (shared)", "noTablesFound": "No tables found", "noTablesMatchFilters": "No tables match the current filters", "noTablesMatchSearch": "No tables match your search", "onlyAssignedUserWillSee": "Only the assigned user will be able to see and use this table. Leave unassigned for shared tables.", "openUserManagement": "Open User Management", "order": "Order", "password": "Password", "password1Placeholder": "password1", "password2Placeholder": "password2", "permissionsDescription": "Control what different user roles can see and do in the system.", "permissionsManagement": "Permissions Management", "phone": "Phone", "phoneNumber": "+355 69 123 4567", "pool": "Pool", "preview": "Preview", "previouslyUploadedSounds": "Previously Uploaded Sounds", "price": "Price", "productCategory": "Product Category", "productDescription": "Product Description", "productName": "Product Name", "productPrice": "Product Price", "qrCodeSettings": "QR Code Settings", "qrCodeUrl": "QR Code URL", "qrCodeUrlDescription": "Enter the URL that customers will be redirected to when they scan the QR code. Leave empty to show business contact information instead.", "qrCodeUrlPlaceholder": "https://example.com", "rate": "Rate", "removeSound": "Remove", "reset": "Reset", "save": "Save", "saveBusinessInformation": "Save Business Information", "saveCurrencySettings": "Save <PERSON><PERSON><PERSON><PERSON>", "saveLoginSettings": "Save Login <PERSON>tings", "searchTables": "Search tables...", "sectionTitle": "Section Title", "selectAll": "Select all", "selectAllGameTables": "Select All Game Tables", "selectCurrency": "Select currency", "selectTableType": "Select table type", "selected": "Selected", "settingsLoadedFromDatabase": "Settings loaded from database and will persist across devices", "settingsPermissions": "Settings", "shared": "Shared", "showQuickLoginButtons": "Show quick login buttons for waiters on the login page", "showSoundLibrary": "Show Sound Library", "snooker": "Snooker", "someProductsCouldNotBeDeleted": "Some products could not be deleted", "someTablesCouldNotBeDeleted": "Some tables could not be deleted", "soundFileDescription": "Upload an audio file (MP3, WAV, OGG, M4A, WebM, AAC) up to 5MB, or enter a URL to an online sound file", "soundFileStoredLocally": "This file is stored locally in your app", "soundFileTooLarge": "Sound file too large. Maximum size is 5MB", "soundUploadedSuccessfully": "Sound uploaded successfully", "statusLabel": "Status:", "stopAnyGame": "Stop Any Game", "stopAnyGameDescription": "Can stop any active game table", "swissFranc": "Swiss Franc", "tableAddedSuccessfully": "Table '{{name}}' added successfully", "tableAvailableWhenActive": "Table is available for games when active", "tableName": "Table Name", "tableNumber": "Table Number", "tableNumberLabel": "Table Number", "tableType": "Table Type", "tableTypeLabel": "Table Type:", "tableUpdatedSuccessfully": "Table '{{name}}' updated successfully", "tablesSelected": "{{count}} table(s) selected", "taxExcludedNote": "Product prices exclude tax (tax will be added at checkout)", "taxId": "K12345678A", "taxIncludedInPrices": "Tax Included in Product Prices", "taxIncludedNote": "Product prices include tax (tax will be shown separately on receipts)", "testSound": "Test Sound", "tiranAlbania": "Tiran, Albania", "titleDisplayedAboveButtons": "The title displayed above the waiter login buttons", "totalTables": "Total Tables", "tryAdjustingSearch": "Try adjusting your search", "turnTaxCalculationOnOff": "Turn tax calculation on/off for all sales", "type": "Type", "typeOfGameTable": "Type of game table", "updateAllTablesNote": "\"Update All Tables\" will apply the new hourly rate to all existing game tables", "updateCategory": "Update Category", "updateGameTable": "Update Game Table", "updateProduct": "Update Product", "uploadSoundFile": "Upload a sound file or enter URL", "uploadingSoundFile": "Uploading sound file...", "usDollar": "US Dollar", "useThisSound": "Use this sound", "userManagement": "User Management", "userManagementCenter": "User Management Center", "username": "Username", "vatNumber": "VAT Number", "viewAllHistory": "View All History", "viewAllHistoryDescription": "Can see game history from all users", "viewAllOrders": "View All Orders", "viewAllOrdersDescription": "Can see all bar orders from all users", "viewAllTables": "View All Tables", "viewAllTablesDescription": "Can see all active game tables from all users", "viewAnalytics": "View Analytics", "viewAnalyticsDescription": "Can access analytics dashboard", "viewOwnOrdersOnly": "View Own Orders Only", "viewOwnOrdersOnlyDescription": "Can only see bar orders they created", "viewOwnStats": "View Own Stats", "viewOwnStatsDescription": "Can see personal performance statistics", "viewOwnTablesOnly": "View Own Tables Only", "viewOwnTablesOnlyDescription": "Can only see game tables they created", "waiter1Placeholder": "waiter1", "waiter2Placeholder": "waiter2", "waiterOne": "Waiter 1", "waiterOneSettings": "Waiter 1 Setting<PERSON>", "adminPermissions": "Admin Permissions", "waiterPermissions": "Waiter Permissions", "waiterTwo": "Waiter 2", "waiterTwoSettings": "Waiter 2 Set<PERSON><PERSON>", "waitersAccounts": "Waiter Accounts", "website": "www.billiardclub.al"}, "analytics": {"title": "Analytics", "dashboard": "Dashboard", "overview": "Overview", "revenue": "Revenue", "performance": "Performance", "insights": "Insights", "todaysRevenueByHour": "Today's Revenue by Hour", "weeklyRevenue": "Weekly Revenue", "monthlyRevenue": "Monthly Revenue", "revenueOverview": "Revenue Overview", "tableUsageByHour": "Table Usage by Hour", "performanceMetrics": "Performance Metrics", "noDataAvailable": "No data available", "accessDenied": "Access Denied", "activeTables": "Active Tables", "averageGameDuration": "Average Game Duration", "avgGameDuration": "Avg Session Duration", "avgOrderValue": "Avg Order Value", "barOrders": "Bar Orders", "barPerformance": "🍺 Bar Performance", "barRevenue": "🍺 Bar Revenue", "barRevenueToday": "Bar Revenue Today", "bestseller": "Bestseller", "consistent": "Consistent", "dailyOverview": "📅 Daily Overview", "gameRoomAnalytics": "🎱 Billiard Room Analytics", "games": "Billiard", "gamesRevenue": "🎱 Billiard Revenue", "gamesToday": "Billiard Sessions Today", "loadError": "Failed to load analytics data", "monthlyReport": "📈 Monthly Report", "mostUsedTable": "Most Used Table", "na": "N/A", "noPermission": "You don't have permission to view analytics", "orders": "Orders", "ordersToday": "Orders Today", "peakActivity": "Peak Activity", "peakHour": "Peak Hour", "peakHours": "Peak Hours", "performanceMetricsTitle": "📊 Performance Metrics", "tableNA": "Table N/A", "tableUsageDataWillAppear": "Table usage data will appear here once billiard sessions and orders are created", "tableUtilization": "Table Utilization", "tables": "tables", "todaysRevenue": "Today's Revenue", "topDrink": "Top Drink", "totalRevenue": "Total Revenue"}, "errors": {"networkError": "Network connection error", "serverError": "Server error occurred", "validationError": "Validation error", "permissionDenied": "Permission denied", "notFound": "Not found", "quotaExceeded": "Storage quota exceeded", "offlineMode": "You are currently offline"}, "success": {"saved": "Successfully saved", "deleted": "Successfully deleted", "synced": "Successfully synchronized", "cacheCleared": "<PERSON><PERSON> cleared successfully"}, "activeGames": {"active": "active", "noGamesYet": "No billiard sessions yet", "recentCompleted": "Recent Completed", "recently": "Recently", "startGameToSeeActivity": "Start a billiard session to see activity", "title": "Active Billiard"}, "auth": {"accountCreated": "Account created successfully", "alreadyHaveAccount": "Already have an account?", "changePassword": "Change Password", "confirmPassword": "Confirm Password", "createAccount": "Create Account", "currentPassword": "Current Password", "dontHaveAccount": "Don't have an account?", "email": "Email", "forgotPassword": "Forgot Password?", "invalidEmail": "Invalid email address", "login": "<PERSON><PERSON>", "loginButton": "<PERSON><PERSON>", "loginError": "Invalid username or password", "logout": "Logout", "logoutButton": "Logout", "newPassword": "New Password", "password": "Password", "passwordChanged": "Password changed successfully", "passwordMismatch": "Passwords do not match", "passwordReset": "Password reset successfully", "passwordTooShort": "Password must be at least 6 characters", "rememberMe": "Remember Me", "resetPassword": "Reset Password", "sessionExpired": "Session expired. Please login again.", "signIn": "Sign In", "signUp": "Sign Up", "username": "Username", "waitersAccounts": "Waiter Accounts"}, "dashboard": {"activeGames": "Active Billiard", "activeTables": "Active Tables", "averageOrderValue": "Average Order Value", "completedOrders": "Completed Orders", "customerSatisfaction": "Customer Satisfaction", "dailyRevenue": "Daily Revenue", "monthlyRevenue": "Monthly Revenue", "overview": "Overview", "performanceMetrics": "Performance Metrics", "quickStats": "Quick Stats", "recentActivity": "Recent Activity", "recentTransactions": "Recent Transactions", "salesTrend": "Sales Trend", "title": "BBM", "todaysOrders": "Today's Orders", "todaysSessions": "Today's Sessions", "topSellingItems": "Top Selling Items", "totalCustomers": "Total Customers", "totalRevenue": "Total Revenue", "totalSales": "Total Sales", "weeklyRevenue": "Weekly Revenue", "welcome": "Welcome"}, "database": {"offline": "Offline", "offlineMessage": "Database is currently unavailable. Some features may be limited.", "online": "Online"}, "hardcoded": {"albanianLek": "Albanian Lek", "applicationJson": "application/json", "barBilardo": "Bar-Bilardo", "billiardClub": "BILLIARD CLUB", "contentType": "Content-Type", "courierNew": "Courier New", "date": "Date", "hoursLeft": "{{hours}}h left", "left": "left", "libreBarcode": "Libre Barcode 39", "minutesLeft": "{{minutes}}m left", "now": "Now", "player": "Player", "recently": "Recently", "scanForContactInfo": "Scan for contact info", "scanForMoreInfo": "Scan for more info", "secondsLeft": "{{seconds}}s left", "system": "System", "systemAutoPrint": "System Auto-Print", "timesUp": "Time's Up!", "total": "TOTAL"}, "profile": {"cancel": "Cancel", "changePassword": "Change Password", "changePhoto": "Change Photo", "confirmNewPassword": "Confirm New Password", "currentPassword": "Current Password", "enterConfirmNewPassword": "Confirm your new password", "enterCurrentPassword": "Enter your current password", "enterFullName": "Enter your full name", "enterNewPassword": "Enter your new password", "enterUsername": "Enter your username", "fullName": "Full Name", "newPassword": "New Password", "passwordFieldsNote": "Password must be at least 8 characters long", "profile": "Profile", "profileInformation": "Profile Information", "saveChanges": "Save Changes", "security": "Security", "userProfile": "User Profile", "username": "Username", "users": "Users"}, "receipt": {"amount": "Amount", "date": "Date", "duration": "Duration", "gameReceipt": "Game Receipt", "orderReceipt": "Order Receipt", "print": "Print", "receipt": "Receipt", "receiptNumber": "Receipt #", "reprint": "Reprint", "table": "Table", "thankYou": "Thank you!", "time": "Time", "total": "Total", "visitAgain": "Visit us again soon"}, "receipts": {"actions": "Actions", "allTables": "All Tables", "allTypes": "All Types", "allUsers": "All Users", "amount": "Amount", "asc": "Ascending", "barOrders": "Bar Orders", "clearFilters": "Clear Filters", "close": "Close", "date": "Date", "dateRange": "Date Range", "delete": "Delete", "desc": "Description", "duration": "Duration", "endTime": "End Time", "exportCSV": "Export CSV", "exportData": "Export Data", "filterByTable": "Filter by Table", "filterByType": "Filter by Type", "filterByUser": "Filter by User", "filtersAndSearch": "Filters & Search", "from": "From", "game": "Game", "gameSessions": "Game Sessions", "items": "Items", "lastUpdated": "Last updated", "loading": "Loading transactions...", "noPermission": "No Permission", "noTransactions": "No transactions found", "order": "Order", "pageAmount": "Page Amount", "pageAvg": "Page Avg", "pickDateRange": "Pick Date Range", "printedAt": "Printed At", "printedBy": "Printed By", "receiptDetails": "Receipt Details", "receiptNumber": "Receipt Number", "receiptPreview": "Receipt Preview", "refresh": "Refresh", "reprint": "Reprint", "searchTransactions": "Search transactions...", "selectDate": "Select date", "selected": "selected", "sortBy": "Sort by", "startTime": "Start Time", "subtotal": "Subtotal", "table": "Table", "tax": "Tax", "time": "Time", "title": "Receipts", "to": "To", "total": "Total", "totalTransactions": "Total Transactions", "transactionHistory": "Transaction History", "type": "Type", "user": "User", "view": "View"}, "storage": {"clearCache": "<PERSON>ache", "online": "Online"}, "users": {"active": "active", "activeUser": "Active User", "addUser": "Add User", "admin": "Admin", "cancel": "Cancel", "clickAddUserToCreate": "Click \"Add User\" to create your first user", "collapse": "Collapse", "confirmDeleteUser": "Are you sure you want to delete user \"{{username}}\"?", "createNewUser": "Create New User", "createUser": "Create User", "created": "Created", "creating": "Creating...", "delete": "Delete", "edit": "Edit", "editUser": "Edit User", "editUserDetails": "Edit User Details", "enterFullName": "Enter full name", "enterNewPassword": "Enter new password", "enterPassword": "Enter password", "enterUsername": "Enter username", "expand": "Expand", "failedToCreateUser": "Failed to create user", "failedToDeleteUser": "Failed to delete user", "failedToUpdateUser": "Failed to update user", "fullName": "Full Name", "inactive": "Inactive", "loadingUsers": "Loading users...", "newPasswordOptional": "New Password (optional)", "noUsersFound": "No users found", "password": "Password", "passwordRequired": "Password is required", "role": "Role", "save": "Save", "saving": "Saving...", "status": "Status", "total": "total", "user": "user", "userCreatedSuccessfully": "User created successfully", "userDeletedSuccessfully": "User deleted successfully", "userDetails": "User Details", "userManagement": "User Management", "userUpdatedSuccessfully": "User updated successfully", "username": "Username", "usernameAndNameRequired": "Username and full name are required", "users": "users", "usersTotal": "{{count}} users total", "waiter": "Waiter"}}