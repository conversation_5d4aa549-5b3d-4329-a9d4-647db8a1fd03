"use client"

import { useState, useEffect } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { CheckCircle, XCircle, Loader2, Database, AlertTriangle, RefreshCw } from 'lucide-react'

export default function DebugTimeLimitPage() {
  const [isChecking, setIsChecking] = useState(false)
  const [isMigrating, setIsMigrating] = useState(false)
  const [dbStatus, setDbStatus] = useState<{
    hasColumn: boolean
    error?: string
    games?: any[]
  } | null>(null)
  const [migrationResult, setMigrationResult] = useState<{ success: boolean; message: string } | null>(null)

  const checkDatabaseStatus = async () => {
    setIsChecking(true)
    try {
      // Check if time_limit column exists by trying to query it
      const response = await fetch('/api/games', {
        credentials: 'include'
      })

      if (response.ok) {
        const games = await response.json()
        
        // Check if any game has time_limit property
        const hasTimeLimitColumn = games.length > 0 && games.some((game: any) => 
          game.hasOwnProperty('time_limit') || game.time_limit !== undefined
        )

        setDbStatus({
          hasColumn: hasTimeLimitColumn,
          games: games.slice(0, 3) // Show first 3 games for debugging
        })
      } else {
        setDbStatus({
          hasColumn: false,
          error: `API Error: ${response.status} ${response.statusText}`
        })
      }
    } catch (error) {
      setDbStatus({
        hasColumn: false,
        error: `Network Error: ${error instanceof Error ? error.message : 'Unknown error'}`
      })
    } finally {
      setIsChecking(false)
    }
  }

  const runMigration = async () => {
    setIsMigrating(true)
    setMigrationResult(null)

    try {
      const response = await fetch('/api/migrate-time-limit', {
        method: 'POST',
        credentials: 'include'
      })

      const data = await response.json()

      if (response.ok) {
        setMigrationResult({ success: true, message: data.message })
        // Recheck database status after migration
        setTimeout(() => checkDatabaseStatus(), 1000)
      } else {
        setMigrationResult({ success: false, message: data.error || 'Migration failed' })
      }
    } catch (error) {
      setMigrationResult({ 
        success: false, 
        message: `Network error: ${error instanceof Error ? error.message : 'Unknown error'}` 
      })
    } finally {
      setIsMigrating(false)
    }
  }

  useEffect(() => {
    checkDatabaseStatus()
  }, [])

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto space-y-6">
        <Card>
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
              <Database className="w-8 h-8 text-blue-600" />
            </div>
            <CardTitle className="text-2xl font-bold">Time Limit Debug & Fix</CardTitle>
            <CardDescription className="text-lg">
              Diagnose and fix time limit persistence issues
            </CardDescription>
          </CardHeader>

          <CardContent className="space-y-6">
            {/* Database Status */}
            <div className="border rounded-lg p-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-semibold text-lg">Database Status</h3>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={checkDatabaseStatus}
                  disabled={isChecking}
                >
                  {isChecking ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <RefreshCw className="w-4 h-4" />
                  )}
                  Refresh
                </Button>
              </div>

              {dbStatus ? (
                <div className={`border rounded-lg p-4 ${
                  dbStatus.hasColumn 
                    ? 'bg-green-50 border-green-200' 
                    : 'bg-red-50 border-red-200'
                }`}>
                  <div className="flex items-center gap-2 mb-2">
                    {dbStatus.hasColumn ? (
                      <CheckCircle className="w-5 h-5 text-green-600" />
                    ) : (
                      <XCircle className="w-5 h-5 text-red-600" />
                    )}
                    <span className={`font-semibold ${
                      dbStatus.hasColumn ? 'text-green-900' : 'text-red-900'
                    }`}>
                      {dbStatus.hasColumn ? 'time_limit Column EXISTS' : 'time_limit Column MISSING'}
                    </span>
                  </div>
                  
                  {dbStatus.error && (
                    <p className="text-red-800 text-sm mb-2">Error: {dbStatus.error}</p>
                  )}

                  {dbStatus.games && (
                    <div className="mt-3">
                      <p className="text-sm font-medium mb-2">Sample Games Data:</p>
                      <pre className="bg-gray-100 p-2 rounded text-xs overflow-x-auto">
                        {JSON.stringify(dbStatus.games, null, 2)}
                      </pre>
                    </div>
                  )}
                </div>
              ) : (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="w-6 h-6 animate-spin mr-2" />
                  <span>Checking database status...</span>
                </div>
              )}
            </div>

            {/* Migration Section */}
            {dbStatus && !dbStatus.hasColumn && (
              <div className="border border-orange-200 rounded-lg p-4 bg-orange-50">
                <div className="flex items-center gap-2 mb-3">
                  <AlertTriangle className="w-5 h-5 text-orange-600" />
                  <span className="font-semibold text-orange-900">Migration Required</span>
                </div>
                <p className="text-orange-800 text-sm mb-4">
                  The time_limit column is missing from your database. This is why time limits are not persisting after page refresh.
                </p>
                
                <Button 
                  onClick={runMigration} 
                  disabled={isMigrating}
                  className="w-full"
                >
                  {isMigrating ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Running Migration...
                    </>
                  ) : (
                    <>
                      <Database className="w-4 h-4 mr-2" />
                      Run Database Migration
                    </>
                  )}
                </Button>
              </div>
            )}

            {/* Migration Result */}
            {migrationResult && (
              <div className={`border rounded-lg p-4 ${
                migrationResult.success 
                  ? 'bg-green-50 border-green-200' 
                  : 'bg-red-50 border-red-200'
              }`}>
                <div className="flex items-center gap-2 mb-2">
                  {migrationResult.success ? (
                    <CheckCircle className="w-5 h-5 text-green-600" />
                  ) : (
                    <XCircle className="w-5 h-5 text-red-600" />
                  )}
                  <span className={`font-semibold ${
                    migrationResult.success ? 'text-green-900' : 'text-red-900'
                  }`}>
                    {migrationResult.success ? 'Migration Successful!' : 'Migration Failed'}
                  </span>
                </div>
                <p className={`text-sm ${
                  migrationResult.success ? 'text-green-800' : 'text-red-800'
                }`}>
                  {migrationResult.message}
                </p>
              </div>
            )}

            {/* Success Actions */}
            {dbStatus?.hasColumn && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-3">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <span className="font-semibold text-green-900">Database Ready!</span>
                </div>
                <p className="text-green-800 text-sm mb-4">
                  Your database has the time_limit column. Time limits should now persist across page refreshes.
                </p>
                <Button 
                  variant="outline" 
                  onClick={() => window.location.href = '/'}
                  className="w-full"
                >
                  Return to BBM App & Test Time Limits
                </Button>
              </div>
            )}

            {/* Instructions */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="font-semibold text-blue-900 mb-2">How to Test After Migration:</h3>
              <ol className="text-blue-800 space-y-1 text-sm list-decimal list-inside">
                <li>Return to the main BBM app</li>
                <li>Set a time limit (e.g., 30 minutes) on any table</li>
                <li>Refresh the page (F5)</li>
                <li>Check that the time limit is still displayed</li>
                <li>Start a game and verify the time limit works</li>
              </ol>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
