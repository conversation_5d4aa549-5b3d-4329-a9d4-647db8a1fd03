"use client"

import React, { useState, useEffect } from "react"
import { useSafeTranslation } from './hooks/useSafeTranslation'
import { Users, GamepadIcon, Coffee, BarChart3, Receipt, SettingsIcon, RefreshCw, Eye, EyeOff, LogIn, AlertTriangle } from 'lucide-react'
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { GameTimer } from "./components/GameTimer"
import { BarMenu } from "./components/BarMenu"
import { Analytics } from "./components/Analytics"
import { ReceiptHistory } from "./components/ReceiptHistory"
import { Settings } from "./components/Settings"
import { UserProfile } from "./components/UserProfile"
import { ErrorBoundary } from "./components/ErrorBoundary"
import { LanguageSelector } from "./components/LanguageSelector"
import { AppleHeader } from "./components/AppleHeader"
import { MigrationStatus } from "./components/MigrationStatus"
import { AuthProvider, useAuth } from "./contexts/AuthContext"
import { DataRefreshProvider } from "./contexts/DataRefreshContext"
import { usePermissions } from "./hooks/usePermissions"
import { clearBrowserData } from "./utils/browserUtils"
import type { Order, Game } from "./types"

// Authentication component to avoid chunk loading issues
interface User {
  id: number
  username: string
  role: 'admin' | 'waiter'
  fullName: string
}

interface AuthComponentProps {
  onLogin: (user: User, token: string) => void
}

interface Waiter {
  id: string
  display_name: string
  username: string
  password: string
  enabled: boolean
}

interface LoginSettings {
  waiters_section_enabled: boolean
  waiters_section_title: string
  waiters: Waiter[]
  database_online?: boolean
  database_error?: string
}

function AuthenticationComponent({ onLogin }: AuthComponentProps) {
  const { t } = useSafeTranslation()
  const { user } = useAuth()

  // Don't render or run any monitoring if user is authenticated
  if (user) {
    console.log('🛑 User authenticated, AuthenticationComponent not rendering')
    return null
  }
  const [username, setUsername] = useState("")
  const [password, setPassword] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")
  const [isLoadingSettings, setIsLoadingSettings] = useState(true)
  const [statusCheckInterval, setStatusCheckInterval] = useState<NodeJS.Timeout | null>(null)
  const [databaseStatus, setDatabaseStatus] = useState<{
    online: boolean
    message: string
    lastChecked: Date | null
    isChecked: boolean
  }>({
    online: false,
    message: "",
    lastChecked: null,
    isChecked: false // Don't show status until actually checked
  })
  const [loginSettings, setLoginSettings] = useState<LoginSettings>({
    waiters_section_enabled: true,
    waiters_section_title: "Waiters Accounts:",
    waiters: [
      {
        id: '1',
        display_name: "Waiter One:",
        username: "waiter1",
        password: "waiter1",
        enabled: true,
      },
      {
        id: '2',
        display_name: "Waiter Two:",
        username: "waiter2",
        password: "waiter2",
        enabled: true,
      }
    ],
    database_online: true
  })

  // Check database status with timeout
  const checkDatabaseStatus = async () => {
    try {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 3000) // 3 second timeout

      const response = await fetch('/api/database/status', {
        signal: controller.signal
      })

      clearTimeout(timeoutId)
      const data = await response.json()

      setDatabaseStatus({
        online: data.online,
        message: data.message,
        lastChecked: new Date(),
        isChecked: true
      })

      // If database comes back online, reload login settings to get waiters
      if (data.online && !databaseStatus.online) {
        console.log('🔄 Database came back online, reloading login settings...')
        loadLoginSettingsFromDatabase()
      }

      return data.online
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        console.warn('Database status check timeout')
      }
      setDatabaseStatus({
        online: false,
        message: 'Failed to check database status',
        lastChecked: new Date(),
        isChecked: true
      })
      return false
    }
  }

  // Function to load login settings from database
  const loadLoginSettingsFromDatabase = async () => {
    try {
      // Load login settings (will automatically handle offline case)
      const response = await fetch('/api/login-settings/public')
      if (response.ok) {
        const data = await response.json()

        // Handle both old and new format
        if (data.waiters && Array.isArray(data.waiters)) {
          // New format with dynamic waiters
          setLoginSettings(data)
        } else {
          // Legacy format - convert to new format
          const waiters: Waiter[] = []
          if (data.waiter1_enabled !== false) {
            waiters.push({
              id: '1',
              display_name: data.waiter1_display_name || "Waiter One:",
              username: data.waiter1_username || "waiter1",
              password: data.waiter1_password || "waiter1",
              enabled: data.waiter1_enabled ?? true,
            })
          }
          if (data.waiter2_enabled !== false) {
            waiters.push({
              id: '2',
              display_name: data.waiter2_display_name || "Waiter Two:",
              username: data.waiter2_username || "waiter2",
              password: data.waiter2_password || "waiter2",
              enabled: data.waiter2_enabled ?? true,
            })
          }
          setLoginSettings({
            waiters_section_enabled: data.waiters_section_enabled ?? true,
            waiters_section_title: data.waiters_section_title || "Waiters Accounts:",
            waiters,
            database_online: true
          })
        }
        console.log('✅ Login settings loaded from database for login page')
      } else {
        throw new Error('Database request failed')
      }
    } catch (error) {
      console.warn('❌ Failed to load login settings from database, trying localStorage:', error)

      // Fallback to localStorage
      try {
        const backupSettings = localStorage.getItem('login_settings_backup')
        if (backupSettings) {
          const parsedSettings = JSON.parse(backupSettings)

          // Convert legacy format if needed
          if (parsedSettings.waiter1_display_name && !parsedSettings.waiters) {
            const waiters: Waiter[] = []
            if (parsedSettings.waiter1_enabled !== false) {
              waiters.push({
                id: '1',
                display_name: parsedSettings.waiter1_display_name || "Waiter One:",
                username: parsedSettings.waiter1_username || "waiter1",
                password: parsedSettings.waiter1_password || "waiter1",
                enabled: parsedSettings.waiter1_enabled ?? true,
              })
            }
            if (parsedSettings.waiter2_enabled !== false) {
              waiters.push({
                id: '2',
                display_name: parsedSettings.waiter2_display_name || "Waiter Two:",
                username: parsedSettings.waiter2_username || "waiter2",
                password: parsedSettings.waiter2_password || "waiter2",
                enabled: parsedSettings.waiter2_enabled ?? true,
              })
            }
            setLoginSettings({
              waiters_section_enabled: false, // Hide waiters when offline
              waiters_section_title: parsedSettings.waiters_section_title || "Waiters Accounts:",
              waiters,
              database_online: false
            })
          } else {
            setLoginSettings({
              ...parsedSettings,
              waiters_section_enabled: false, // Hide waiters when offline
              database_online: false
            })
          }
          console.log('✅ Login settings loaded from localStorage backup for login page')
        }
      } catch (localError) {
        console.warn('❌ Failed to load from localStorage, using defaults:', localError)
        // Keep default settings on error but hide waiters section
        setLoginSettings(prev => ({
          ...prev,
          waiters_section_enabled: false,
          database_online: false
        }))
      }
    } finally {
      setIsLoadingSettings(false)
    }
  }

  // Load login settings and check database status on component mount
  useEffect(() => {

    const initializeLoginPage = async () => {
      // Load settings and check database status in parallel
      await Promise.all([
        loadLoginSettingsFromDatabase(),
        checkDatabaseStatus()
      ])
    }

    // Initialize login page
    initializeLoginPage()

    // Set up dynamic database status checks
    const setupStatusChecks = () => {
      // Clear existing interval
      if (statusCheckInterval) {
        clearInterval(statusCheckInterval)
      }

      // Check less frequently for better performance (every 60 seconds when online, every 15 seconds when offline)
      const interval = databaseStatus.online ? 60000 : 15000
      const newInterval = setInterval(() => {
        // Use requestAnimationFrame for non-blocking database checks
        requestAnimationFrame(() => {
          checkDatabaseStatus()
        })
      }, interval)
      setStatusCheckInterval(newInterval)
    }

    // Initial setup
    setupStatusChecks()

    return () => {
      if (statusCheckInterval) {
        clearInterval(statusCheckInterval)
      }
    }
  }, [])

  // Clean up intervals when component unmounts (user logs in)
  useEffect(() => {
    return () => {
      if (statusCheckInterval) {
        clearInterval(statusCheckInterval)
        console.log('🧹 Cleaned up database status monitoring on login')
      }
    }
  }, [])

  // Update check frequency when database status changes
  useEffect(() => {
    if (databaseStatus.isChecked) {
      // Clear existing interval
      if (statusCheckInterval) {
        clearInterval(statusCheckInterval)
      }

      // Set new interval based on database status (optimized for performance)
      // Check every 60 seconds when online, every 15 seconds when offline
      const interval = databaseStatus.online ? 60000 : 15000
      const newInterval = setInterval(() => {
        // Use requestAnimationFrame for non-blocking database checks
        requestAnimationFrame(() => {
          checkDatabaseStatus()
        })
      }, interval)
      setStatusCheckInterval(newInterval)

      console.log(`🔄 Database status check interval updated: ${databaseStatus.online ? '60s (online)' : '15s (offline)'}`)
    }
  }, [databaseStatus.online, databaseStatus.isChecked])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError("")

    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Include cookies for authentication
        body: JSON.stringify({ username, password }),
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Login failed')
      }

      // Cookies are set automatically by the server
      // No need to store auth data client-side
      onLogin(data.user, data.token)
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Login failed')
    } finally {
      setIsLoading(false)
    }
  }

  const quickLogin = (user: string, pass: string) => {
    setUsername(user)
    setPassword(pass)
  }

  return (
    <div className="max-w-md w-full space-y-8">
      <div className="text-center">
        <div className="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-blue-100">
          <Users className="h-6 w-6 text-blue-600" />
        </div>
        <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
          {t('dashboard.title')}
        </h2>
        <p className="mt-2 text-sm text-gray-600">
          {t('auth.login')}
        </p>
      </div>

      {/* Database Status Notification - Only show after status is checked */}
      {databaseStatus.isChecked && !databaseStatus.online && (
        <Alert variant="destructive" className="mb-4">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>{t('database.offline')}</AlertTitle>
          <AlertDescription>
            {t('database.offlineMessage')} {databaseStatus.message}
          </AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <LogIn className="h-5 w-5" />
            {t('auth.login')}
            {/* Database Status Indicator - Only show after status is checked */}
            {databaseStatus.isChecked && (
              <div className={`ml-auto flex items-center space-x-1 px-2 py-1 rounded-full text-xs ${
                databaseStatus.online
                  ? 'bg-green-50 text-green-700'
                  : 'bg-red-50 text-red-700'
              }`}>
                <div className={`w-2 h-2 rounded-full ${
                  databaseStatus.online ? 'bg-green-500' : 'bg-red-500'
                } ${databaseStatus.online ? 'animate-pulse' : ''}`}></div>
                <span>{databaseStatus.online ? t('database.online') : t('database.offline')}</span>
              </div>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div>
              <Label htmlFor="username">{t('auth.username')}</Label>
              <Input
                id="username"
                type="text"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                required
                placeholder={t('auth.username')}
              />
            </div>

            <div>
              <Label htmlFor="password">{t('auth.password')}</Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  placeholder={t('auth.password')}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>

            <Button
              type="submit"
              className="w-full"
              disabled={isLoading}
            >
              {isLoading ? t('common.loading') : t('auth.loginButton')}
            </Button>
          </form>

          {/* Waiters credentials - Only show after settings loaded and database status checked */}
          {!isLoadingSettings && databaseStatus.isChecked && loginSettings.waiters_section_enabled && databaseStatus.online && (
            <div className="mt-6 pt-6 border-t border-gray-200">
              <p className="text-sm text-gray-600 mb-3">{loginSettings.waiters_section_title}</p>
              <div className="space-y-2">
                {loginSettings.waiters.filter(waiter => waiter.enabled).map((waiter) => (
                  <Button
                    key={waiter.id}
                    variant="outline"
                    size="sm"
                    className="w-full text-left justify-start"
                    onClick={() => quickLogin(waiter.username, waiter.password)}
                  >
                    <span className="font-medium">{waiter.display_name}</span>
                    <span className="ml-2 text-gray-600">{waiter.username}</span>
                  </Button>
                ))}
              </div>
            </div>
          )}


        </CardContent>
      </Card>
    </div>
  )
}

// Dashboard Component
function Dashboard() {
  const { t } = useSafeTranslation()
  const { user, logout } = useAuth()
  const { canAccessSettings } = usePermissions()
  const [activeGames, setActiveGames] = useState<Game[]>([])
  const [recentOrders, setRecentOrders] = useState<Order[]>([])
  const [totalRevenue, setTotalRevenue] = useState(0)
  // Initialize activeView from localStorage or default to "games"
  const [activeView, setActiveView] = useState<"games" | "bar" | "analytics" | "receipts" | "settings">(() => {
    if (typeof window !== 'undefined') {
      const savedView = localStorage.getItem('bbm_active_view')
      if (savedView && ['games', 'bar', 'analytics', 'receipts', 'settings'].includes(savedView)) {
        return savedView as "games" | "bar" | "analytics" | "receipts" | "settings"
      }
    }
    return "games"
  })
  const [isUserProfileOpen, setIsUserProfileOpen] = useState(false)

  // Navigation menu items with icons - filtered by permissions
  const allNavigationItems = [
    { key: "games", label: t('games.title'), icon: GamepadIcon, shortLabel: t('navigation.games') },
    { key: "bar", label: t('bar.title'), icon: Coffee, shortLabel: t('navigation.bar') },
    { key: "analytics", label: t('analytics.title'), icon: BarChart3, shortLabel: t('navigation.analytics') },
    { key: "receipts", label: t('receipts.title'), icon: Receipt, shortLabel: t('receipts.title') },
    { key: "settings", label: t('settings.title'), icon: SettingsIcon, shortLabel: t('navigation.settings') },
  ]

  // Filter navigation items based on permissions
  const navigationItems = allNavigationItems.filter(item => {
    if (item.key === "settings") {
      // Always show Settings for admin users, even if permissions haven't loaded yet
      return user?.role === 'admin' || canAccessSettings()
    }
    return true
  })

  // Save activeView to localStorage whenever it changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('bbm_active_view', activeView)
    }
  }, [activeView])

  // Validate saved view on component mount and redirect if no permission
  useEffect(() => {
    if (activeView === "settings" && user?.role !== 'admin' && !canAccessSettings()) {
      setActiveView("games")
    }
  }, [activeView, canAccessSettings, user?.role])

  // Validate the saved view has valid permissions on mount
  useEffect(() => {
    const validateSavedView = () => {
      if (activeView === "settings" && user?.role !== 'admin' && !canAccessSettings()) {
        console.log('🔒 Redirecting from settings - no permission')
        setActiveView("games")
      }
    }

    // Run validation after permissions are loaded
    validateSavedView()
  }, [canAccessSettings, user?.role]) // Only run when permissions change



  // Handle cache clear
  const handleClearCache = () => {
    const confirmed = window.confirm(
      "This will clear all cached data and reload the page. You may need to log in again. Continue?"
    )
    if (confirmed) {
      console.log('🧹 Manual cache clear initiated...')
      clearBrowserData()
      setTimeout(() => {
        window.location.reload()
      }, 500)
    }
  }

  // Load active games and revenue from database (only once on mount)
  useEffect(() => {
    const fetchData = async () => {
      try {
        console.log('📊 Loading initial dashboard data...')

        // Fetch games
        const gamesResponse = await fetch('/api/games', {
          credentials: 'include' // Use cookies for authentication
        })
        if (gamesResponse.ok) {
          const gamesData = await gamesResponse.json()
          setActiveGames(gamesData.filter((game: any) => game.status === 'active'))
          console.log('✅ Dashboard games loaded')
        }

        // Fetch orders
        const ordersResponse = await fetch('/api/orders', {
          credentials: 'include' // Use cookies for authentication
        })
        if (ordersResponse.ok) {
          const ordersData = await ordersResponse.json()
          // Ensure orders is an array before mapping
          const ordersArray = Array.isArray(ordersData) ? ordersData : []
          const ordersWithDates = ordersArray.map((order: any) => ({
            ...order,
            id: order.id.toString(),
            created_at: new Date(order.created_at),
            updated_at: new Date(order.updated_at),
          }))
          setRecentOrders(ordersWithDates.slice(0, 5))

          // Calculate total revenue
          const revenue = ordersArray.reduce((sum: number, order: any) => sum + (order.total || 0), 0)
          setTotalRevenue(revenue)
          console.log('✅ Dashboard orders loaded')
        }

        console.log('✅ Initial dashboard data loading complete')
      } catch (error) {
        console.error('Error fetching dashboard data:', error)
      }
    }

    fetchData()
  }, []) // Only run once on mount

  // Convert navigation items to AppleHeader format
  const appleNavigationItems = navigationItems.map(item => ({
    label: item.shortLabel,
    href: item.key,
    isActive: activeView === item.key
  }))

  const handleAppleNavigation = (href: string) => {
    setActiveView(href as any)
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Apple Header */}
      <AppleHeader
        navigationItems={appleNavigationItems}
        onNavigationClick={handleAppleNavigation}
        user={user}
        onUserProfileOpen={() => setIsUserProfileOpen(true)}
        onLogout={logout}
      />

      {/* Main Content - Added top padding for fixed header */}
      <main className="max-w-7xl mx-auto pt-20 py-6 sm:px-6 lg:px-8">
        {/* Migration Status Alert */}
        <div className="px-4 sm:px-0">
          <MigrationStatus />
        </div>

        {activeView === "games" && (
          <div className="px-4 py-6 sm:px-0">
            <GameTimer activeGames={activeGames} setActiveGames={setActiveGames} setTotalRevenue={setTotalRevenue} />
          </div>
        )}

        {activeView === "bar" && (
          <div className="px-4 py-6 sm:px-0">
            <BarMenu recentOrders={recentOrders} setRecentOrders={setRecentOrders} setTotalRevenue={setTotalRevenue} />
          </div>
        )}

        {activeView === "analytics" && (
          <div className="px-4 py-6 sm:px-0">
            <Analytics />
          </div>
        )}

        {activeView === "receipts" && (
          <div className="px-4 py-6 sm:px-0">
            <ReceiptHistory />
          </div>
        )}

        {activeView === "settings" && (
          <div className="px-4 py-6 sm:px-0">
            <Settings />
          </div>
        )}
      </main>

      {/* User Profile Dialog */}
      <UserProfile
        isOpen={isUserProfileOpen}
        onClose={() => setIsUserProfileOpen(false)}
      />
    </div>
  )
}

// Main App Component with Authentication
export default function MainApp() {
  return (
    <ErrorBoundary>
      <AuthProvider>
        <AuthenticatedApp />
      </AuthProvider>
    </ErrorBoundary>
  )
}

function AuthenticatedApp() {
  const { user, isLoading, login } = useAuth()

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <AuthenticationComponent onLogin={login} />
      </div>
    )
  }

  // User is authenticated - no more login page monitoring needed
  console.log('✅ User authenticated, login page monitoring stopped')
  return (
    <DataRefreshProvider>
      <Dashboard />
    </DataRefreshProvider>
  )
}
