"use client"

import { useState } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { CheckCircle, XCircle, Loader2, Database } from 'lucide-react'

export default function MigrateTimeLimitPage() {
  const [isRunning, setIsRunning] = useState(false)
  const [result, setResult] = useState<{ success: boolean; message: string } | null>(null)

  const runMigration = async () => {
    setIsRunning(true)
    setResult(null)

    try {
      const response = await fetch('/api/migrate-time-limit', {
        method: 'POST',
        credentials: 'include'
      })

      const data = await response.json()

      if (response.ok) {
        setResult({ success: true, message: data.message })
      } else {
        setResult({ success: false, message: data.error || 'Migration failed' })
      }
    } catch (error) {
      setResult({ 
        success: false, 
        message: `Network error: ${error instanceof Error ? error.message : 'Unknown error'}` 
      })
    } finally {
      setIsRunning(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
            <Database className="w-8 h-8 text-blue-600" />
          </div>
          <CardTitle className="text-2xl font-bold">Database Migration</CardTitle>
          <CardDescription className="text-lg">
            Add Time Limit Support to Games Table
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="font-semibold text-blue-900 mb-2">What this migration does:</h3>
            <ul className="text-blue-800 space-y-1 text-sm">
              <li>• Adds <code className="bg-blue-100 px-1 rounded">time_limit</code> column to games table</li>
              <li>• Creates helper functions for time calculations</li>
              <li>• Adds database indexes for better performance</li>
              <li>• Creates view for active games with time information</li>
              <li>• Enables time limit persistence across page refreshes</li>
            </ul>
          </div>

          {result && (
            <div className={`border rounded-lg p-4 ${
              result.success 
                ? 'bg-green-50 border-green-200' 
                : 'bg-red-50 border-red-200'
            }`}>
              <div className="flex items-center gap-2 mb-2">
                {result.success ? (
                  <CheckCircle className="w-5 h-5 text-green-600" />
                ) : (
                  <XCircle className="w-5 h-5 text-red-600" />
                )}
                <span className={`font-semibold ${
                  result.success ? 'text-green-900' : 'text-red-900'
                }`}>
                  {result.success ? 'Migration Successful!' : 'Migration Failed'}
                </span>
              </div>
              <p className={`text-sm ${
                result.success ? 'text-green-800' : 'text-red-800'
              }`}>
                {result.message}
              </p>
            </div>
          )}

          <div className="flex flex-col gap-4">
            <Button 
              onClick={runMigration} 
              disabled={isRunning || (result?.success === true)}
              className="w-full"
              size="lg"
            >
              {isRunning ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Running Migration...
                </>
              ) : result?.success ? (
                <>
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Migration Completed
                </>
              ) : (
                <>
                  <Database className="w-4 h-4 mr-2" />
                  Run Migration
                </>
              )}
            </Button>

            {result?.success && (
              <div className="text-center">
                <p className="text-sm text-gray-600 mb-3">
                  Migration completed successfully! You can now return to the main app.
                </p>
                <Button 
                  variant="outline" 
                  onClick={() => window.location.href = '/'}
                  className="w-full"
                >
                  Return to BBM App
                </Button>
              </div>
            )}
          </div>

          <div className="text-xs text-gray-500 text-center">
            <p>⚠️ This migration requires admin privileges</p>
            <p>Make sure you're logged in as an admin user</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
