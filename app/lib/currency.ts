export interface CurrencySettings {
  currency: string
  symbol: string
  showDecimals: boolean
  taxIncluded: boolean
  taxEnabled: boolean
  taxRate: number
  qrCodeEnabled: boolean
  qrCodeUrl: string
}

// Default currency settings
export const DEFAULT_CURRENCY_SETTINGS: CurrencySettings = {
  currency: 'Albanian Lek',
  symbol: 'Leke',
  showDecimals: false,
  taxIncluded: false,
  taxEnabled: true,
  taxRate: 20,
  qrCodeEnabled: false,
  qrCodeUrl: '',
}

// Format a number as currency based on settings
export function formatCurrency(amount: number, settings: CurrencySettings): string {
  const formattedAmount = settings.showDecimals
    ? amount.toFixed(2)
    : Math.round(amount).toString()

  return `${formattedAmount} ${settings.symbol}`
}

// Calculate tax amount
export function calculateTax(amount: number, settings: CurrencySettings): number {
  if (!settings.taxEnabled) return 0
  return amount * (settings.taxRate / 100)
}

// Calculate total with tax
export function calculateTotal(subtotal: number, settings: CurrencySettings): number {
  if (!settings.taxEnabled) return subtotal
  if (settings.taxIncluded) {
    return subtotal
  }
  return subtotal + calculateTax(subtotal, settings)
}

// Calculate subtotal from tax-included price
export function calculateSubtotalFromTotal(total: number, settings: CurrencySettings): number {
  if (!settings.taxEnabled || !settings.taxIncluded) {
    return total
  }
  return total / (1 + settings.taxRate / 100)
}

// Calculate tax from tax-included price
export function calculateTaxFromTotal(total: number, settings: CurrencySettings): number {
  if (!settings.taxEnabled) return 0
  if (!settings.taxIncluded) {
    return calculateTax(total, settings)
  }
  // When tax is included, calculate the tax portion from the total
  const subtotalExcludingTax = total / (1 + settings.taxRate / 100)
  return total - subtotalExcludingTax
}

// Format price for display (handles tax inclusion logic)
export function formatPrice(price: number, settings: CurrencySettings): {
  displayPrice: string
  subtotal: number
  tax: number
  total: number
} {
  let subtotal: number
  let tax: number
  let total: number

  if (settings.taxIncluded) {
    // Price includes tax
    total = price
    subtotal = calculateSubtotalFromTotal(price, settings)
    tax = total - subtotal
  } else {
    // Price excludes tax
    subtotal = price
    tax = calculateTax(price, settings)
    total = subtotal + tax
  }

  return {
    displayPrice: formatCurrency(price, settings),
    subtotal,
    tax,
    total
  }
}

// Format receipt line item
export function formatReceiptItem(name: string, quantity: number, unitPrice: number, settings: CurrencySettings): {
  line: string
  subtotal: number
  tax: number
  total: number
} {
  const itemTotal = unitPrice * quantity
  const priceInfo = formatPrice(itemTotal, settings)

  return {
    line: `${name} x ${quantity} = ${formatCurrency(itemTotal, settings)}`,
    subtotal: priceInfo.subtotal,
    tax: priceInfo.tax,
    total: priceInfo.total
  }
}

// Format receipt totals section
export function formatReceiptTotals(subtotal: number, settings: CurrencySettings): string[] {
  const lines: string[] = []

  if (!settings.taxEnabled) {
    // No tax - just show total
    lines.push(`Total: ${formatCurrency(subtotal, settings)}`)
  } else if (settings.taxIncluded) {
    // When tax is included in product prices:
    // subtotal = sum of product prices (which include tax)
    // We need to calculate what the tax portion is and show the breakdown
    const taxRate = settings.taxRate / 100
    const subtotalExcludingTax = subtotal / (1 + taxRate)
    const taxAmount = subtotal - subtotalExcludingTax

    lines.push(`Subtotal (excl. tax): ${formatCurrency(subtotalExcludingTax, settings)}`)
    lines.push(`Tax (${Math.round(Number(settings.taxRate))}%): ${formatCurrency(taxAmount, settings)}`)
    lines.push(`Total: ${formatCurrency(subtotal, settings)}`) // This should be the original subtotal (sum of product prices)
  } else {
    // When tax is excluded, we add it
    const taxAmount = calculateTax(subtotal, settings)
    const total = subtotal + taxAmount

    lines.push(`Subtotal: ${formatCurrency(subtotal, settings)}`)
    lines.push(`Tax (${Math.round(Number(settings.taxRate))}%): ${formatCurrency(taxAmount, settings)}`)
    lines.push(`Total: ${formatCurrency(total, settings)}`)
  }

  return lines
}

// Format receipt totals section with localized labels
export function formatReceiptTotalsLocalized(
  subtotal: number,
  settings: CurrencySettings,
  t: (key: string, options?: any) => string
): string[] {
  const lines: string[] = []

  if (!settings.taxEnabled) {
    // No tax - just show total
    lines.push(`${t('games.total')}: ${formatCurrency(subtotal, settings)}`)
  } else if (settings.taxIncluded) {
    // When tax is included in product prices:
    // subtotal = sum of product prices (which include tax)
    // We need to calculate what the tax portion is and show the breakdown
    const taxRate = settings.taxRate / 100
    const subtotalExcludingTax = subtotal / (1 + taxRate)
    const taxAmount = subtotal - subtotalExcludingTax

    lines.push(`${t('games.subtotalExclTax')}: ${formatCurrency(subtotalExcludingTax, settings)}`)
    lines.push(`Taksa ${Math.round(Number(settings.taxRate))}%: ${formatCurrency(taxAmount, settings)}`)
    lines.push(`${t('games.total')}: ${formatCurrency(subtotal, settings)}`)
  } else {
    // When tax is excluded, we add it
    const taxAmount = calculateTax(subtotal, settings)
    const total = subtotal + taxAmount

    lines.push(`${t('common.subtotal')}: ${formatCurrency(subtotal, settings)}`)
    lines.push(`Taksa ${Math.round(Number(settings.taxRate))}%: ${formatCurrency(taxAmount, settings)}`)
    lines.push(`${t('games.total')}: ${formatCurrency(total, settings)}`)
  }

  return lines
}

// Fetch currency settings from API
export async function fetchCurrencySettings(): Promise<CurrencySettings> {
  try {
    const response = await fetch('/api/currency-settings')
    if (response.ok) {
      const data = await response.json()
      return {
        currency: data.currency || DEFAULT_CURRENCY_SETTINGS.currency,
        symbol: data.symbol || DEFAULT_CURRENCY_SETTINGS.symbol,
        showDecimals: data.show_decimals ?? DEFAULT_CURRENCY_SETTINGS.showDecimals,
        taxIncluded: data.tax_included ?? DEFAULT_CURRENCY_SETTINGS.taxIncluded,
        taxEnabled: data.tax_enabled ?? DEFAULT_CURRENCY_SETTINGS.taxEnabled,
        taxRate: Number(data.tax_rate) || DEFAULT_CURRENCY_SETTINGS.taxRate,
        qrCodeEnabled: data.qr_code_enabled ?? DEFAULT_CURRENCY_SETTINGS.qrCodeEnabled,
        qrCodeUrl: data.qr_code_url || DEFAULT_CURRENCY_SETTINGS.qrCodeUrl,
      }
    }
  } catch (error) {
    console.error('Failed to fetch currency settings:', error)
  }

  return DEFAULT_CURRENCY_SETTINGS
}

// Generate QR code as data URL with robust error handling
export async function generateQRCode(url: string, businessInfo?: any): Promise<string> {
  // Return empty string immediately if running on server side
  if (typeof window === 'undefined') {
    return ''
  }

  try {
    // Use dynamic import with multiple fallback strategies
    let QRCode: any

    try {
      const QRCodeModule = await import('qrcode')
      QRCode = QRCodeModule.default || QRCodeModule
    } catch (importError) {
      console.warn('QRCode module import failed:', importError)
      return ''
    }

    // Validate QRCode module
    if (!QRCode || typeof QRCode.toDataURL !== 'function') {
      console.warn('QRCode module not properly loaded')
      return ''
    }

    // If URL is empty, generate QR code with business contact info
    let qrContent = url.trim()
    if (!qrContent) {
      // Create a simple contact card format
      qrContent = businessInfo ?
        `${businessInfo.name}\nTel: ${businessInfo.phone}\n${businessInfo.address}` :
        'Thank you for visiting!'
    }

    const qrCodeDataURL = await QRCode.toDataURL(qrContent, {
      width: 100,
      margin: 1,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    })
    return qrCodeDataURL
  } catch (error) {
    console.error('Failed to generate QR code:', error)
    return ''
  }
}
