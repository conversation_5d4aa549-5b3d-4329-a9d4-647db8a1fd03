import { useEffect, useRef, useCallback, useState } from 'react'
import { performanceMonitor } from '../utils/performance/PerformanceMonitor'

interface TimerOptions {
  interval?: number
  enabled?: boolean
  immediate?: boolean
  onTick?: () => void
}

/**
 * Optimized timer hook that prevents performance issues
 * - Uses requestAnimationFrame for better performance
 * - Automatically pauses when tab is not visible
 * - Debounces rapid updates
 * - Provides performance monitoring
 */
export function useOptimizedTimer(
  callback: () => void,
  options: TimerOptions = {}
) {
  const {
    interval = 1000,
    enabled = true,
    immediate = false,
    onTick
  } = options

  const callbackRef = useRef(callback)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const lastTickRef = useRef<number>(0)
  const isVisibleRef = useRef<boolean>(true)
  const [isRunning, setIsRunning] = useState(false)

  // Update callback ref when callback changes
  callbackRef.current = callback

  // Handle visibility change to pause timer when tab is not visible
  useEffect(() => {
    const handleVisibilityChange = () => {
      isVisibleRef.current = !document.hidden
      if (document.hidden) {
        console.log('⏸️ Timer paused - tab not visible')
      } else {
        console.log('▶️ Timer resumed - tab visible')
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange)
  }, [])

  const tick = useCallback(() => {
    const now = performance.now()
    
    // Skip if tab is not visible (performance optimization)
    if (!isVisibleRef.current) {
      return
    }

    // Throttle updates to prevent excessive calls
    if (now - lastTickRef.current < interval) {
      return
    }

    lastTickRef.current = now

    // Measure callback performance
    performanceMonitor.measure('timer-callback', () => {
      callbackRef.current()
      onTick?.()
    }, 'timer')
  }, [interval, onTick])

  const start = useCallback(() => {
    if (intervalRef.current) return

    setIsRunning(true)
    console.log(`🚀 Starting optimized timer (${interval}ms interval)`)

    // Use setInterval with requestAnimationFrame for better performance
    intervalRef.current = setInterval(() => {
      requestAnimationFrame(tick)
    }, interval)

    // Run immediately if requested
    if (immediate) {
      requestAnimationFrame(tick)
    }
  }, [interval, immediate, tick])

  const stop = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
      intervalRef.current = null
      setIsRunning(false)
      console.log('⏹️ Stopped optimized timer')
    }
  }, [])

  const restart = useCallback(() => {
    stop()
    start()
  }, [stop, start])

  // Auto-start/stop based on enabled prop
  useEffect(() => {
    if (enabled) {
      start()
    } else {
      stop()
    }

    return stop
  }, [enabled, start, stop])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stop()
    }
  }, [stop])

  return {
    isRunning,
    start,
    stop,
    restart
  }
}

/**
 * Hook for optimized state updates that batches changes
 */
export function useOptimizedState<T>(
  initialValue: T,
  updateInterval: number = 100
) {
  const [state, setState] = useState<T>(initialValue)
  const pendingUpdateRef = useRef<T | null>(null)
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)

  const setOptimizedState = useCallback((newValue: T | ((prev: T) => T)) => {
    const value = typeof newValue === 'function' 
      ? (newValue as (prev: T) => T)(pendingUpdateRef.current || state)
      : newValue

    pendingUpdateRef.current = value

    // Debounce state updates
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }

    timeoutRef.current = setTimeout(() => {
      if (pendingUpdateRef.current !== null) {
        setState(pendingUpdateRef.current)
        pendingUpdateRef.current = null
      }
    }, updateInterval)
  }, [state, updateInterval])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])

  return [state, setOptimizedState] as const
}

/**
 * Hook for debounced values to prevent excessive re-renders
 */
export function useDebounced<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

/**
 * Hook for throttled values to limit update frequency
 */
export function useThrottled<T>(value: T, interval: number): T {
  const [throttledValue, setThrottledValue] = useState<T>(value)
  const lastUpdated = useRef<number>(0)

  useEffect(() => {
    const now = Date.now()
    
    if (now - lastUpdated.current >= interval) {
      setThrottledValue(value)
      lastUpdated.current = now
    } else {
      const timeoutId = setTimeout(() => {
        setThrottledValue(value)
        lastUpdated.current = Date.now()
      }, interval - (now - lastUpdated.current))

      return () => clearTimeout(timeoutId)
    }
  }, [value, interval])

  return throttledValue
}
