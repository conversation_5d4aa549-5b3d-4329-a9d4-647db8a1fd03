import React, { useCallback, useRef } from 'react'
import { performanceMonitor } from '../utils/performance/PerformanceMonitor'

interface OptimizedClickOptions {
  debounceMs?: number
  throttleMs?: number
  measurePerformance?: boolean
  preventDoubleClick?: boolean
}

/**
 * Optimized click handler hook that prevents performance issues
 * - Debounces rapid clicks
 * - Measures performance
 * - Prevents double-clicks
 * - Uses requestAnimationFrame for non-blocking execution
 */
export function useOptimizedClick<T extends any[]>(
  handler: (...args: T) => void | Promise<void>,
  options: OptimizedClickOptions = {}
) {
  const {
    debounceMs = 100,
    throttleMs = 0,
    measurePerformance = true,
    preventDoubleClick = true
  } = options

  const lastClickRef = useRef<number>(0)
  const isProcessingRef = useRef<boolean>(false)
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)

  const optimizedHandler = useCallback((...args: T) => {
    const now = Date.now()

    // Prevent double-clicks
    if (preventDoubleClick && now - lastClickRef.current < debounceMs) {
      return
    }

    // Throttle if specified
    if (throttleMs > 0 && now - lastClickRef.current < throttleMs) {
      return
    }

    // Prevent concurrent execution
    if (isProcessingRef.current) {
      return
    }

    lastClickRef.current = now

    // Clear any pending timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }

    // Debounce the actual execution
    timeoutRef.current = setTimeout(() => {
      // Use requestAnimationFrame for non-blocking execution
      requestAnimationFrame(async () => {
        isProcessingRef.current = true

        try {
          if (measurePerformance) {
            await performanceMonitor.measureAsync('click-handler', async () => {
              await handler(...args)
            })
          } else {
            await handler(...args)
          }
        } catch (error) {
          console.error('Click handler error:', error)
        } finally {
          isProcessingRef.current = false
        }
      })
    }, debounceMs)
  }, [handler, debounceMs, throttleMs, measurePerformance, preventDoubleClick])

  // Cleanup on unmount
  const cleanup = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    isProcessingRef.current = false
  }, [])

  return {
    onClick: optimizedHandler,
    cleanup,
    isProcessing: () => isProcessingRef.current
  }
}

/**
 * Hook for optimized form submissions
 */
export function useOptimizedSubmit<T extends any[]>(
  handler: (...args: T) => void | Promise<void>,
  options: OptimizedClickOptions = {}
) {
  const optimizedOptions = {
    debounceMs: 300, // Longer debounce for forms
    preventDoubleClick: true,
    measurePerformance: true,
    ...options
  }

  return useOptimizedClick(handler, optimizedOptions)
}

/**
 * Hook for optimized button clicks with visual feedback
 */
export function useOptimizedButton<T extends any[]>(
  handler: (...args: T) => void | Promise<void>,
  options: OptimizedClickOptions = {}
) {
  const optimizedOptions = {
    debounceMs: 150,
    preventDoubleClick: true,
    measurePerformance: true,
    ...options
  }

  const { onClick, cleanup, isProcessing } = useOptimizedClick(handler, optimizedOptions)

  return {
    onClick,
    cleanup,
    isProcessing,
    disabled: isProcessing() // Can be used to disable button during processing
  }
}

/**
 * Higher-order component for optimizing click handlers
 */
export function withOptimizedClick<P extends { onClick?: (...args: any[]) => void }>(
  Component: React.ComponentType<P>,
  options: OptimizedClickOptions = {}
) {
  return function OptimizedClickComponent(props: P) {
    const { onClick: originalOnClick, ...otherProps } = props

    const { onClick: optimizedOnClick } = useOptimizedClick(
      originalOnClick || (() => {}),
      options
    )

    return React.createElement(Component, {
      ...otherProps,
      onClick: optimizedOnClick
    } as P)
  }
}

/**
 * Utility for measuring click handler performance
 */
export function measureClickPerformance<T extends any[]>(
  handler: (...args: T) => void | Promise<void>,
  name: string = 'click-handler'
) {
  return async (...args: T) => {
    const startTime = performance.now()
    
    try {
      await handler(...args)
    } finally {
      const duration = performance.now() - startTime
      
      if (duration > 100) {
        console.warn(`🐌 Slow click handler: ${name} took ${duration.toFixed(2)}ms`)
      }
      
      performanceMonitor.end(name)
    }
  }
}
