'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'

export default function MigratePage() {
  const [result, setResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  const runMigration = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/migrate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include'
      })

      const data = await response.json()
      setResult(data)
    } catch (error: unknown) {
      setResult({ error: error instanceof Error ? error.message : 'Unknown error' })
    } finally {
      setLoading(false)
    }
  }

  const checkMigrationStatus = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/migrate', {
        method: 'GET',
        credentials: 'include'
      })

      const data = await response.json()
      setResult(data)
    } catch (error: unknown) {
      setResult({ error: error instanceof Error ? error.message : 'Unknown error' })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto p-8">
      <h1 className="text-2xl font-bold mb-4">Database Migration</h1>
      
      <div className="space-y-4">
        <Button onClick={checkMigrationStatus} disabled={loading}>
          Check Migration Status
        </Button>
        
        <Button onClick={runMigration} disabled={loading}>
          Run Migration
        </Button>
        
        {result && (
          <div className="mt-4 p-4 bg-gray-100 rounded">
            <h3 className="font-bold">Result:</h3>
            <pre className="mt-2 text-sm overflow-auto">
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
        )}
      </div>
    </div>
  )
}
