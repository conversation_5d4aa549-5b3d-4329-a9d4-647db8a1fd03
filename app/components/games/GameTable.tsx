"use client"

import { useSafeTranslation } from '../../hooks/useSafeTranslation'
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger } from "@/components/ui/select"
import { Timer } from 'lucide-react'
import { formatCurrency, type CurrencySettings } from "../../lib/currency"

interface GameTableData {
  id: string
  number: number
  name: string
  isActive: boolean
  hourlyRate: number
  tableType: string
}

interface Game {
  id: string
  tableNumber: number
  startTime: Date
  endTime?: Date
  duration: number
  cost: number
  status: "active" | "completed"
  timeLimit?: number | "unlimited"
  hasAlerted?: boolean
  created_by_name?: string
  created_by_username?: string
  created_by_role?: string
}

interface GameTableProps {
  gameTable: GameTableData
  activeGame?: Game
  currentTime: number
  timeLimit: number | "unlimited"
  currencySettings: CurrencySettings | null
  onStartGame: (tableNumber: number) => void
  onEndGame: (gameId: string) => void
  onSetTimeLimit: (tableNumber: number, limit: number | "unlimited") => void
}

// Keep the original structure but we'll translate labels in the component

const formatTime = (minutes: number): string => {
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  return `${hours.toString().padStart(2, "0")}:${mins.toString().padStart(2, "0")}`
}

const calculateCostFromMinutes = (minutes: number, hourlyRate: number): number => {
  return Math.round((minutes / 60) * hourlyRate)
}

export function GameTable({
  gameTable,
  activeGame,
  currentTime,
  timeLimit,
  currencySettings,
  onStartGame,
  onEndGame,
  onSetTimeLimit
}: GameTableProps) {
  const { t } = useSafeTranslation()

  const timeLimitOptions = [
    { value: "unlimited", label: t('games.unlimited') },
    { value: 60, label: t('games.oneHour') },
    { value: 120, label: t('games.twoHours') },
    { value: 180, label: t('games.threeHours') },
    { value: 240, label: t('games.fourHours') },
    { value: 300, label: t('games.fiveHours') },
    { value: 360, label: t('games.sixHours') },
  ]

  const hasActiveGame = !!activeGame
  const isTableActive = gameTable.isActive
  const tableNumber = gameTable.number

  // Simplified table availability logic (no reservations)
  const canUseTable = isTableActive

  const getTimeLeftPercentage = () => {
    if (!hasActiveGame || activeGame.timeLimit === "unlimited" || typeof activeGame.timeLimit !== "number") return 100
    const elapsed = currentTime || 0
    const percentage = Math.max(0, 100 - (elapsed / activeGame.timeLimit) * 100)
    return percentage
  }

  const getRemainingTime = () => {
    if (!hasActiveGame || !activeGame.timeLimit || activeGame.timeLimit === "unlimited") return null
    const totalMinutes = typeof activeGame.timeLimit === 'number' ? activeGame.timeLimit : 0
    if (totalMinutes <= 0) return null

    const elapsedMinutes = (Date.now() - activeGame.startTime.getTime()) / (1000 * 60)
    const remainingMinutes = Math.max(0, totalMinutes - elapsedMinutes)
    
    if (remainingMinutes <= 0) return t('games.timesUp')
    
    const hours = Math.floor(remainingMinutes / 60)
    const mins = Math.floor(remainingMinutes % 60)
    
    if (remainingMinutes < 1) {
      const secs = Math.floor((remainingMinutes % 1) * 60)
      return t('games.secondsLeft', { seconds: secs })
    }

    return hours > 0 ? t('games.hoursMinutesLeft', { hours, minutes: mins }) : t('games.minutesLeft', { minutes: mins })
  }

  const getCurrentCost = () => {
    if (!hasActiveGame) return 0
    const preciseSeconds = (Date.now() - activeGame.startTime.getTime()) / 1000
    const preciseMinutes = preciseSeconds / 60
    return calculateCostFromMinutes(preciseMinutes, gameTable.hourlyRate)
  }

  const defaultCurrency = {
    currency: 'Albanian Lek',
    symbol: 'L',
    showDecimals: false,
    taxIncluded: false,
    taxEnabled: true,
    taxRate: 20,
    qrCodeEnabled: false,
    qrCodeUrl: '',
  }

  const currency = currencySettings || defaultCurrency

  return (
    <div className="relative">
      <div
        className={`
          group relative w-full rounded-2xl overflow-hidden shadow-xl border-2 backdrop-blur-sm transition-all duration-200
          ${hasActiveGame
            ? 'bg-gradient-to-br from-emerald-500 via-green-500 to-teal-600 border-emerald-300 shadow-emerald-200/50'
            : canUseTable
              ? 'bg-gradient-to-br from-blue-500 via-indigo-500 to-purple-600 border-blue-300 shadow-blue-200/50 cursor-pointer hover:shadow-xl'
              : 'bg-gradient-to-br from-gray-400 via-gray-500 to-slate-600 border-gray-300 opacity-80'
          }
        `}
        style={{ aspectRatio: '1.4', minHeight: '250px' }}
        onClick={() => {
          if (canUseTable) {
            onStartGame(tableNumber)
          }
        }}
      >
        {/* Glass Overlay */}
        <div className="absolute inset-0 bg-white/10 backdrop-blur-[2px]"></div>

        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-4 left-4 w-3 h-3 bg-white rounded-full"></div>
          <div className="absolute top-4 right-4 w-3 h-3 bg-white rounded-full"></div>
          <div className="absolute bottom-4 left-4 w-3 h-3 bg-white rounded-full"></div>
          <div className="absolute bottom-4 right-4 w-3 h-3 bg-white rounded-full"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-12 h-12 border-2 border-white rounded-full"></div>
        </div>

        {/* Billiard Icon */}
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-4xl opacity-20">
          🎱
        </div>

        {/* Content */}
        <div className="relative h-full flex flex-col text-white p-2 z-10">
          {/* Header */}
          <div className="flex items-center justify-between mb-1">
            <div className="flex items-center gap-1">
              <div className={`w-1 h-1 rounded-full ${
                hasActiveGame ? 'bg-green-400'
                : isTableActive ? 'bg-blue-400'
                : 'bg-gray-400'
              }`}></div>
              <div className="text-xs font-bold truncate max-w-[120px]" title={gameTable.name}>
                {gameTable.name || `${t('games.table')} ${tableNumber}`}
              </div>
            </div>
            <div className={`px-1 py-0.5 rounded text-xs font-medium ${
              hasActiveGame ? 'bg-green-500/30 text-green-100'
              : isTableActive ? 'bg-blue-500/30 text-blue-100'
              : 'bg-gray-500/30 text-gray-100'
            }`}>
              {hasActiveGame ? t('games.live')
               : isTableActive ? t('games.ready')
               : t('games.off')}
            </div>
          </div>

          {/* Time Limit Display */}
          <div className="text-center mb-1">
            <div className="text-xs opacity-75 font-medium">
              {t('games.default')}: {timeLimit === "unlimited" ? "∞" : typeof timeLimit === 'number' ? `${Math.floor(timeLimit / 60)}h` : "∞"}
            </div>
          </div>

          {/* Progress Bar for Active Games */}
          {hasActiveGame && activeGame.timeLimit !== "unlimited" && (
            <div className="mb-1">
              <div className="w-full bg-white/20 rounded-full h-1">
                <div
                  className="bg-gradient-to-r from-green-400 via-yellow-400 to-red-500 h-1 rounded-full transition-all duration-1000"
                  style={{ width: `${getTimeLeftPercentage()}%` }}
                />
              </div>
              <div className="flex justify-between text-xs opacity-60 mt-0.5">
                <span>{getRemainingTime()}</span>
                <span>
                  {typeof activeGame.timeLimit === 'number' && activeGame.timeLimit > 0 ?
                    `${Math.floor(activeGame.timeLimit / 60)}h ${t('games.limit')}` :
                    t('games.noLimit')
                  }
                </span>
              </div>
            </div>
          )}

          {/* Main Content */}
          <div className="flex-1 flex flex-col justify-evenly min-h-0">
            {hasActiveGame ? (
              <>
                {/* Game Time */}
                <div className="text-center">
                  <div className="text-base font-black tracking-wide">
                    {formatTime(currentTime)}
                  </div>
                </div>

                {/* User Badge */}
                <div className="flex justify-center">
                  <div className="flex items-center gap-1 bg-white/25 px-2 py-1 rounded-full text-xs">
                    <div className="w-1 h-1 bg-green-400 rounded-full"></div>
                    <span className="font-medium truncate max-w-20">
                      {activeGame.created_by_name || activeGame.created_by_username || t('games.player')}
                    </span>
                  </div>
                </div>

                {/* Cost Display */}
                <div className="flex justify-center">
                  <div className="bg-white/25 px-3 py-1 rounded-full text-center">
                    <div className="text-sm font-bold text-white">
                      {formatCurrency(getCurrentCost(), currency)}
                    </div>
                  </div>
                </div>
              </>
            ) : canUseTable ? (
              <div className="text-center space-y-1">
                <div className="text-sm font-bold">
                  {t('games.readyToPlay')}
                </div>
                <div className="text-xs opacity-90">
                  {t('games.tapToStart')}
                </div>
                <div className="bg-white/20 px-2 py-0.5 rounded-full text-xs font-semibold">
                  {formatCurrency(gameTable.hourlyRate, currency)} {t('games.perHour')}
                </div>
              </div>
            ) : (
              <div className="text-center">
                <div className="text-xl opacity-50 mb-2">🎱</div>
                <div className="text-xs font-semibold opacity-70">{t('games.offline')}</div>
                <div className="text-xs opacity-50">{t('games.contactStaff')}</div>
              </div>
            )}
          </div>

          {/* Action Controls for Active Games */}
          {hasActiveGame && (
            <div className="space-y-1 mt-1">
              {/* Time Limit Control */}
              <Select
                value={
                  activeGame.timeLimit === "unlimited"
                    ? "unlimited"
                    : activeGame.timeLimit?.toString() || timeLimit.toString()
                }
                onValueChange={(value) =>
                  onSetTimeLimit(tableNumber, value === "unlimited" ? "unlimited" : Number(value))
                }
              >
                <SelectTrigger
                  className="w-full h-6 text-xs bg-white/25 border-white/40 text-white rounded font-medium"
                  onClick={(e) => e.stopPropagation()}
                >
                  <div className="flex items-center justify-center gap-1 w-full">
                    <Timer className="w-2.5 h-2.5" />
                    <span className="text-white/80">{t('games.change')}:</span>
                    <span className="font-bold text-white">
                      {activeGame.timeLimit === "unlimited"
                        ? "∞"
                        : typeof activeGame.timeLimit === 'number'
                          ? `${Math.floor(activeGame.timeLimit / 60)}h`
                          : "∞"
                      }
                    </span>
                  </div>
                </SelectTrigger>
                <SelectContent>
                  {timeLimitOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value.toString()}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {/* Stop Button */}
              <Button
                onClick={(e) => {
                  e.stopPropagation()
                  onEndGame(activeGame.id)
                }}
                className="w-full bg-red-500 hover:bg-red-600 text-white border-0 py-1.5 text-xs font-bold rounded transition-colors"
              >
                🔴 {t('games.stop')}
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
