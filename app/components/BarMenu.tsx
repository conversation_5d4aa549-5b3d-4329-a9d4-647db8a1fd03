"use client"

import { useState, useEffect, useRef, useMemo } from "react"
import { useSafeTranslation } from '../hooks/useSafeTranslation'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import {
  Coffee,
  Plus,
  Minus,
  ShoppingCart,
  Users,
  Clock,
  Eye,
  Printer,
  Search,
  Star,
  TrendingUp,
  ChefHat,
  Sparkles,
  Target,
  BarChart3,
  CheckCircle,
  DollarSign
} from 'lucide-react'
import { logReceiptToHistory } from "../utils/receiptLogger"
import { useAuth } from "../contexts/AuthContext"
import { usePermissions } from "../hooks/usePermissions"
import { useDataRefresh } from "../contexts/DataRefreshContext"
import { fetchCurrencySettings, formatCurrency, formatReceiptItem, formatReceiptTotals, generateQRCode, type CurrencySettings } from "../lib/currency"
import { formatLocalizedDate } from "../utils/dateLocalization"
import {
  setUserSetting,
  getUserSetting,
  configureStorage,
  getStorageManager
} from "../utils/storage"
import { debounce } from "../utils/debounce"
import type { Order, OrderItem, DrinkItem, TableOrder } from "../types"
// Removed hardcoded table imports - now fully database-driven
interface BarTable {
  id: number
  number: number
  name: string
  isActive: boolean
  assignedUserId?: number
  assignedUsername?: string
}

interface GameTable {
  id: number
  number: number
  name: string
  isActive: boolean
  assignedUserId?: number
  assignedUsername?: string
}

interface BarMenuProps {
  recentOrders: Order[]
  setRecentOrders: (orders: Order[] | ((prev: Order[]) => Order[])) => void
  setTotalRevenue: (revenue: number | ((prev: number) => number)) => void
}

export function BarMenu({ recentOrders, setRecentOrders, setTotalRevenue }: BarMenuProps) {
  const { t, i18n } = useSafeTranslation()
  const { token } = useAuth()
  const { filterBarData } = usePermissions()
  const { refreshTrigger } = useDataRefresh()
  const barMenuRef = useRef<HTMLDivElement>(null)
  const tableSelectionRef = useRef<HTMLDivElement>(null)
  const [selectedTable, setSelectedTable] = useState<number | null>(null)
  const [tableOrders, setTableOrders] = useState<{ [key: number]: TableOrder }>({})
  const [availableTables, setAvailableTables] = useState<BarTable[]>([])
  const [allBarTables, setAllBarTables] = useState<BarTable[]>([])
  const [categories, setCategories] = useState<Array<{ key: string; label: string; icon: any }>>([])
  const [products, setProducts] = useState<DrinkItem[]>([])
  const [availableGameTables, setAvailableGameTables] = useState<GameTable[]>([])
  const [allGameTables, setAllGameTables] = useState<GameTable[]>([])
  // Initialize selectedCategory from localStorage or default to "all"
  const [selectedCategory, setSelectedCategory] = useState<string>(() => {
    if (typeof window !== 'undefined') {
      const savedCategory = localStorage.getItem('bbm_bar_selected_category')
      if (savedCategory) {
        return savedCategory
      }
    }
    return "all"
  })

  // Initialize searchQuery from localStorage or default to ""
  const [searchQuery, setSearchQuery] = useState<string>(() => {
    if (typeof window !== 'undefined') {
      const savedQuery = localStorage.getItem('bbm_bar_search_query')
      if (savedQuery) {
        return savedQuery
      }
    }
    return ""
  })
  const [isLocalStorageLoaded, setIsLocalStorageLoaded] = useState(false)

  const [quickFilters, setQuickFilters] = useState({
    popular: false,
    recent: false,
    favorites: false
  })
  const [orderStats, setOrderStats] = useState({
    todayOrders: 0,
    todayRevenue: 0,
    avgOrderValue: 0,
    popularItems: [] as string[]
  })
  const [businessInfo, setBusinessInfo] = useState<{
    name: string
    address: string
    phone: string
    email: string
    vat_number: string
  } | null>(null)
  const [currencySettings, setCurrencySettings] = useState<CurrencySettings | null>(null)
  const [showOrderPreview, setShowOrderPreview] = useState(false)
  const [previewOrder, setPreviewOrder] = useState<Order | null>(null)
  const [showDailyReport, setShowDailyReport] = useState(false)
  const [todayOrders, setTodayOrders] = useState<any[]>([])
  const [showMonthlyReport, setShowMonthlyReport] = useState(false)
  const [monthOrders, setMonthOrders] = useState<any[]>([])
  const [showReportTypeDialog, setShowReportTypeDialog] = useState(false)
  const [pendingReportType, setPendingReportType] = useState<'daily' | 'monthly' | null>(null)
  const [reportIncludes, setReportIncludes] = useState<{games: boolean, orders: boolean}>({games: false, orders: true})
  const [todayGames, setTodayGames] = useState<any[]>([])
  const [monthGames, setMonthGames] = useState<any[]>([])
  const [allGames, setAllGames] = useState<any[]>([])

  // Load tables with user assignment (only user's assigned tables)
  useEffect(() => {
    const loadTables = async () => {
      try {
        const response = await fetch('/api/tables/user', {
          credentials: 'include'
        })

        if (response.ok) {
          const tablesData = await response.json()
          console.log('🔍 Raw tables data from API:', tablesData)

          const mappedTables = tablesData
            .filter((t: any) => t.is_active) // Only active tables
            .map((t: any) => ({
              id: t.id,
              number: t.number,
              name: t.name,
              isActive: t.is_active,
              assignedUserId: t.assigned_user_id,
              assignedUsername: t.assigned_username
            }))

          console.log('🔍 Filtered active tables:', mappedTables)
          console.log('🔍 Total tables from API:', tablesData.length)
          console.log('🔍 Active tables after filter:', mappedTables.length)

          setAvailableTables(mappedTables)
          console.log('✅ Loaded user bar tables:', mappedTables.length, 'tables')
        } else {
          console.error('❌ Failed to load bar tables from database, status:', response.status)
          const errorText = await response.text()
          console.error('❌ Error details:', errorText)
          // No fallback - database is required
          setAvailableTables([])
        }
      } catch (error) {
        console.error('❌ Failed to load bar tables:', error)
        // No fallback - database is required
        setAvailableTables([])
      }
    }

    loadTables()
  }, [])

  // Load game tables with user assignment (only user's assigned tables)
  useEffect(() => {
    const loadGameTables = async () => {
      try {
        const response = await fetch('/api/gametables/user', {
          credentials: 'include'
        })
        if (response.ok) {
          const tablesData = await response.json()
          const mappedTables = tablesData
            .filter((t: any) => t.is_active)
            .map((t: any) => ({
              id: t.id,
              number: t.number,
              name: t.name,
              isActive: t.is_active,
              assignedUserId: t.assigned_user_id,
              assignedUsername: t.assigned_username
            }))
          setAvailableGameTables(mappedTables)
        } else {
          setAvailableGameTables([])
        }
      } catch (error) {
        console.error('❌ Failed to load game tables:', error)
        setAvailableGameTables([])
      }
    }
    loadGameTables()
  }, [])

  // New: Load all bar tables for report lookups
  useEffect(() => {
    const loadAllBarTables = async () => {
      try {
        const response = await fetch('/api/tables')
        if (response.ok) {
          const tablesData = await response.json()
          setAllBarTables(
            tablesData.map((t: any) => ({
              id: t.id,
              number: t.number,
              name: t.name,
              isActive: t.is_active,
            }))
          )
        }
      } catch (error) {
        console.error('Failed to load all bar tables for report lookup:', error)
      }
    }
    loadAllBarTables()
  }, [])

  // New: Load all game tables for report lookups
  useEffect(() => {
    const loadAllGameTables = async () => {
      try {
        const response = await fetch('/api/gametables')
        if (response.ok) {
          const tablesData = await response.json()
          setAllGameTables(
            tablesData.map((t: any) => ({
              id: t.id,
              number: t.number,
              name: t.name,
              isActive: t.is_active,
            }))
          )
        }
      } catch (error) {
        console.error('Failed to load all game tables for report lookup:', error)
      }
    }
    loadAllGameTables()
  }, [])

  // Load draft orders after tables are available
  useEffect(() => {
    const loadDraftOrders = async () => {
      if (availableTables.length === 0 || !isLocalStorageLoaded) return

      try {
        console.log("🔍 Loading draft orders for available tables...")
        const localStorage = getStorageManager('local')
        const loadedOrders: { [key: number]: TableOrder } = {}

        // Load draft orders for all available tables
        for (const table of availableTables) {
          try {
            console.log(`🔍 Checking Local Storage for table ${table.number} with key: bar_draft_order_${table.number}`)
            const orderData = await localStorage.getJSON<TableOrder>(`bar_draft_order_${table.number}`)
            console.log(`📥 Raw data for table ${table.number}:`, orderData)

            if (orderData && orderData.items && orderData.items.length > 0) {
              console.log(`✅ Loading saved draft order for table ${table.number}:`, orderData)
              loadedOrders[table.number] = {
                ...orderData,
                lastUpdated: new Date(orderData.lastUpdated || new Date())
              }
            } else {
              console.log(`ℹ️ No valid draft order found for table ${table.number}`)
            }
          } catch (error) {
            console.warn(`❌ Failed to load draft order for table ${table.number}:`, error)
          }
        }

        if (Object.keys(loadedOrders).length > 0) {
          console.log(`✅ Restored ${Object.keys(loadedOrders).length} draft orders from Local Storage:`, loadedOrders)
          setTableOrders(loadedOrders)
        } else {
          console.log("ℹ️ No draft orders found in Local Storage")
        }
      } catch (error) {
        console.error("❌ Error loading draft orders:", error)
      }
    }

    loadDraftOrders()
  }, [availableTables, isLocalStorageLoaded])

  // Configure storage with auth token when it changes
  useEffect(() => {
    if (token) {
      configureStorage({ authToken: token })
    }
  }, [token])

  // Create debounced save functions to prevent excessive API calls
  const _debouncedSaveTableOrders = debounce(async (orders: { [key: number]: TableOrder }) => {
    // Save each table order to database instead of memory storage
    for (const [tableNumber, orderData] of Object.entries(orders)) {
      try {
        const response = await fetch('/api/draft-orders', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include', // Use cookies for authentication
          body: JSON.stringify({
            tableNumber: parseInt(tableNumber),
            orderData
          }),
        });
        
        if (!response.ok) {
          console.error(`Failed to save draft order for table ${tableNumber}`);
        }
      } catch (error) {
        console.error(`Error saving draft order for table ${tableNumber}:`, error);
      }
    }
  }, 500) // 500ms debounce

  const debouncedSaveUserSettings = debounce(async (key: string, value: any) => {
    // Save user settings to database (if authenticated)
    await setUserSetting(key, value)
  }, 1000) // 1 second debounce for database operations

  // Load settings and set hardcoded data
  useEffect(() => {
    const loadSettings = async () => {
      try {
        // Load business information with timeout
        try {
          const businessResponse = await fetch('/api/business-info', {
            signal: AbortSignal.timeout(5000) // 5 second timeout
          })
          if (businessResponse.ok) {
            const businessData = await businessResponse.json()
            setBusinessInfo(businessData)
          }
        } catch (error) {
          console.warn('Failed to load business info:', error)
        }

        // Load currency settings with timeout
        try {
          const currencyData = await fetchCurrencySettings()
          setCurrencySettings(currencyData)
        } catch (error) {
          console.warn('Failed to load currency settings:', error)
          // Set default currency settings
          setCurrencySettings({
            currency: t('hardcoded.albanianLek'),
            symbol: 'L',
            showDecimals: false,
            taxIncluded: false,
            taxEnabled: true,
            taxRate: 20,
            qrCodeEnabled: false,
            qrCodeUrl: '',
          })
        }

        // Load products from database with timeout
        try {
          const productsResponse = await fetch('/api/menu-items', {
            signal: AbortSignal.timeout(5000) // 5 second timeout
          })
          if (productsResponse.ok) {
            const productsData = await productsResponse.json()
            setProducts(productsData.filter((p: any) => p.available).map((p: any) => ({
              id: p.id,
              name: p.name,
              price: p.price,
              category: p.category,
              isActive: p.available
            })))
          }
        } catch (error) {
          console.warn('Failed to load products:', error)
        }

        // Tables are already loaded synchronously in separate useEffect above

        // Load games data
        const gamesResponse = await fetch('/api/games', {
          credentials: 'include' // Use cookies for authentication
        })
        if (gamesResponse.ok) {
          const gamesData = await gamesResponse.json()
          const gamesWithDates = gamesData.map((game: any) => ({
            ...game,
            id: game.id.toString(),
            startTime: new Date(game.start_time),
            endTime: game.end_time ? new Date(game.end_time) : undefined,
            tableNumber: game.table_number,
            status: game.status,
            created_by_username: game.created_by_username,
            created_by_name: game.created_by_name,
            created_by_role: game.created_by_role
          }))
          setAllGames(gamesWithDates)
        }

        // Load categories from database with timeout
        try {
          const response = await fetch('/api/categories', {
            signal: AbortSignal.timeout(5000) // 5 second timeout
          })
          if (response.ok) {
            const categoriesData = await response.json()
            const activeCategories = categoriesData
              .filter((c: any) => c.is_active)
              .sort((a: any, b: any) => a.display_order - b.display_order)
              .map((c: any) => ({
                key: c.key,
                label: c.name,
                icon: Coffee, // You can map different icons later
              }))
            setCategories(activeCategories)
          } else {
            // Default categories if database is empty
            const defaultCategories = [
              { key: "drinks", label: t('bar.drinks'), icon: Coffee },
              { key: "snacks", label: t('bar.snacks'), icon: Coffee },
              { key: "food", label: t('bar.food'), icon: Coffee },
            ]
            setCategories(defaultCategories)
          }
        } catch (error) {
          console.warn('Failed to load categories:', error)
          // Default categories if fetch fails
          const defaultCategories = [
            { key: "drinks", label: t('bar.drinks'), icon: Coffee },
            { key: "snacks", label: t('bar.snacks'), icon: Coffee },
            { key: "food", label: t('bar.food'), icon: Coffee },
          ]
          setCategories(defaultCategories)
        }
      } catch (error) {
        console.error("Failed to load settings:", error)
      }
    }

    loadSettings()

    // Listen for settings changes
    const handleStorageChange = () => {
      loadSettings()
    }

    window.addEventListener("storage", handleStorageChange)
    return () => window.removeEventListener("storage", handleStorageChange)
  }, [])

  // Refresh orders data when refresh trigger changes
  useEffect(() => {
    const refreshOrdersData = async () => {
      try {
        console.log('Refreshing orders data due to data refresh trigger')
        const ordersResponse = await fetch('/api/orders', {
          credentials: 'include' // Include cookies for authentication
        })
        if (ordersResponse.ok) {
          const orders = await ordersResponse.json()
          // Ensure orders is an array before mapping
          const ordersArray = Array.isArray(orders) ? orders : []
          const ordersWithDates = ordersArray.map((order: any) => ({
            ...order,
            id: order.id.toString(),
            created_at: new Date(order.created_at),
            items: order.items.map((item: any, index: number) => ({
              ...item,
              id: item.id ? Number(item.id) : (order.id * 1000) + index + 1,
              isActive: true
            }))
          }))
          setRecentOrders(ordersWithDates)
        }
      } catch (error) {
        console.error('Failed to refresh orders data:', error)
      }
    }

    if (refreshTrigger > 0) {
      refreshOrdersData()
    }
  }, [refreshTrigger, setRecentOrders])

  // Load table orders and preferences from storage on mount (optimized)
  useEffect(() => {
    const loadDataFromStorage = async () => {
      try {
        console.log("🔄 Loading Bar data from storage...")

        // Load user settings with optimized non-blocking approach
        // First try localStorage (fast), then database (background)
        const loadUserSettings = async () => {
          try {
            // Try localStorage first (fast)
            const localStorage = getStorageManager('local')
            const localSavedTable = await localStorage.get('setting_selected_table')
            let parsedLocalTable: number | null = null

            if (localSavedTable) {
              try {
                parsedLocalTable = JSON.parse(localSavedTable)
                console.log("✅ Loading saved selected table from localStorage:", parsedLocalTable)
                setSelectedTable(parsedLocalTable)
              } catch (error) {
                console.warn("⚠️ Failed to parse localStorage table:", error)
              }
            }

            // Load from database in background (non-blocking)
            setTimeout(async () => {
              try {
                const dbSavedTable = await getUserSetting<number>('selected_table')
                if (dbSavedTable && dbSavedTable !== parsedLocalTable) {
                  console.log("✅ Syncing selected table from database:", dbSavedTable)
                  setSelectedTable(dbSavedTable)
                  // Update localStorage with database value
                  await localStorage.set('setting_selected_table', JSON.stringify(dbSavedTable))
                }
              } catch (error) {
                console.warn("⚠️ Database settings load failed (non-blocking):", error)
              }
            }, 100) // Small delay to not block initial render
          } catch (error) {
            console.warn("⚠️ Settings load failed:", error)
          }
        }

        // Load settings asynchronously
        loadUserSettings()

        // Load draft orders from Local Storage (persistent across browser refresh)
        console.log("🔍 Loading draft orders from Local Storage...")
        const localStorage = getStorageManager('local')
        const loadedOrders: { [key: number]: TableOrder } = {}

        // Draft orders are now loaded in a separate effect after tables are available
        console.log('ℹ️ Draft orders will be loaded after tables are available')

        setIsLocalStorageLoaded(true);
      } catch (error) {
        console.error("❌ Error loading from storage:", error);
        setIsLocalStorageLoaded(true);
      }
    }

    loadDataFromStorage();
  }, []);

  // Save table orders immediately to Local Storage when they change (only after initial load)
  useEffect(() => {
    if (isLocalStorageLoaded) {
      console.log("💾 Saving table orders to Local Storage:", tableOrders)

      // Save each table order individually and immediately to Local Storage
      const saveOrders = async () => {
        const localStorage = getStorageManager('local')

        for (const [tableNum, order] of Object.entries(tableOrders)) {
          if (order && order.items && order.items.length > 0) {
            try {
              console.log(`💾 Saving draft order for table ${tableNum} to Local Storage:`, order)
              await localStorage.setJSON(`bar_draft_order_${tableNum}`, order)
              console.log(`✅ Successfully saved draft order for table ${tableNum} to Local Storage`)
            } catch (error) {
              console.error(`❌ Failed to save draft order for table ${tableNum} to Local Storage:`, error)
            }
          } else {
            // Clear empty orders from Local Storage
            try {
              await localStorage.delete(`bar_draft_order_${tableNum}`)
              console.log(`🗑️ Cleared empty draft order for table ${tableNum} from Local Storage`)
            } catch (error) {
              // Ignore errors for non-existent keys
            }
          }
        }
      }

      saveOrders()
    }
  }, [tableOrders, isLocalStorageLoaded])

  // Save selected table with debouncing
  useEffect(() => {
    if (selectedTable !== null) {
      debouncedSaveUserSettings('selected_table', selectedTable)
    }
  }, [selectedTable, debouncedSaveUserSettings])

  // Save selected category with debouncing
  useEffect(() => {
    if (selectedCategory) {
      debouncedSaveUserSettings('selected_category', selectedCategory)
    }
  }, [selectedCategory, debouncedSaveUserSettings])

  // Save search query with debouncing
  useEffect(() => {
    if (searchQuery) {
      debouncedSaveUserSettings('search_query', searchQuery)
    }
  }, [searchQuery, debouncedSaveUserSettings])

  // Save selectedCategory to localStorage whenever it changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('bbm_bar_selected_category', selectedCategory)
    }
  }, [selectedCategory])

  // Save searchQuery to localStorage whenever it changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('bbm_bar_search_query', searchQuery)
    }
  }, [searchQuery])

  // Smart filtering and analytics
  const filteredProducts = useMemo(() => {
    let filtered = products

    // Category filter
    if (selectedCategory !== "all") {
      filtered = filtered.filter(p => p.category === selectedCategory)
    }

    // Search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(p =>
        p.name.toLowerCase().includes(query) ||
        p.category.toLowerCase().includes(query)
      )
    }

    // Quick filters
    if (quickFilters.popular) {
      filtered = filtered.filter(p => orderStats.popularItems.includes(p.name))
    }

    return filtered
  }, [products, selectedCategory, searchQuery, quickFilters, orderStats.popularItems])

  // Calculate smart insights
  const smartInsights = useMemo(() => {
    const totalTables = availableTables.length
    const tablesWithOrders = Object.keys(tableOrders).length
    const totalItems = Object.values(tableOrders).reduce((sum, order) => sum + order.items.length, 0)
    const totalValue = Object.values(tableOrders).reduce((sum, order) => sum + order.total, 0)

    return {
      utilization: totalTables > 0 ? Math.round((tablesWithOrders / totalTables) * 100) : 0,
      avgOrderSize: tablesWithOrders > 0 ? Math.round(totalItems / tablesWithOrders) : 0,
      pendingValue: totalValue,
      busyTables: tablesWithOrders
    }
  }, [availableTables.length, tableOrders])

  // Get current order for selected table
  const getCurrentOrder = (): OrderItem[] => {
    if (!selectedTable || !tableOrders[selectedTable]) return []
    return tableOrders[selectedTable].items
  }

  // Update table order
  const _updateTableOrder = (tableNumber: number, items: OrderItem[]) => {
    const total = Number(items.reduce((sum, item) => sum + item.price * item.quantity, 0))
    setTableOrders((prev) => ({
      ...prev,
      [tableNumber]: {
        table_number: tableNumber,
        items,
        total,
        lastUpdated: new Date(),
      },
    }))
  }

  // Add item to current table order
  const addToOrder = async (item: DrinkItem) => {
    if (!selectedTable) {
      console.log("No table selected, cannot add item")
      return
    }
    console.log("Adding item to table", selectedTable, ":", item)

    // Note: Table status updates removed since we're using hardcoded tables
    // Tables are managed via Settings -> Bar Tables for hardcoded configuration

    setTableOrders((prev) => {
      const currentOrder = prev[selectedTable] || {
        table_number: selectedTable,
        items: [],
        total: 0,
        lastUpdated: new Date(),
      }

      const existingItem = currentOrder.items.find((i) => i.id === item.id)

      if (existingItem) {
        const updatedItems = currentOrder.items.map((i) =>
          i.id === item.id ? { ...i, quantity: i.quantity + 1 } : i
        )
        const newTotal = Number(updatedItems.reduce((sum, i) => sum + (Number(i.price) * Number(i.quantity)), 0))
        return {
          ...prev,
          [selectedTable]: {
            ...currentOrder,
            items: updatedItems,
            total: newTotal,
            lastUpdated: new Date(),
          },
        }
      }

      const newItem: OrderItem = {
        id: item.id,
        name: item.name,
        price: Number(item.price),
        quantity: 1,
        category: item.category,
        isActive: item.isActive,
      }

      const newTotal = Number(currentOrder.total) + Number(item.price)
      const updatedOrder = {
        ...currentOrder,
        items: [...currentOrder.items, newItem],
        total: newTotal,
        lastUpdated: new Date(),
      }

      // Immediately save to Local Storage after adding item
      setTimeout(async () => {
        try {
          console.log(`💾 Immediately saving after adding item to table ${selectedTable} to Local Storage:`, updatedOrder)
          const localStorage = getStorageManager('local')
          await localStorage.setJSON(`bar_draft_order_${selectedTable}`, updatedOrder)
          console.log(`✅ Immediately saved draft order for table ${selectedTable} to Local Storage`)
        } catch (error) {
          console.error(`❌ Failed to immediately save draft order for table ${selectedTable} to Local Storage:`, error)
        }
      }, 100) // Small delay to ensure state is updated

      return {
        ...prev,
        [selectedTable]: updatedOrder,
      }
    })
  }

  // Decrease quantity or remove item if quantity becomes 0
  const decreaseQuantity = (itemId: number) => {
    if (!selectedTable) return

    setTableOrders((prev) => {
      const currentOrder = prev[selectedTable]
      const updatedItems = currentOrder.items.map((item) => {
        if (item.id === itemId) {
          const newQuantity = item.quantity - 1
          return newQuantity > 0 ? { ...item, quantity: newQuantity } : null
        }
        return item
      }).filter(Boolean) as OrderItem[] // Remove null items (quantity 0)

      const newTotal = Number(updatedItems.reduce((sum, i) => sum + (Number(i.price) * Number(i.quantity)), 0))
      return {
        ...prev,
        [selectedTable]: {
          ...currentOrder,
          items: updatedItems,
          total: newTotal,
          lastUpdated: new Date(),
        },
      }
    })
  }

  // Remove item completely from current table order
  const removeFromOrder = (itemId: number) => {
    if (!selectedTable) return

    setTableOrders((prev) => {
      const currentOrder = prev[selectedTable]
      const updatedItems = currentOrder.items.filter((i) => i.id !== itemId)
      const newTotal = Number(updatedItems.reduce((sum, i) => sum + (Number(i.price) * Number(i.quantity)), 0))
      return {
        ...prev,
        [selectedTable]: {
          ...currentOrder,
          items: updatedItems,
          total: newTotal,
          lastUpdated: new Date(),
        },
      }
    })
  }

  // Increase quantity of an existing item in the order
  const increaseQuantity = (itemId: number) => {
    if (!selectedTable) return

    setTableOrders((prev) => {
      const currentOrder = prev[selectedTable]
      const updatedItems = currentOrder.items.map((i) =>
        i.id === itemId ? { ...i, quantity: i.quantity + 1 } : i
      )
      const newTotal = Number(updatedItems.reduce((sum, i) => sum + (Number(i.price) * Number(i.quantity)), 0))
      return {
        ...prev,
        [selectedTable]: {
          ...currentOrder,
          items: updatedItems,
          total: newTotal,
          lastUpdated: new Date(),
        },
      }
    })
  }

  // Update quantity of an item in the current table order
  const _updateQuantity = (itemId: number, quantity: number) => {
    if (!selectedTable) return

    setTableOrders((prev) => {
      const currentOrder = prev[selectedTable]
      const updatedItems = currentOrder.items.map((i) =>
        i.id === itemId ? { ...i, quantity } : i
      )
      const newTotal = Number(updatedItems.reduce((sum, i) => sum + (Number(i.price) * Number(i.quantity)), 0))
      return {
        ...prev,
        [selectedTable]: {
          ...currentOrder,
          items: updatedItems,
          total: newTotal,
          lastUpdated: new Date(),
        },
      }
    })
  }

  // Calculate total for current table
  const calculateTotal = (): number => {
    if (!selectedTable || !tableOrders[selectedTable]) return 0
    return Number(tableOrders[selectedTable].total)
  }

  // Submit order for current table with auto-print
  const submitOrder = async () => {
    if (!selectedTable || !tableOrders[selectedTable]?.items.length) {
      console.log("Cannot submit order: no table selected or no items")
      return
    }

    console.log("Submitting order for table", selectedTable, "with items:", tableOrders[selectedTable].items)
    console.log("Auth token:", token ? "Present" : "Missing")

    try {
      const orderData = {
        table_number: selectedTable,
        items: tableOrders[selectedTable].items,
        total: tableOrders[selectedTable].total,
        status: "completed",
        created_at: new Date()
      }

      console.log("Order data being sent:", orderData)

      const response = await fetch("/api/orders", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: 'include', // Include cookies for authentication
        body: JSON.stringify(orderData),
      })

      console.log("Order API response status:", response.status)

      if (!response.ok) {
        const errorData = await response.text()
        console.error("Order API error:", errorData)
        throw new Error(`Failed to create order: ${response.status} - ${errorData}`)
      }

      const newOrder = await response.json()
      console.log("Order created successfully:", newOrder)
      setRecentOrders((prev) => [newOrder, ...prev])
      setTotalRevenue((prev) => prev + newOrder.total)

      // Log receipt
      console.log("Logging receipt to history...")
      try {
        await logReceiptToHistory({
          type: "order",
          data: {
            tableNumber: selectedTable,
            cost: tableOrders[selectedTable].total,
            items: tableOrders[selectedTable].items.map(item => ({
              name: item.name,
              price: item.price,
              quantity: item.quantity
            })),
            status: "completed",
            created_at: new Date()
          },
          printedBy: t('hardcoded.systemAutoPrint'),
          printedAt: new Date()
        })
        console.log("Receipt logged successfully")
      } catch (receiptError) {
        console.error("Failed to log receipt:", receiptError)
      }

      // Generate receipt with dynamic currency formatting
      const currency = currencySettings || {
        currency: t('bar.albanianLek'),
        symbol: 'L',
        showDecimals: false,
        taxIncluded: false,
        taxEnabled: true,
        taxRate: 20,
        qrCodeEnabled: false,
        qrCodeUrl: '',
      }

      // Calculate receipt items with proper currency formatting
      const receiptItems = tableOrders[selectedTable].items.map(item => {
        return formatReceiptItem(item.name, item.quantity, Number(item.price), currency)
      })

      // For tax-included mode, we need to pass the sum of original product prices (which include tax)
      // For tax-excluded mode, we pass the subtotal (which excludes tax)
      const orderSubtotal = tableOrders[selectedTable].items.reduce((sum, item) => sum + (item.quantity * Number(item.price)), 0)
      const totalsLines = formatReceiptTotals(orderSubtotal, currency)

      // Generate QR code if enabled
      let qrCodeDataURL = ''
      if (currency.qrCodeEnabled) {
        // Fetch business info for QR code
        let businessInfo = null
        try {
          const businessResponse = await fetch('/api/business-info')
          if (businessResponse.ok) {
            businessInfo = await businessResponse.json()
          }
        } catch (error) {
          console.error('Failed to fetch business info for QR code:', error)
        }

        qrCodeDataURL = await generateQRCode(currency.qrCodeUrl, businessInfo)
      }

      // Auto-print receipt directly without confirmation
      const receiptHtml = `
        <html>
          <head>
            <title>Receipt</title>
            <style>
              @page {
                size: 80mm auto;
                margin: 0;
              }
              body {
                font-family: 'Courier New', monospace;
                width: 80mm;
                margin: 0;
                padding: 3mm;
                font-size: 11px;
                line-height: 1.3;
                color: #000;
              }
              .header {
                text-align: center;
                margin-bottom: 8px;
                border-bottom: 1px solid #000;
                padding-bottom: 5px;
              }
              .header h2 {
                margin: 0;
                font-size: 16px;
                font-weight: bold;
                text-transform: uppercase;
              }
              .header p {
                margin: 1px 0;
                font-size: 10px;
              }
              .info {
                margin: 8px 0;
                font-size: 11px;
              }
              .info p {
                margin: 2px 0;
                display: flex;
                justify-content: space-between;
              }
              .items {
                margin: 8px 0;
              }
              .item {
                margin: 2px 0;
                font-size: 11px;
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
              }
              .item-name {
                flex: 1;
                margin-right: 5px;
              }
              .item-price {
                white-space: nowrap;
                font-weight: bold;
              }
              .total {
                margin-top: 8px;
                border-top: 2px solid #000;
                padding-top: 5px;
                font-weight: bold;
              }
              .total-line {
                display: flex;
                justify-content: space-between;
                margin: 2px 0;
                font-size: 12px;
              }
              .grand-total {
                font-size: 14px;
                border-top: 1px solid #000;
                padding-top: 3px;
                margin-top: 3px;
              }
              .footer {
                text-align: center;
                margin-top: 10px;
                font-size: 9px;
                border-top: 1px dashed #000;
                padding-top: 5px;
              }
              .barcode {
                text-align: center;
                margin-top: 8px;
                font-family: 'Libre Barcode 39', cursive;
                font-size: 20px;
                letter-spacing: 2px;
              }
              .qr-section {
                text-align: center;
                margin: 10px 0;
              }
              .qr-section img {
                width: 60px;
                height: 60px;
                margin: 5px 0;
              }
            </style>
          </head>
          <body>
            <div class="header">
              <h2>${businessInfo?.name || t('hardcoded.billiardClub')}</h2>
              <p>${t('bar.barOrderReceipt')}</p>
              ${businessInfo?.phone ? `<p>${t('bar.tel')}: ${businessInfo.phone}</p>` : ''}
              ${businessInfo?.address ? `<p>${t('bar.address')}: ${businessInfo.address}</p>` : ''}
              ${businessInfo?.vat_number ? `<p>VAT: ${businessInfo.vat_number}</p>` : ''}
            </div>

            <div class="info">
              <p><span>${t('bar.receipt')} #:</span><span>O${newOrder.id.toString().padStart(3, "0")}</span></p>
              <p><span>${t('bar.date')}:</span><span>${(() => {
                const now = new Date()
                const day = now.getDate().toString().padStart(2, '0')
                const month = (now.getMonth() + 1).toString().padStart(2, '0')
                const year = now.getFullYear()
                return `${day}/${month}/${year}`
              })()} ${new Date().toLocaleTimeString()}</span></p>
              <p><span>${t('bar.table')}:</span><span>${getBarTableName(selectedTable)}</span></p>
            </div>

            <div class="items">
              ${receiptItems.map(item => {
                const parts = item.line.split('  ')
                const itemName = parts[0] || ''
                const itemPrice = parts[parts.length - 1] || ''
                return `<div class="item">
                  <span class="item-name">${itemName}</span>
                  <span class="item-price">${itemPrice}</span>
                </div>`
              }).join('')}
            </div>

            <div class="total">
              ${totalsLines.map(line => {
                const parts = line.split(':')
                const label = parts[0] || ''
                const amount = parts[1] || ''
                return `<div class="total-line ${line.includes('TOTAL') ? 'grand-total' : ''}">
                  <span>${label}:</span>
                  <span>${amount.trim()}</span>
                </div>`
              }).join('')}
            </div>

            ${qrCodeDataURL ? `
              <div class="qr-section">
                <img src="${qrCodeDataURL}" alt="QR Code" />
                <p>${currency.qrCodeUrl ? t('bar.scanForMoreInfo') : t('bar.scanForContactInfo')}</p>
              </div>
            ` : ''}

            <div class="footer">
              <p>${t('bar.thankYouForOrder')}</p>
              <p>${t('bar.enjoyYourDrinks')}</p>
              <div class="barcode">O${newOrder.id.toString().padStart(3, "0")}</div>
            </div>
          </body>
        </html>
      `

      // Create a new window for printing with minimal visibility
      const printWindow = window.open('', '_blank', 'width=300,height=400,scrollbars=yes,resizable=yes')
      if (printWindow) {
        printWindow.document.documentElement.innerHTML = receiptHtml

        // Don't focus the window to keep it less intrusive
        // printWindow.focus()

        // Wait for images (including QR code) to load before printing
        let hasPrinted = false

        if (qrCodeDataURL) {
          // Use window.onload to ensure all content including images are loaded
          printWindow.onload = () => {
            if (!hasPrinted) {
              hasPrinted = true
              setTimeout(() => {
                printWindow.print()
                setTimeout(() => {
                  printWindow.close()
                }, 100) // Small delay before closing
              }, 100) // Small delay after onload
            }
          }

          // Fallback timeout in case onload doesn't fire
          setTimeout(() => {
            if (!hasPrinted && !printWindow.closed) {
              hasPrinted = true
              printWindow.print()
              setTimeout(() => {
                printWindow.close()
              }, 100)
            }
          }, 1000)
        } else {
          setTimeout(() => {
            printWindow.print()
            setTimeout(() => {
              printWindow.close()
            }, 100)
          }, 50)
        }
      }

      // Clear the order from both state and storage
      setTableOrders((prev) => {
        const updated = { ...prev }
        delete updated[selectedTable]
        return updated
      })

      // Clear the temporary draft order from Local Storage
      try {
        const localStorage = getStorageManager('local')
        await localStorage.delete(`bar_draft_order_${selectedTable}`)
        console.log(`✅ Cleared draft order for table ${selectedTable} from Local Storage`)
      } catch (error) {
        console.error('❌ Failed to clear draft order from Local Storage:', error)
      }

      // Note: Table status updates removed since we're using hardcoded tables
      // Tables are managed via Settings -> Bar Tables for hardcoded configuration

      setSelectedTable(null)

      // Auto-scroll back to table selection after successful order
      setTimeout(() => {
        if (tableSelectionRef.current) {
          tableSelectionRef.current.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          })
        }
      }, 200) // Small delay to ensure state updates are complete

    } catch (error) {
      console.error('Error creating order:', error)
    }
  }

  const _completeOrder = async (orderId: string) => {
    try {
      const response = await fetch('/api/orders', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: orderId,
          status: 'completed',
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to complete order')
      }

      const updatedOrder = await response.json()
      setRecentOrders((prev) => prev.map((order) => (order.id === orderId ? updatedOrder : order)))
    } catch (error) {
      console.error('Failed to complete order:', error)
    }
  }

  // Clear order for specific table
  const clearTableOrder = async (tableNumber: number) => {
    setTableOrders((prev) => {
      const updated = { ...prev }
      delete updated[tableNumber]
      return updated
    })

    // Clear from Local Storage
    try {
      const localStorage = getStorageManager('local')
      await localStorage.delete(`bar_draft_order_${tableNumber}`)
      console.log(`✅ Cleared draft order for table ${tableNumber} from Local Storage`)
    } catch (error) {
      console.error(`❌ Failed to clear draft order for table ${tableNumber} from Local Storage:`, error)
    }

    // If clearing the currently selected table, also clear selection
    if (selectedTable === tableNumber) {
      setSelectedTable(null)
    }
  }

  // Clear all hold orders
  const clearAllHolds = async () => {
    setTableOrders({})
    setSelectedTable(null)

    // Clear all temporary draft orders from Local Storage
    try {
      console.log('🗑️ Clearing all draft orders from Local Storage...')
      const localStorage = getStorageManager('local')

      // Clear draft orders for all available tables (dynamic based on database)
      for (const table of availableTables) {
        try {
          await localStorage.delete(`bar_draft_order_${table.number}`)
        } catch (error) {
          // Ignore errors for non-existent keys
        }
      }

      console.log('✅ Cleared all draft orders from Local Storage')
    } catch (error) {
      console.error('❌ Failed to clear draft orders from Local Storage:', error)
    }
  }

  // View order details
  const viewOrderDetails = (order: Order) => {
    setPreviewOrder(order)
    setShowOrderPreview(true)
  }

  // Fetch business info and currency settings when preview dialog opens
  useEffect(() => {
    const fetchPreviewSettings = async () => {
      if (showOrderPreview) {
        try {
          const [businessResponse, currencySettings] = await Promise.all([
            fetch('/api/business-info'),
            fetchCurrencySettings()
          ])

          if (businessResponse.ok) {
            const business = await businessResponse.json()
            setBusinessInfo(business)
          }

          // fetchCurrencySettings already returns properly formatted data
          setCurrencySettings(currencySettings)
        } catch (error) {
          console.error('Failed to fetch preview settings:', error)
        }
      }
    }

    fetchPreviewSettings()
  }, [showOrderPreview])

  // Fetch today's orders for daily report
  const fetchTodayOrders = async () => {
    try {
      const response = await fetch('/api/orders')
      if (response.ok) {
        const orders = await response.json()
        const today = new Date()
        const todayOrdersFiltered = orders.filter((order: any) => {
          const orderDate = new Date(order.created_at)
          return orderDate.toDateString() === today.toDateString()
        })
        setTodayOrders(todayOrdersFiltered)
      }
    } catch (error) {
      console.error('Failed to fetch today orders:', error)
    }
  }

  // Fetch today's games for daily report
  const fetchTodayGames = async () => {
    try {
      const today = new Date()
      const todayGamesFiltered = allGames.filter((game) =>
        game.status === "completed" &&
        game.endTime &&
        new Date(game.endTime).toDateString() === today.toDateString()
      )
      setTodayGames(todayGamesFiltered)
    } catch (error) {
      console.error('Failed to fetch today games:', error)
    }
  }

  // Fetch current month's orders for monthly report
  const fetchMonthOrders = async () => {
    try {
      const response = await fetch('/api/orders')
      if (response.ok) {
        const orders = await response.json()
        const currentDate = new Date()
        const currentMonth = currentDate.getMonth()
        const currentYear = currentDate.getFullYear()

        const monthOrdersFiltered = orders.filter((order: any) => {
          const orderDate = new Date(order.created_at)
          return orderDate.getMonth() === currentMonth && orderDate.getFullYear() === currentYear
        })
        setMonthOrders(monthOrdersFiltered)
      }
    } catch (error) {
      console.error('Failed to fetch month orders:', error)
    }
  }

  // Fetch current month's games for monthly report
  const fetchMonthGames = async () => {
    try {
      const currentDate = new Date()
      const currentMonth = currentDate.getMonth()
      const currentYear = currentDate.getFullYear()

      const monthGamesFiltered = allGames.filter((game) => {
        if (game.status !== "completed" || !game.endTime) return false
        const gameDate = new Date(game.endTime)
        return gameDate.getMonth() === currentMonth && gameDate.getFullYear() === currentYear
      })
      setMonthGames(monthGamesFiltered)
    } catch (error) {
      console.error('Failed to fetch month games:', error)
    }
  }

  // Check if table has pending orders
  const getTableOrderStatus = (tableNumber: number) => {
    const pendingOrders = recentOrders.filter(
      (order) => order.table_number === tableNumber && order.status === "pending",
    )
    const hasHoldOrder = !!tableOrders[tableNumber] && tableOrders[tableNumber].items.length > 0

    const holdTotal = hasHoldOrder ? Number(tableOrders[tableNumber].total) : 0
    console.log(`Table ${tableNumber} hold total:`, holdTotal, 'Raw total:', hasHoldOrder ? tableOrders[tableNumber].total : 'N/A')

    return {
      hasPendingOrders: pendingOrders.length > 0,
      pendingCount: pendingOrders.length,
      totalPending: pendingOrders.reduce((sum, order) => sum + Number(order.total), 0),
      hasHoldOrder,
      holdTotal,
      holdItemCount: hasHoldOrder ? tableOrders[tableNumber].items.reduce((sum, item) => sum + Number(item.quantity), 0) : 0,
    }
  }

  // Handle table selection with auto-scroll
  const handleTableSelection = (tableNumber: number) => {
    setSelectedTable(tableNumber)

    // Auto-scroll to Bar Menu section when a table is clicked
    setTimeout(() => {
      if (barMenuRef.current) {
        barMenuRef.current.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        })
      }
    }, 100) // Small delay to ensure the UI has updated
  }

  // Get table color based on status
  const getTableColor = (tableNumber: number, isSelected: boolean) => {
    const status = getTableOrderStatus(tableNumber)

    if (isSelected) {
      return "border-blue-500 bg-blue-100 shadow-lg text-blue-800"
    } else if (status.hasHoldOrder) {
      return "border-red-500 bg-red-500 hover:bg-red-600 text-white shadow-md"
    } else {
      return "border-green-200 bg-green-50 hover:bg-green-100 text-green-800 shadow-sm hover:shadow-md"
    }
  }

  // Product management
  const _addProduct = async (productData: Omit<DrinkItem, "id">) => {
    try {
      const response = await fetch('/api/menu-items', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: productData.name,
          price: productData.price,
          category: productData.category,
          available: productData.isActive
        }),
      })

      if (response.ok) {
        const newProduct = await response.json()
        setProducts([...products, {
          id: newProduct.id,
          name: newProduct.name,
          price: newProduct.price,
          category: newProduct.category,
          isActive: newProduct.available
        }])
        // Product added successfully
      }
    } catch (error) {
      console.error("Failed to add product:", error)
    }
  }

  const _updateProduct = async (id: number, updates: Partial<DrinkItem>) => {
    try {
      const response = await fetch('/api/menu-items', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id,
          name: updates.name,
          price: updates.price,
          category: updates.category,
          available: updates.isActive
        }),
      })

      if (response.ok) {
        const updatedProduct = await response.json()
        setProducts(products.map((product) =>
          product.id === id ? {
            id: updatedProduct.id,
            name: updatedProduct.name,
            price: updatedProduct.price,
            category: updatedProduct.category,
            isActive: updatedProduct.available
          } : product
        ))
        // Product updated successfully
      }
    } catch (error) {
      console.error("Failed to update product:", error)
    }
  }

  const _deleteProduct = async (id: number) => {
    try {
      const response = await fetch(`/api/menu-items?id=${id}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        setProducts(products.filter((product) => product.id !== id))
      }
    } catch (error) {
      console.error("Failed to delete product:", error)
    }
  }

  const getBarTableName = (number: number) => {
    return (
      availableTables.find(t => t.number === number)?.name ||
      allBarTables.find(t => t.number === number)?.name ||
      `${number}`
    )
  }

  const getGameTableName = (number: number) => {
    return (
      availableGameTables.find(t => t.number === number)?.name ||
      allGameTables.find(t => t.number === number)?.name ||
      `${number}`
    )
  }

  return (
    <div className="space-y-6">
      {/* Smart Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-gradient-to-r from-orange-500 to-red-600 rounded-lg">
            <Coffee className="h-6 w-6 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
              {t('bar.smartBar')}
              <Sparkles className="h-5 w-5 text-yellow-500" />
            </h1>
            <p className="text-sm text-gray-600">
              {smartInsights.busyTables} {t('bar.activeTables')} • {smartInsights.utilization}% {t('bar.utilization')}
            </p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          {/* Quick Stats */}
          <div className="hidden sm:flex items-center gap-4 text-sm">
            <div className="flex items-center gap-1">
              <Target className="h-4 w-4 text-green-600" />
              <span className="font-medium">{smartInsights.pendingValue.toFixed(0)}L</span>
              <span className="text-gray-500">{t('bar.pending').toLowerCase()}</span>
            </div>
            <div className="flex items-center gap-1">
              <BarChart3 className="h-4 w-4 text-blue-600" />
              <span className="font-medium">{smartInsights.avgOrderSize}</span>
              <span className="text-gray-500">{t('bar.avgItems')}</span>
            </div>
          </div>


        </div>
      </div>
      {/* Table Selection - Compact Design */}
      <Card ref={tableSelectionRef} className="mb-4">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-base">
            <Users className="h-4 w-4" />
            {t('bar.selectTable')}
            {Object.keys(tableOrders).length > 0 && (
              <div className="ml-auto flex items-center gap-2">
                <Badge variant="outline" className="bg-red-100 text-red-800 border-red-300 text-xs">
                  {Object.keys(tableOrders).length} {t('bar.onHold')}
                </Badge>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={clearAllHolds}
                  className="text-red-600 hover:text-red-700 border-red-300 hover:border-red-400 h-6 text-xs px-2"
                >
                  {t('bar.clearAll')}
                </Button>
              </div>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          {availableTables.length === 0 ? (
            <div className="text-center py-8">
              <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                <h3 className="text-lg font-semibold text-yellow-800 mb-2">{t('bar.noTablesAvailable')}</h3>
                <p className="text-yellow-700 text-sm mb-3">
                  {t('bar.noTablesAssigned')}
                </p>
                <div className="text-xs text-yellow-600 space-y-1">
                  <p>• {t('bar.checkTablesInSettings')}</p>
                  <p>• {t('bar.ensureTablesActive')}</p>
                  <p>• {t('bar.contactAdminForTables')}</p>
                </div>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-6 sm:grid-cols-8 md:grid-cols-10 lg:grid-cols-12 xl:grid-cols-16 gap-2">
              {availableTables.sort((a, b) => a.name.localeCompare(b.name, undefined, { numeric: true, sensitivity: 'base' })).map((table) => {
              const orderStatus = getTableOrderStatus(table.number)
              const isSelected = selectedTable === table.number

              return (
                <button
                  key={table.id}
                  onClick={() => handleTableSelection(table.number)}
                  className={`
                    relative w-12 h-12 rounded-md border-2 transition-all duration-200
                    ${getTableColor(table.number, isSelected)}
                    hover:shadow-md focus:outline-none focus:ring-2 focus:ring-blue-300
                    flex items-center justify-center font-semibold text-sm
                  `}
                >
                  {/* Table Name */}
                  <div className="leading-none text-xs font-medium">{table.name}</div>

                  {/* Hold Order Indicator (Red) */}
                  {orderStatus.hasHoldOrder && (
                    <div className="absolute -top-1 -right-1">
                      <div className="w-3 h-3 bg-red-500 text-white text-xs font-bold rounded-full flex items-center justify-center">
                        {orderStatus.holdItemCount}
                      </div>
                    </div>
                  )}

                  {/* Pending Orders Indicator (Orange) */}
                  {orderStatus.hasPendingOrders && !orderStatus.hasHoldOrder && (
                    <div className="absolute -top-1 -right-1">
                      <div className="w-3 h-3 bg-orange-500 text-white text-xs font-bold rounded-full flex items-center justify-center">
                        {orderStatus.pendingCount}
                      </div>
                    </div>
                  )}

                  {/* Selected Indicator */}
                  {isSelected && (
                    <div className="absolute -top-1 -left-1">
                      <div className="w-3 h-3 bg-blue-500 rounded-full flex items-center justify-center">
                        <div className="w-1.5 h-1.5 bg-white rounded-full"></div>
                      </div>
                    </div>
                  )}
                </button>
              )
            })}
            </div>
          )}

          {selectedTable && (
            <div className="mt-3 p-2 bg-blue-50 border border-blue-200 rounded-md">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-300 text-xs">
                    {availableTables.find(t => t.number === selectedTable)?.name || `Table ${selectedTable}`}
                  </Badge>
                  {getTableOrderStatus(selectedTable).hasPendingOrders && (
                    <Badge variant="outline" className="bg-orange-100 text-orange-800 border-orange-300 text-xs">
                      {getTableOrderStatus(selectedTable).pendingCount} {t('bar.pending')}
                    </Badge>
                  )}
                  {getTableOrderStatus(selectedTable).hasHoldOrder && (
                    <Badge variant="outline" className="bg-red-100 text-red-800 border-red-300 text-xs">
                      <Clock className="h-3 w-3 mr-1" />
                      {t('bar.onHold')}
                    </Badge>
                  )}
                </div>
                {getTableOrderStatus(selectedTable).hasHoldOrder && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => clearTableOrder(selectedTable)}
                    className="text-red-600 hover:text-red-700 h-6 text-xs px-2"
                  >
                    {t('bar.clear')}
                  </Button>
                )}
              </div>
            </div>
          )}

          {!selectedTable && (
            <div className="mt-3 p-2 bg-gray-50 border border-gray-200 rounded-md text-center">
              <p className="text-gray-600 text-sm">{t('bar.selectTableToOrder')}</p>
              {Object.keys(tableOrders).length > 0 && (
                <p className="text-xs text-red-600 mt-1">
                  {Object.keys(tableOrders).length} {t('bar.tablesOnHold')}
                </p>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
        {/* Smart Menu */}
        <div className="lg:col-span-2">
          <Card ref={barMenuRef} className="shadow-sm">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <ChefHat className="h-5 w-5 text-orange-600" />
                  {t('bar.smartMenu')}
                  {selectedTable && (
                    <Badge variant="outline" className="ml-2 text-xs bg-orange-50 text-orange-700 border-orange-200">
                      {availableTables.find(t => t.number === selectedTable)?.name || `Table ${selectedTable}`}
                    </Badge>
                  )}
                </CardTitle>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-xs">
                    {filteredProducts.length} {t('bar.items')}
                  </Badge>
                  <Button
                    onClick={() => {
                      setPendingReportType('daily')
                      setReportIncludes({games: true, orders: true})
                      setShowReportTypeDialog(true)
                    }}
                    variant="outline"
                    size="sm"
                    className="h-7 text-xs px-2"
                  >
                    📊 {t('bar.reports')}
                  </Button>
                  <Button
                    onClick={() => {
                      setPendingReportType('monthly')
                      setReportIncludes({games: true, orders: true})
                      setShowReportTypeDialog(true)
                    }}
                    variant="outline"
                    size="sm"
                    className="h-7 text-xs px-2"
                  >
                    📅 {t('bar.monthlyReports')}
                  </Button>
                </div>
              </div>

              {/* Smart Search & Filters */}
              {selectedTable && (
                <div className="space-y-3 mt-4">
                  {/* Search Bar */}
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder={t('bar.searchMenuItems')}
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10 h-9"
                    />
                  </div>

                  {/* Category Filters */}
                  <div className="flex items-center gap-2 overflow-x-auto pb-2">
                    <Button
                      variant={selectedCategory === "all" ? "default" : "outline"}
                      size="sm"
                      onClick={() => setSelectedCategory("all")}
                      className="text-xs whitespace-nowrap"
                    >
                      {t('common.all')} ({products.length})
                    </Button>
                    {categories.map((category) => {
                      const count = products.filter(p => p.category === category.key).length
                      if (count === 0) return null

                      return (
                        <Button
                          key={category.key}
                          variant={selectedCategory === category.key ? "default" : "outline"}
                          size="sm"
                          onClick={() => setSelectedCategory(category.key)}
                          className="text-xs whitespace-nowrap flex items-center gap-1"
                        >
                          <category.icon className="h-3 w-3" />
                          {category.label} ({count})
                        </Button>
                      )
                    })}
                  </div>

                  {/* Quick Filters */}
                  <div className="flex items-center gap-2">
                    <span className="text-xs text-gray-500">{t('bar.quick')}:</span>
                    <Button
                      variant={quickFilters.popular ? "default" : "outline"}
                      size="sm"
                      onClick={() => setQuickFilters(prev => ({ ...prev, popular: !prev.popular }))}
                      className="text-xs h-7 px-2"
                    >
                      <Star className="h-3 w-3 mr-1" />
                      {t('bar.popular')}
                    </Button>
                    <Button
                      variant={quickFilters.recent ? "default" : "outline"}
                      size="sm"
                      onClick={() => setQuickFilters(prev => ({ ...prev, recent: !prev.recent }))}
                      className="text-xs h-7 px-2"
                    >
                      <Clock className="h-3 w-3 mr-1" />
                      {t('bar.recent')}
                    </Button>
                  </div>
                </div>
              )}
            </CardHeader>
            <CardContent className="pt-0">
              {!selectedTable ? (
                <div className="text-center py-12">
                  <div className="p-4 bg-gradient-to-br from-orange-50 to-red-50 rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center">
                    <Users className="h-10 w-10 text-orange-500" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{t('bar.selectTableToStartOrdering')}</h3>
                  <p className="text-gray-500 text-sm">{t('bar.chooseTableToStartOrdering')}</p>
                  <div className="mt-4 flex items-center justify-center gap-2 text-xs text-gray-400">
                    <Target className="h-3 w-3" />
                    <span>{t('bar.chooseFromAvailableTables', { count: availableTables.length })}</span>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  {/* Smart Menu Grid */}
                  {filteredProducts.length > 0 ? (
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                      {filteredProducts.map((item) => {
                        const isPopular = orderStats.popularItems.includes(item.name)

                        return (
                          <div
                            key={item.id}
                            className="group relative p-4 border rounded-xl hover:shadow-md transition-all duration-200 hover:border-orange-200 bg-white"
                          >
                            {/* Popular Badge */}
                            {isPopular && (
                              <div className="absolute -top-2 -right-2">
                                <Badge className="bg-yellow-100 text-yellow-800 border-yellow-300 text-xs px-2 py-1">
                                  <Star className="h-3 w-3 mr-1" />
                                  {t('bar.popular')}
                                </Badge>
                              </div>
                            )}

                            <div className="flex items-start justify-between">
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center gap-2 mb-1">
                                  <h4 className="font-semibold text-sm text-gray-900 truncate">{item.name}</h4>
                                  {isPopular && <TrendingUp className="h-3 w-3 text-yellow-600" />}
                                </div>
                                <div className="flex items-center gap-2 mb-2">
                                  <span className="text-lg font-bold text-orange-600">{item.price}L</span>
                                  <Badge variant="outline" className="text-xs">
                                    {item.category}
                                  </Badge>
                                </div>
                                {item.description && (
                                  <p className="text-xs text-gray-500 line-clamp-2">{item.description}</p>
                                )}
                              </div>

                              <Button
                                onClick={() => addToOrder(item)}
                                size="sm"
                                className="ml-3 h-9 w-9 p-0 bg-gradient-to-r from-orange-500 to-red-600 hover:from-orange-600 hover:to-red-700 shadow-sm"
                              >
                                <Plus className="h-4 w-4" />
                              </Button>
                            </div>

                            {/* Quick Add Animation */}
                            <div className="absolute inset-0 bg-green-100 rounded-xl opacity-0 group-hover:opacity-10 transition-opacity duration-200 pointer-events-none" />
                          </div>
                        )
                      })}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <div className="p-3 bg-gray-100 rounded-full w-16 h-16 mx-auto mb-3 flex items-center justify-center">
                        <Search className="h-8 w-8 text-gray-400" />
                      </div>
                      <h3 className="font-medium text-gray-900 mb-1">{t('bar.noItemsFound')}</h3>
                      <p className="text-sm text-gray-500">
                        {searchQuery ? t('bar.noItemsMatch', { query: searchQuery }) : t('bar.noItemsInCategory')}
                      </p>
                      {searchQuery && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setSearchQuery('')}
                          className="mt-3"
                        >
                          {t('bar.clearSearch')}
                        </Button>
                      )}
                    </div>
                  )}

                  {products.length === 0 && (
                    <div className="text-center py-8">
                      <div className="p-3 bg-gray-100 rounded-full w-16 h-16 mx-auto mb-3 flex items-center justify-center">
                        <Coffee className="h-8 w-8 text-gray-400" />
                      </div>
                      <h3 className="font-medium text-gray-900 mb-1">{t('bar.noMenuItems')}</h3>
                      <p className="text-sm text-gray-500">{t('bar.addProductsInSettings')}</p>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Current Order & Recent Orders - Compact Design */}
        <div className="space-y-4">
          {/* Smart Current Order */}
          <Card className="shadow-sm">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <ShoppingCart className="h-5 w-5 text-orange-600" />
                  {t('bar.currentOrder')}
                </div>
                {selectedTable && getCurrentOrder().length > 0 && (
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-xs bg-orange-50 text-orange-700 border-orange-200">
                      {availableTables.find(t => t.number === selectedTable)?.name || `Table ${selectedTable}`}
                    </Badge>
                    <Badge className="text-xs bg-green-100 text-green-800">
                      {getCurrentOrder().length} {t('bar.items')}
                    </Badge>
                  </div>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              {!selectedTable ? (
                <div className="text-center py-6">
                  <div className="p-3 bg-gray-100 rounded-full w-12 h-12 mx-auto mb-3 flex items-center justify-center">
                    <ShoppingCart className="h-6 w-6 text-gray-400" />
                  </div>
                  <p className="text-sm text-gray-500">{t('bar.selectTableToStartOrdering')}</p>
                </div>
              ) : getCurrentOrder().length === 0 ? (
                <div className="text-center py-6">
                  <div className="p-3 bg-orange-50 rounded-full w-12 h-12 mx-auto mb-3 flex items-center justify-center">
                    <Plus className="h-6 w-6 text-orange-500" />
                  </div>
                  <p className="text-sm text-gray-500">{t('bar.addItemsFromMenu')}</p>
                  <p className="text-xs text-gray-400 mt-1">{availableTables.find(t => t.number === selectedTable)?.name || `Table ${selectedTable}`} ready for orders</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {getCurrentOrder().map((item) => (
                    <div key={item.id} className="flex items-center justify-between p-3 bg-gradient-to-r from-gray-50 to-orange-50 rounded-lg border border-orange-100">
                      <div className="flex-1 min-w-0">
                        <h4 className="font-semibold text-sm text-gray-900 truncate">{item.name}</h4>
                        <div className="flex items-center gap-2 mt-1">
                          <span className="text-xs text-gray-600">{item.price}L {t('bar.each')}</span>
                          <span className="text-xs text-orange-600 font-medium">
                            = {(item.price * item.quantity).toFixed(2)}L
                          </span>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          onClick={() => decreaseQuantity(item.id)}
                          size="sm"
                          variant="outline"
                          className="h-7 w-7 p-0 border-red-200 hover:bg-red-50 hover:border-red-300"
                        >
                          <Minus className="h-3 w-3 text-red-600" />
                        </Button>
                        <div className="w-8 text-center">
                          <span className="text-sm font-bold bg-white px-2 py-1 rounded border">
                            {item.quantity}
                          </span>
                        </div>
                        <Button
                          onClick={() => increaseQuantity(item.id)}
                          size="sm"
                          variant="outline"
                          className="h-7 w-7 p-0 border-green-200 hover:bg-green-50 hover:border-green-300"
                        >
                          <Plus className="h-3 w-3 text-green-600" />
                        </Button>
                      </div>
                    </div>
                  ))}

                  {/* Smart Order Summary */}
                  <div className="border-t border-orange-200 pt-4 mt-4">
                    <div className="bg-gradient-to-r from-orange-50 to-red-50 rounded-lg p-4 border border-orange-200">
                      <div className="flex justify-between items-center mb-3">
                        <span className="font-semibold text-gray-900">{t('bar.orderTotal')}:</span>
                        <span className="text-2xl font-bold text-orange-600">
                          {calculateTotal().toFixed(2)}L
                        </span>
                      </div>

                      <div className="flex items-center gap-2 text-xs text-gray-600 mb-3">
                        <Users className="h-3 w-3" />
                        <span>{availableTables.find(t => t.number === selectedTable)?.name || `Table ${selectedTable}`}</span>
                        <span>•</span>
                        <Clock className="h-3 w-3" />
                        <span>{getCurrentOrder().length} {t('bar.items')}</span>
                        <span>•</span>
                        <DollarSign className="h-3 w-3" />
                        <span>{t('bar.avgPerItem', { amount: (calculateTotal() / getCurrentOrder().length).toFixed(0) })}</span>
                      </div>

                      <Button
                        onClick={submitOrder}
                        className="w-full h-10 bg-gradient-to-r from-orange-500 to-red-600 hover:from-orange-600 hover:to-red-700 text-white font-medium shadow-sm"
                        disabled={!selectedTable}
                      >
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4" />
                          {t('bar.submitOrderAndPrint')}
                        </div>
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Today's Orders - Compact Design */}
          <Card>
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <CardTitle className="text-sm">{t('bar.todaysOrders')}</CardTitle>
                {(() => {
                  const today = new Date()
                  const allTodayOrders = recentOrders.filter((order) => {
                    const orderDate = new Date(order.created_at)
                    return orderDate.toDateString() === today.toDateString()
                  })
                  const todayOrders = filterBarData(allTodayOrders)
                    .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
                  const totalSum = todayOrders.reduce((sum, order) => sum + Number(order.total), 0)

                  return (
                    <div className="flex items-center gap-2 text-xs text-gray-600">
                      <span className="font-medium">{todayOrders.length}</span>
                      <span>•</span>
                      <span className="font-medium">{totalSum.toFixed(0)} L</span>
                    </div>
                  )
                })()}
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-1">
                {(() => {
                  const today = new Date()
                  const allTodayOrders = recentOrders.filter((order) => {
                    const orderDate = new Date(order.created_at)
                    return orderDate.toDateString() === today.toDateString()
                  })
                  const todayOrders = filterBarData(allTodayOrders)
                    .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())

                  if (todayOrders.length === 0) {
                    return <p className="text-gray-500 text-center py-3 text-xs">{t('bar.noOrdersToday')}</p>
                  }

                  return (
                    <div className="max-h-48 overflow-y-auto">
                      {todayOrders.map((order) => {
                        const creatorDisplay = order.created_by_name
                          ? order.created_by_name.split(' ')[0]
                          : (() => {
                              const names = order.items.map((item: OrderItem) => item.name).join(', ')
                              return names.length > 28 ? `${names.slice(0, 28)}…` : names
                            })()

                        return (
                          <div
                            key={order.id}
                            className="flex items-center justify-between p-2 border rounded-md hover:bg-gray-50 mb-1"
                          >
                            <div className="flex items-center gap-2 flex-1 min-w-0">
                              <div className="flex items-center gap-1">
                                <span className="font-medium text-xs">
                                  {getBarTableName(order.table_number)}
                                </span>
                                <span
                                  className={`w-1.5 h-1.5 rounded-full ${
                                    order.status === 'completed' ? 'bg-green-500' : 'bg-orange-500'
                                  }`}
                                ></span>
                              </div>
                              <div className="text-xs text-gray-600 truncate">
                                {order.items.length}{' '}
                                {order.items.length !== 1 ? t('bar.items') : t('bar.item')}
                              </div>
                              <div className="text-xs text-gray-500 truncate max-w-28">{creatorDisplay}</div>
                              <div className="font-medium text-xs ml-auto">
                                {Number(order.total).toFixed(0)} L
                              </div>
                            </div>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => viewOrderDetails(order)}
                              className="h-6 w-6 p-0 ml-2"
                            >
                              <Eye className="h-3 w-3" />
                            </Button>
                          </div>
                        )
                      })}
                    </div>
                  )
                })()}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Order Preview Dialog */}
      <Dialog open={showOrderPreview} onOpenChange={setShowOrderPreview}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <ShoppingCart className="h-5 w-5" />
              {t('bar.orderDetails')} - {t('bar.table')} {previewOrder ? (availableTables.find(t=>t.number===previewOrder.table_number)?.name || `T${previewOrder.table_number}`) : ''}
            </DialogTitle>
          </DialogHeader>
          {previewOrder && (
            <div className="space-y-4">
              {/* Receipt Preview - Identical to Transaction History format */}
              <div
                className="bg-white border rounded mx-auto"
                style={{
                  fontFamily: "'Courier New', monospace",
                  fontSize: '12px',
                  lineHeight: '1.4',
                  maxWidth: '300px',
                  padding: '10px'
                }}
              >
                {/* Header - Exact match to Transaction History format */}
                <div
                  className="text-center"
                  style={{
                    borderBottom: '2px solid #000',
                    paddingBottom: '10px',
                    marginBottom: '15px'
                  }}
                >
                  <div style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '5px' }}>
                    {businessInfo?.name || t('hardcoded.billiardClub')}
                  </div>
                  <div style={{ fontSize: '10px', marginBottom: '5px' }}>
                    {t('bar.barOrderReceipt')}
                  </div>
                  {businessInfo?.phone && (
                    <div style={{ fontSize: '10px', marginBottom: '5px' }}>
                      {t('bar.tel')}: {businessInfo.phone}
                    </div>
                  )}
                  {businessInfo?.address && (
                    <div style={{ fontSize: '10px', marginBottom: '5px' }}>
                      {t('bar.address')}: {businessInfo.address}
                    </div>
                  )}
                  {businessInfo?.vat_number && (
                    <div style={{ fontSize: '10px', marginBottom: '5px' }}>
                      VAT: {businessInfo.vat_number}
                    </div>
                  )}
                </div>

                {/* Receipt Info - Exact match to Transaction History format */}
                <div style={{ marginBottom: '15px' }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>
                    <span>{t('bar.receipt')} #:</span>
                    <span>O{previewOrder.id.toString().padStart(3, "0")}</span>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>
                    <span>{t('bar.date')}:</span>
                    <span>{(() => {
                      const date = new Date(previewOrder.created_at)
                      const day = date.getDate().toString().padStart(2, '0')
                      const month = (date.getMonth() + 1).toString().padStart(2, '0')
                      const year = date.getFullYear()
                      return `${day}/${month}/${year}`
                    })()} {new Date(previewOrder.created_at).toLocaleTimeString()}</span>
                  </div>
                  <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>
                    <span>{t('bar.table')}:</span>
                    <span>{availableTables.find(t => t.number === previewOrder.table_number)?.name || `Table ${previewOrder.table_number}`}</span>
                  </div>
                </div>

                {/* Items section - single line per item */}
                <div style={{ marginBottom: '15px' }}>
                  {previewOrder.items.map((item, index) => (
                    <div key={index} style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      marginBottom: '5px',
                      fontSize: '12px'
                    }}>
                      <span>{item.name} x {item.quantity} = {item.price} L</span>
                      <span>{item.quantity * item.price} L</span>
                    </div>
                  ))}
                </div>

                {/* Dashed separator line */}
                <div style={{
                  borderTop: '1px dashed #000',
                  margin: '10px 0',
                  width: '100%'
                }}></div>

                {/* Totals section matching Transaction History format exactly */}
                <div style={{ marginBottom: '15px' }}>
                  {(() => {
                    const total = previewOrder.items.reduce((sum, item) => sum + (item.quantity * item.price), 0)
                    const taxEnabled = currencySettings?.taxEnabled !== false // Default to true if not specified
                    const taxRate = currencySettings?.taxRate || 20
                    // Since prices include tax, calculate subtotal by removing tax from total
                    const subtotal = taxEnabled ? Math.round(total / (1 + taxRate / 100)) : total
                    const taxAmount = taxEnabled ? total - subtotal : 0

                    return (
                      <>
                        {taxEnabled && (
                          <>
                            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px', fontSize: '12px' }}>
                              <span>{t('bar.subtotalExclTax')}:</span>
                              <span>{subtotal} L</span>
                            </div>
                            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px', fontSize: '12px' }}>
                              <span>{t('bar.tax')} ({taxRate}.0%):</span>
                              <span>{taxAmount} L</span>
                            </div>
                          </>
                        )}
                        <div style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          marginBottom: '5px',
                          fontSize: '14px',
                          fontWeight: 'bold'
                        }}>
                          <span>{t('bar.total')}:</span>
                          <span>{total} L</span>
                        </div>
                      </>
                    )
                  })()}
                </div>

                {/* QR Code Section - Exact match to Transaction History format */}
                {currencySettings?.qrCodeEnabled && (
                  <div style={{ textAlign: 'center', marginBottom: '15px' }}>
                    <img
                      src={`https://api.qrserver.com/v1/create-qr-code/?size=100x100&data=${encodeURIComponent(
                        currencySettings?.qrCodeUrl ||
                        `${businessInfo?.name || t('hardcoded.barBilardo')}\n${t('bar.tel')}: ${businessInfo?.phone || ''}\n${t('bar.address')}: ${businessInfo?.address || ''}`
                      )}`}
                      alt="QR Code"
                      style={{ width: '100px', height: '100px', margin: '0 auto', display: 'block' }}
                    />
                  </div>
                )}

                {/* Footer removed as per client request */}
              </div>

              {/* Print Button */}
              <div className="flex justify-center pt-2">
                <Button
                  onClick={async () => {
                    // Generate QR code if enabled (using the same method as main print function)
                    let qrCodeDataURL = ''
                    if (currencySettings?.qrCodeEnabled) {
                      try {
                        qrCodeDataURL = await generateQRCode(currencySettings.qrCodeUrl || '', businessInfo)
                        console.log('QR code generated for Today\'s Orders print:', qrCodeDataURL ? 'Success' : 'Failed')
                      } catch (error) {
                        console.error('Failed to generate QR code for Today\'s Orders:', error)
                      }
                    }

                    // Create and print receipt
                    const receiptHtml = `
                      <html>
                        <head>
                          <title>Order Receipt</title>
                          <style>
                            @page {
                              size: 80mm auto;
                              margin: 0;
                            }
                            body {
                              font-family: 'Courier New', monospace;
                              width: 80mm;
                              margin: 0;
                              padding: 3mm;
                              font-size: 11px;
                              line-height: 1.3;
                              color: #000;
                            }
                            .header {
                              text-align: center;
                              margin-bottom: 8px;
                              border-bottom: 1px solid #000;
                              padding-bottom: 5px;
                            }
                            .header h2 {
                              margin: 0;
                              font-size: 16px;
                              font-weight: bold;
                              text-transform: uppercase;
                            }
                            .header p {
                              margin: 1px 0;
                              font-size: 10px;
                            }
                            .info {
                              margin: 8px 0;
                              font-size: 11px;
                            }
                            .info p {
                              margin: 2px 0;
                              display: flex;
                              justify-content: space-between;
                            }
                            .items {
                              margin: 8px 0;
                            }
                            .item {
                              margin: 2px 0;
                              font-size: 11px;
                              display: flex;
                              justify-content: space-between;
                              align-items: flex-start;
                            }
                            .item-name {
                              flex: 1;
                              margin-right: 5px;
                            }
                            .item-price {
                              white-space: nowrap;
                              font-weight: bold;
                            }
                            .total {
                              margin-top: 8px;
                              border-top: 2px solid #000;
                              padding-top: 5px;
                              font-weight: bold;
                              display: flex;
                              justify-content: space-between;
                              font-size: 14px;
                            }
                            .footer {
                              text-align: center;
                              margin-top: 10px;
                              font-size: 9px;
                              border-top: 1px dashed #000;
                              padding-top: 5px;
                            }
                            .barcode {
                              text-align: center;
                              margin-top: 8px;
                              font-family: 'Libre Barcode 39', cursive;
                              font-size: 20px;
                              letter-spacing: 2px;
                            }
                            .qr-code {
                              text-align: center;
                              margin: 10px 0;
                            }
                            .qr-code img {
                              width: 100px;
                              height: 100px;
                              margin: 0 auto;
                              display: block;
                            }
                          </style>
                        </head>
                        <body>
                          <div class="header">
                            <h2>${businessInfo?.name || t('hardcoded.billiardClub')}</h2>
                            <p>Bar Order Receipt</p>
                            ${businessInfo?.phone ? `<p>${t('bar.tel')}: ${businessInfo.phone}</p>` : ''}
                            ${businessInfo?.address ? `<p>${t('bar.address')}: ${businessInfo.address}</p>` : ''}
                            ${businessInfo?.vat_number ? `<p>VAT: ${businessInfo.vat_number}</p>` : ''}
                          </div>

                          <div class="info">
                            <p><span>Receipt #:</span><span>O${previewOrder.id.toString().padStart(3, "0")}</span></p>
                            <p><span>Date:</span><span>${(() => {
                              const date = new Date(previewOrder.created_at)
                              const day = date.getDate().toString().padStart(2, '0')
                              const month = (date.getMonth() + 1).toString().padStart(2, '0')
                              const year = date.getFullYear()
                              return `${day}/${month}/${year}`
                            })()} ${new Date(previewOrder.created_at).toLocaleTimeString()}</span></p>
                            <p><span>Table:</span><span>${availableTables.find(t => t.number === previewOrder.table_number)?.name || `Table ${previewOrder.table_number}`}</span></p>
                          </div>

                          <div class="items">
                            ${previewOrder.items.map(item => `
                              <div class="item">
                                <span class="item-name">${item.quantity}x ${item.name}</span>
                                <span class="item-price">${(item.quantity * item.price).toFixed(0)} L</span>
                              </div>
                            `).join('')}
                          </div>

                          ${(() => {
                            const total = Number(previewOrder.total)
                            const taxEnabled = currencySettings?.taxEnabled !== false
                            const taxRate = currencySettings?.taxRate || 20
                            const subtotal = taxEnabled ? Math.round(total / (1 + taxRate / 100)) : total
                            const taxAmount = taxEnabled ? total - subtotal : 0

                            return taxEnabled ? `
                              <div style="margin: 8px 0; border-top: 1px dashed #000; padding-top: 5px;">
                                <div style="display: flex; justify-content: space-between; margin: 2px 0; font-size: 11px;">
                                  <span>Subtotal (excl. tax):</span>
                                  <span>${subtotal} L</span>
                                </div>
                                <div style="display: flex; justify-content: space-between; margin: 2px 0; font-size: 11px;">
                                  <span>Tax (${taxRate}.0%):</span>
                                  <span>${taxAmount} L</span>
                                </div>
                              </div>
                            ` : ''
                          })()}

                          <div class="total">
                            <span>TOTAL:</span>
                            <span>${Number(previewOrder.total).toFixed(0)} L</span>
                          </div>

                          ${qrCodeDataURL ? `
                            <div class="qr-code">
                              <img src="${qrCodeDataURL}" alt="QR Code" style="width: 80px; height: 80px; display: block; margin: 0 auto; -webkit-print-color-adjust: exact; print-color-adjust: exact;" />
                              <p style="font-size: 10px; margin-top: 5px; text-align: center;">
                                ${currencySettings?.qrCodeUrl ? t('hardcoded.scanForMoreInfo') : t('hardcoded.scanForContactInfo')}
                              </p>
                            </div>
                          ` : ''}

                          <div class="footer">
                            <p>Thank you for your order!</p>
                            <p>Enjoy your drinks!</p>
                            <div class="barcode">O${previewOrder.id.toString().padStart(3, "0")}</div>
                          </div>
                        </body>
                      </html>
                    `
                    const printWindow = window.open('', '_blank', 'width=300,height=400')
                    if (printWindow) {
                      printWindow.document.documentElement.innerHTML = receiptHtml

                      // Wait for images (including QR code) to load before printing
                      let hasPrinted = false

                      if (qrCodeDataURL) {
                        // Use window.onload to ensure all content including images are loaded
                        printWindow.onload = () => {
                          if (!hasPrinted) {
                            hasPrinted = true
                            setTimeout(() => {
                              printWindow.print()
                              setTimeout(() => {
                                printWindow.close()
                              }, 100) // Small delay before closing
                            }, 100) // Small delay after onload
                          }
                        }

                        // Fallback timeout in case onload doesn't fire
                        setTimeout(() => {
                          if (!hasPrinted && !printWindow.closed) {
                            hasPrinted = true
                            printWindow.print()
                            setTimeout(() => {
                              printWindow.close()
                            }, 100)
                          }
                        }, 1000)
                      } else {
                        setTimeout(() => {
                          printWindow.print()
                          setTimeout(() => {
                            printWindow.close()
                          }, 100)
                        }, 50)
                      }
                    }
                  }}
                  className="flex items-center gap-2"
                >
                  <Printer className="h-4 w-4" />
                  {t('bar.printReceipt')}
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Report Type Selection Dialog */}
      <Dialog open={showReportTypeDialog} onOpenChange={setShowReportTypeDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>
              {pendingReportType === 'daily' ? `📊 ${t('bar.dailyReport')}` : `📅 ${t('bar.monthlyReport')}`}
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              {t('bar.chooseWhatToInclude', { reportType: pendingReportType })}
            </p>

            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <input
                  type="radio"
                  id="games-only"
                  name="reportType"
                  value="games"
                  checked={reportIncludes.games && !reportIncludes.orders}
                  onChange={() => setReportIncludes({games: true, orders: false})}
                  className="w-4 h-4 text-blue-600"
                />
                <label htmlFor="games-only" className="text-sm font-medium text-gray-700">
                  🎱 {t('bar.gamesReportOnly')}
                </label>
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="radio"
                  id="orders-only"
                  name="reportType"
                  value="orders"
                  checked={!reportIncludes.games && reportIncludes.orders}
                  onChange={() => setReportIncludes({games: false, orders: true})}
                  className="w-4 h-4 text-blue-600"
                />
                <label htmlFor="orders-only" className="text-sm font-medium text-gray-700">
                  🍺 {t('bar.barReportOnly')}
                </label>
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="radio"
                  id="both"
                  name="reportType"
                  value="both"
                  checked={reportIncludes.games && reportIncludes.orders}
                  onChange={() => setReportIncludes({games: true, orders: true})}
                  className="w-4 h-4 text-blue-600"
                />
                <label htmlFor="both" className="text-sm font-medium text-gray-700">
                  📊 {t('bar.bothGamesAndBar')}
                </label>
              </div>
            </div>

            <div className="flex justify-end gap-2 pt-4">
              <Button
                variant="outline"
                onClick={() => setShowReportTypeDialog(false)}
                size="sm"
              >
                {t('bar.cancel')}
              </Button>
              <Button
                onClick={() => {
                  if (pendingReportType === 'daily') {
                    fetchTodayOrders()
                    fetchTodayGames()
                    setShowDailyReport(true)
                  } else if (pendingReportType === 'monthly') {
                    fetchMonthOrders()
                    fetchMonthGames()
                    setShowMonthlyReport(true)
                  }
                  setShowReportTypeDialog(false)
                }}
                size="sm"
              >
                {t('bar.generateReport')}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Daily Report Dialog */}
      <Dialog open={showDailyReport} onOpenChange={setShowDailyReport}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              📊 {t('bar.dailyBarReport')} - {(() => {
                const today = new Date()
                const day = today.getDate().toString().padStart(2, '0')
                const month = (today.getMonth() + 1).toString().padStart(2, '0')
                const year = today.getFullYear()
                return `${day}/${month}/${year}`
              })()}
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-6">
            {(() => {
              const totalGamesCost = todayGames.reduce((sum, game) => {
                const cost = typeof game.cost === 'string' ? parseFloat(game.cost) : (game.cost || 0)
                return sum + cost
              }, 0)

              const totalOrdersCost = todayOrders.reduce((sum, order) => {
                const orderTotal = typeof order.total === 'string' ? parseFloat(order.total) : (order.total || 0)
                return sum + orderTotal
              }, 0)
              const grandTotal = (reportIncludes.games ? totalGamesCost : 0) + (reportIncludes.orders ? totalOrdersCost : 0)

              const currency = currencySettings || {
                currency: t('bar.albanianLek'),
                symbol: 'L',
                showDecimals: false,
                taxIncluded: false,
                taxEnabled: true,
                taxRate: 20,
                qrCodeEnabled: false,
                qrCodeUrl: '',
              }

              return (
                <>
                  {/* Daily Report Preview */}
                  <div className="bg-gray-50 p-6 rounded-lg font-mono text-sm">
                    <div className="text-center border-b border-gray-300 pb-4 mb-4">
                      <div className="font-bold text-lg">{businessInfo?.name || t('hardcoded.billiardClub')}</div>
                      <div className="text-sm">{t('bar.dailyReport')}</div>
                      <div className="text-xs">{(() => {
                        const today = new Date()
                        const day = today.getDate().toString().padStart(2, '0')
                        const month = (today.getMonth() + 1).toString().padStart(2, '0')
                        const year = today.getFullYear()
                        return `${day}/${month}/${year}`
                      })()}</div>
                      {businessInfo?.phone && <div className="text-xs">{t('bar.tel')}: {businessInfo.phone}</div>}
                      {businessInfo?.address && <div className="text-xs">{t('bar.address')}: {businessInfo.address}</div>}
                    </div>

                    {/* Games Section */}
                    {reportIncludes.games && (
                      <div className="mb-4">
                        <div className="font-bold border-b border-gray-300 pb-1 mb-2">{t('bar.billiardGames')} ({todayGames.length})</div>
                        {todayGames.length === 0 ? (
                          <div className="text-gray-500 text-center py-2">{t('bar.noGamesCompletedToday')}</div>
                        ) : (
                          <div className="space-y-1">
                            {todayGames.map((game) => (
                              <div key={game.id} className="flex justify-between">
                                <span>{getGameTableName(game.tableNumber)} - {Math.floor(game.duration / 60)}h {game.duration % 60}m</span>
                                <span>{formatCurrency(typeof game.cost === 'string' ? parseFloat(game.cost) : (game.cost || 0), currency)}</span>
                              </div>
                            ))}
                            <div className="flex justify-between font-bold border-t border-gray-300 pt-1 mt-2">
                              <span>{t('bar.gamesSubtotal')}</span>
                              <span>{formatCurrency(totalGamesCost, currency)}</span>
                            </div>
                          </div>
                        )}
                      </div>
                    )}

                    {/* Orders Section */}
                    {reportIncludes.orders && (
                      <div className="mb-4">
                        <div className="font-bold border-b border-gray-300 pb-1 mb-2">{t('bar.barOrders')} ({todayOrders.length})</div>
                        {todayOrders.length === 0 ? (
                          <div className="text-gray-500 text-center py-2">{t('bar.noOrdersCompletedToday')}</div>
                        ) : (
                          <div className="space-y-2 max-h-40 overflow-y-auto">
                            {todayOrders.sort((a, b) => {
                              const tableA = getBarTableName(a.table_number)
                              const tableB = getBarTableName(b.table_number)
                              return tableA.localeCompare(tableB, undefined, { numeric: true, sensitivity: 'base' })
                            }).map((order) => (
                              <div key={order.id} className="border-b border-gray-200 pb-2 last:border-b-0">
                                <div className="flex justify-between font-medium">
                                  <span className="font-bold">{getBarTableName(order.table_number)}</span>
                                  <span>{formatCurrency(typeof order.total === 'string' ? parseFloat(order.total) : (order.total || 0), currency)}</span>
                                </div>
                                <div className="text-xs text-gray-600 ml-2">
                                  {order.items?.map((item: any, index: number) => (
                                    <div key={index} className="flex justify-between">
                                      <span>{item.quantity}x {item.name}</span>
                                      <span>{formatCurrency(item.quantity * item.price, currency)}</span>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            ))}
                            <div className="flex justify-between font-bold border-t border-gray-300 pt-1 mt-2">
                              <span>{t('bar.ordersSubtotal')}</span>
                              <span>{formatCurrency(totalOrdersCost, currency)}</span>
                            </div>
                          </div>
                        )}
                      </div>
                    )}

                    <div className="flex justify-between font-bold text-lg border-t-2 border-gray-400 pt-2">
                      <span>{t('bar.dailyTotal')}</span>
                      <span>{formatCurrency(grandTotal, currency)}</span>
                    </div>
                  </div>

                  {/* Print Button */}
                  <div className="flex justify-center">
                    <Button
                      onClick={() => {
                        // Generate and print daily report
                        // Pre-evaluate translations to avoid template literal issues
                        const translations = {
                          dailyReport: t('bar.dailyReport'),
                          billiardGames: t('bar.billiardGames'),
                          barOrders: t('bar.barOrders'),
                          noGamesCompletedToday: t('bar.noGamesCompletedToday'),
                          noOrdersCompletedToday: t('bar.noOrdersCompletedToday'),
                          gamesSubtotal: t('bar.gamesSubtotal'),
                          ordersSubtotal: t('bar.ordersSubtotal'),
                          dailyTotal: t('bar.dailyTotal'),
                          reportGenerated: t('bar.reportGenerated'),
                          address: t('bar.address'),
                          vat: t('bar.vat')
                        }

                        const reportHtml = `
                          <html>
                            <head>
                              <title>${translations.dailyReport}</title>
                              <style>
                                @page { size: 80mm auto; margin: 0; }
                                body {
                                  font-family: 'Courier New', monospace;
                                  width: 80mm;
                                  margin: 0;
                                  padding: 5mm;
                                  font-size: 12px;
                                  line-height: 1.2;
                                }
                                .header { text-align: center; margin-bottom: 10px; }
                                .header h2 { margin: 0; font-size: 14px; }
                                .header p { margin: 2px 0; }
                                .section { margin: 10px 0; }
                                .section-title { font-weight: bold; border-bottom: 1px solid #000; padding-bottom: 2px; margin-bottom: 5px; }
                                .item { margin: 3px 0; display: flex; justify-content: space-between; }
                                .subtotal { margin-top: 5px; border-top: 1px dashed #000; padding-top: 3px; font-weight: bold; }
                                .total { margin-top: 10px; border-top: 2px solid #000; padding-top: 5px; font-weight: bold; font-size: 14px; }
                                .footer { text-align: center; margin-top: 10px; font-size: 10px; }
                              </style>
                            </head>
                            <body>
                              <div class="header">
                                <h2>${businessInfo?.name || t('hardcoded.billiardClub')}</h2>
                                <p>${translations.dailyReport}</p>
                                <p>${(() => {
                                  const today = new Date()
                                  const day = today.getDate().toString().padStart(2, '0')
                                  const month = (today.getMonth() + 1).toString().padStart(2, '0')
                                  const year = today.getFullYear()
                                  return `${day}/${month}/${year}`
                                })()}</p>
                                ${businessInfo?.phone ? `<p>${t('bar.tel')}: ${businessInfo.phone}</p>` : ''}
                                ${businessInfo?.address ? `<p>${translations.address}: ${businessInfo.address}</p>` : ''}
                              </div>

                              ${reportIncludes.games ? `
                                <div class="section">
                                  <div class="section-title">${translations.billiardGames} (${todayGames.length})</div>
                                  ${todayGames.length === 0 ? `<p style="text-align: center; color: #666;">${translations.noGamesCompletedToday}</p>` :
                                    todayGames.map(game => `
                                      <div class="item">
                                        <span>${getGameTableName(game.tableNumber)} - ${Math.floor(game.duration / 60)}h ${game.duration % 60}m</span>
                                        <span>${formatCurrency(typeof game.cost === 'string' ? parseFloat(game.cost) : (game.cost || 0), currency)}</span>
                                      </div>
                                    `).join('') + `
                                      <div class="item subtotal">
                                        <span>${translations.gamesSubtotal}</span>
                                        <span>${formatCurrency(totalGamesCost, currency)}</span>
                                      </div>
                                    `
                                  }
                                </div>
                              ` : ''}

                              ${reportIncludes.orders ? `
                                <div class="section">
                                  <div class="section-title">${translations.barOrders} (${todayOrders.length})</div>
                                  ${todayOrders.length === 0 ? `<p style="text-align: center; color: #666;">${translations.noOrdersCompletedToday}</p>` :
                                    todayOrders.sort((a, b) => {
                                      const tableA = getBarTableName(a.table_number)
                                      const tableB = getBarTableName(b.table_number)
                                      return tableA.localeCompare(tableB, undefined, { numeric: true, sensitivity: 'base' })
                                    }).map(order => `
                                      <div class="item">
                                        <span style="font-weight: bold;">${getBarTableName(order.table_number)}</span>
                                        <span>${formatCurrency(typeof order.total === 'string' ? parseFloat(order.total) : (order.total || 0), currency)}</span>
                                      </div>
                                      ${order.items?.map((item: OrderItem) => `
                                        <div class="item" style="margin-left: 10px; font-size: 10px;">
                                          <span>${item.quantity}x ${item.name}</span>
                                          <span>${formatCurrency(item.quantity * item.price, currency)}</span>
                                        </div>
                                      `).join('')}
                                    `).join('') + `
                                      <div class="item subtotal">
                                        <span>${translations.ordersSubtotal}</span>
                                        <span>${formatCurrency(totalOrdersCost, currency)}</span>
                                      </div>
                                    `
                                  }
                                </div>
                              ` : ''}

                              <div class="item total">
                                <span>${translations.dailyTotal}</span>
                                <span>${formatCurrency(grandTotal, currency)}</span>
                              </div>

                              <div class="footer">
                                <p>${translations.reportGenerated} ${new Date().toLocaleString()}</p>
                                ${businessInfo?.vat_number ? `<p>${translations.vat}: ${businessInfo.vat_number}</p>` : ''}
                              </div>
                            </body>
                          </html>
                        `
                        const printWindow = window.open('', '_blank', 'width=300,height=400')
                        if (printWindow) {
                          printWindow.document.documentElement.innerHTML = reportHtml
                          printWindow.print()
                          printWindow.close()
                        }
                      }}
                      className="flex items-center gap-2"
                    >
                      <Printer className="h-4 w-4" />
                      {t('bar.printDailyReport')}
                    </Button>
                  </div>
                </>
              )
            })()}
          </div>
        </DialogContent>
      </Dialog>

      {/* Monthly Report Dialog */}
      <Dialog open={showMonthlyReport} onOpenChange={setShowMonthlyReport}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              📅 {t('bar.monthlyBarReport')} - {(() => {
                const today = new Date()
                const day = today.getDate().toString().padStart(2, '0')
                const month = (today.getMonth() + 1).toString().padStart(2, '0')
                const year = today.getFullYear()
                return `${day}/${month}/${year}`
              })()}
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-6">
            {(() => {
              const totalGamesCost = monthGames.reduce((sum, game) => {
                const cost = typeof game.cost === 'string' ? parseFloat(game.cost) : (game.cost || 0)
                return sum + cost
              }, 0)

              const totalOrdersCost = monthOrders.reduce((sum, order) => {
                const orderTotal = typeof order.total === 'string' ? parseFloat(order.total) : (order.total || 0)
                return sum + orderTotal
              }, 0)
              const grandTotal = (reportIncludes.games ? totalGamesCost : 0) + (reportIncludes.orders ? totalOrdersCost : 0)

              const currency = currencySettings || {
                currency: t('hardcoded.albanianLek'),
                symbol: 'L',
                showDecimals: false,
                taxIncluded: false,
                taxEnabled: true,
                taxRate: 20,
                qrCodeEnabled: false,
                qrCodeUrl: '',
              }

              return (
                <>
                  {/* Monthly Report Preview */}
                  <div className="bg-gray-50 p-6 rounded-lg font-mono text-sm">
                    <div className="text-center border-b border-gray-300 pb-4 mb-4">
                      <div className="font-bold text-lg">{businessInfo?.name || t('hardcoded.billiardClub')}</div>
                      <div className="text-sm">{t('bar.monthlyReport')}</div>
                      <div className="text-xs">{(() => {
                        const today = new Date()
                        const day = today.getDate().toString().padStart(2, '0')
                        const month = (today.getMonth() + 1).toString().padStart(2, '0')
                        const year = today.getFullYear()
                        return `${day}/${month}/${year}`
                      })()}</div>
                      {businessInfo?.phone && <div className="text-xs">{t('bar.tel')}: {businessInfo.phone}</div>}
                      {businessInfo?.address && <div className="text-xs">{t('bar.address')}: {businessInfo.address}</div>}
                    </div>

                    {/* Games Section */}
                    {reportIncludes.games && (
                      <div className="mb-4">
                        <div className="font-bold border-b border-gray-300 pb-1 mb-2">{t('bar.billiardGames')} ({monthGames.length})</div>
                        {monthGames.length === 0 ? (
                          <div className="text-gray-500 text-center py-2">{t('bar.noGamesCompletedThisMonth')}</div>
                        ) : (
                          <div className="space-y-1 max-h-40 overflow-y-auto">
                            {monthGames.map((game) => (
                              <div key={game.id} className="flex justify-between">
                                <span>{getGameTableName(game.tableNumber)} - {Math.floor(game.duration / 60)}h {game.duration % 60}m</span>
                                <span>{formatCurrency(typeof game.cost === 'string' ? parseFloat(game.cost) : (game.cost || 0), currency)}</span>
                              </div>
                            ))}
                            <div className="flex justify-between font-bold border-t border-gray-300 pt-1 mt-2">
                              <span>{t('bar.gamesSubtotal')}</span>
                              <span>{formatCurrency(totalGamesCost, currency)}</span>
                            </div>
                          </div>
                        )}
                      </div>
                    )}

                    {/* Orders Section */}
                    {reportIncludes.orders && (
                      <div className="mb-4">
                        <div className="font-bold border-b border-gray-300 pb-1 mb-2">{t('bar.barOrders')} ({monthOrders.length})</div>
                        {monthOrders.length === 0 ? (
                          <div className="text-gray-500 text-center py-2">{t('bar.noOrdersCompletedThisMonth')}</div>
                        ) : (
                          <div className="space-y-2 max-h-40 overflow-y-auto">
                            {monthOrders.sort((a, b) => {
                              const tableA = getBarTableName(a.table_number)
                              const tableB = getBarTableName(b.table_number)
                              return tableA.localeCompare(tableB, undefined, { numeric: true, sensitivity: 'base' })
                            }).map((order) => (
                              <div key={order.id} className="border-b border-gray-200 pb-2 last:border-b-0">
                                <div className="flex justify-between font-medium">
                                  <span className="font-bold">{getBarTableName(order.table_number)}</span>
                                  <span>{formatCurrency(typeof order.total === 'string' ? parseFloat(order.total) : (order.total || 0), currency)}</span>
                                </div>
                                <div className="text-xs text-gray-600 ml-2">
                                  {order.items?.map((item: any, index: number) => (
                                    <div key={index} className="flex justify-between">
                                      <span>{item.quantity}x {item.name}</span>
                                      <span>{formatCurrency(item.quantity * item.price, currency)}</span>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            ))}
                            <div className="flex justify-between font-bold border-t border-gray-300 pt-1 mt-2">
                              <span>{t('bar.ordersSubtotal')}</span>
                              <span>{formatCurrency(totalOrdersCost, currency)}</span>
                            </div>
                          </div>
                        )}
                      </div>
                    )}

                    <div className="flex justify-between font-bold text-lg border-t-2 border-gray-400 pt-2">
                      <span>{t('bar.monthlyTotal')}</span>
                      <span>{formatCurrency(grandTotal, currency)}</span>
                    </div>
                  </div>

                  {/* Print Button */}
                  <div className="flex justify-center">
                    <Button
                      onClick={() => {
                        // Generate and print monthly report
                        // Pre-evaluate translations to avoid template literal issues
                        const translations = {
                          monthlyReport: t('bar.monthlyReport'),
                          billiardGames: t('bar.billiardGames'),
                          barOrders: t('bar.barOrders'),
                          noGamesCompletedThisMonth: t('bar.noGamesCompletedThisMonth'),
                          noOrdersCompletedThisMonth: t('bar.noOrdersCompletedThisMonth'),
                          gamesSubtotal: t('bar.gamesSubtotal'),
                          ordersSubtotal: t('bar.ordersSubtotal'),
                          monthlyTotal: t('bar.monthlyTotal'),
                          reportGenerated: t('bar.reportGenerated'),
                          address: t('bar.address'),
                          vat: t('bar.vat')
                        }

                        const reportHtml = `
                          <html>
                            <head>
                              <title>${translations.monthlyReport}</title>
                              <style>
                                @page { size: 80mm auto; margin: 0; }
                                body {
                                  font-family: 'Courier New', monospace;
                                  width: 80mm;
                                  margin: 0;
                                  padding: 5mm;
                                  font-size: 12px;
                                  line-height: 1.2;
                                }
                                .header { text-align: center; margin-bottom: 10px; }
                                .header h2 { margin: 0; font-size: 14px; }
                                .header p { margin: 2px 0; }
                                .section { margin: 10px 0; }
                                .section-title { font-weight: bold; border-bottom: 1px solid #000; padding-bottom: 2px; margin-bottom: 5px; }
                                .item { margin: 3px 0; display: flex; justify-content: space-between; }
                                .subtotal { margin-top: 5px; border-top: 1px dashed #000; padding-top: 3px; font-weight: bold; }
                                .total { margin-top: 10px; border-top: 2px solid #000; padding-top: 5px; font-weight: bold; font-size: 14px; }
                                .footer { text-align: center; margin-top: 10px; font-size: 10px; }
                              </style>
                            </head>
                            <body>
                              <div class="header">
                                <h2>${businessInfo?.name || t('hardcoded.billiardClub')}</h2>
                                <p>${translations.monthlyReport}</p>
                                <p>${(() => {
                                  const today = new Date()
                                  const day = today.getDate().toString().padStart(2, '0')
                                  const month = (today.getMonth() + 1).toString().padStart(2, '0')
                                  const year = today.getFullYear()
                                  return `${day}/${month}/${year}`
                                })()}</p>
                                ${businessInfo?.phone ? `<p>${t('bar.tel')}: ${businessInfo.phone}</p>` : ''}
                                ${businessInfo?.address ? `<p>${translations.address}: ${businessInfo.address}</p>` : ''}
                              </div>

                              ${reportIncludes.games ? `
                                <div class="section">
                                  <div class="section-title">${translations.billiardGames} (${monthGames.length})</div>
                                  ${monthGames.length === 0 ? `<p style="text-align: center; color: #666;">${translations.noGamesCompletedThisMonth}</p>` :
                                    monthGames.map(game => `
                                      <div class="item">
                                        <span>${getGameTableName(game.tableNumber)} - ${Math.floor(game.duration / 60)}h ${game.duration % 60}m</span>
                                        <span>${formatCurrency(typeof game.cost === 'string' ? parseFloat(game.cost) : (game.cost || 0), currency)}</span>
                                      </div>
                                    `).join('') + `
                                      <div class="item subtotal">
                                        <span>${translations.gamesSubtotal}</span>
                                        <span>${formatCurrency(totalGamesCost, currency)}</span>
                                      </div>
                                    `
                                  }
                                </div>
                              ` : ''}

                              ${reportIncludes.orders ? `
                                <div class="section">
                                  <div class="section-title">${translations.barOrders} (${monthOrders.length})</div>
                                  ${monthOrders.length === 0 ? `<p style="text-align: center; color: #666;">${translations.noOrdersCompletedThisMonth}</p>` :
                                    monthOrders.sort((a, b) => {
                                      const tableA = getBarTableName(a.table_number)
                                      const tableB = getBarTableName(b.table_number)
                                      return tableA.localeCompare(tableB, undefined, { numeric: true, sensitivity: 'base' })
                                    }).map(order => `
                                      <div class="item">
                                        <span style="font-weight: bold;">${getBarTableName(order.table_number)}</span>
                                        <span>${formatCurrency(typeof order.total === 'string' ? parseFloat(order.total) : (order.total || 0), currency)}</span>
                                      </div>
                                      ${order.items?.map((item: any) => `
                                        <div class="item" style="margin-left: 10px; font-size: 10px;">
                                          <span>${item.quantity}x ${item.name}</span>
                                          <span>${formatCurrency(item.quantity * item.price, currency)}</span>
                                        </div>
                                      `).join('')}
                                    `).join('') + `
                                      <div class="item subtotal">
                                        <span>${translations.ordersSubtotal}</span>
                                        <span>${formatCurrency(totalOrdersCost, currency)}</span>
                                      </div>
                                    `
                                  }
                                </div>
                              ` : ''}

                              <div class="item total">
                                <span>${translations.monthlyTotal}</span>
                                <span>${formatCurrency(grandTotal, currency)}</span>
                              </div>

                              <div class="footer">
                                <p>${translations.reportGenerated} ${new Date().toLocaleString()}</p>
                                ${businessInfo?.vat_number ? `<p>${translations.vat}: ${businessInfo.vat_number}</p>` : ''}
                              </div>
                            </body>
                          </html>
                        `
                        const printWindow = window.open('', '_blank', 'width=300,height=400')
                        if (printWindow) {
                          printWindow.document.documentElement.innerHTML = reportHtml
                          printWindow.print()
                          printWindow.close()
                        }
                      }}
                      className="flex items-center gap-2"
                    >
                      <Printer className="h-4 w-4" />
                      {t('bar.printMonthlyReport')}
                    </Button>
                  </div>
                </>
              )
            })()}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
