"use client"

import { useState, useEffect, useRef } from "react"
import { useSafeTranslation } from '../hooks/useSafeTranslation'
import { But<PERSON> } from "@/components/ui/button"

import { Clock, Bell, BellOff, Timer, Infinity, Eye, Printer } from 'lucide-react'
import { ReceiptPrinter } from "./ReceiptPrinter"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { logReceiptToHistory } from "../utils/receiptLogger"
import { useAuth } from "../contexts/AuthContext"
import { usePermissions } from "../hooks/usePermissions"
import { useDataRefresh } from "../contexts/DataRefreshContext"

import { fetchCurrencySettings, formatCurrency, formatR<PERSON>eiptTotals, generateQRCode, type CurrencySettings } from "../lib/currency"
import { formatLocalizedDate, formatLocalizedTime } from "../utils/dateLocalization"
import { GAME_TABLES, type GameTable } from "../constants/gameTables"
import { getStorageManager } from "../utils/storage"
import { optimizedStorage } from "../utils/storage/OptimizedStorageManager"
import Cookies from 'js-cookie'
import { GameReports } from "./games/GameReports"

interface Game {
  id: string
  tableNumber: number
  startTime: Date
  endTime?: Date
  duration: number
  cost: number
  status: "active" | "completed"
  timeLimit?: number | "unlimited" // in minutes or unlimited
  hasAlerted?: boolean
  created_by_name?: string
  created_by_username?: string
  created_by_role?: string
}

interface GameTimerProps {
  activeGames: Game[]
  setActiveGames: (games: Game[] | ((prevGames: Game[]) => Game[])) => void
  setTotalRevenue: (revenue: number | ((prev: number) => number)) => void
}

export function GameTimer({ activeGames, setActiveGames, setTotalRevenue }: GameTimerProps) {
  const { t, i18n } = useSafeTranslation()
  const { token, user } = useAuth()
  const { filterGameData, filterBarData, canViewAllTables, canViewOwnTablesOnly, canCreateNewGames, canStopAnyGame, canStopOwnGamesOnly } = usePermissions()
  const { refreshTrigger } = useDataRefresh()

  const [gameTables, setGameTables] = useState<GameTable[]>([])
  const [availableTables, setAvailableTables] = useState<any[]>([])
  const [currentTimes, setCurrentTimes] = useState<{ [key: string]: number }>({})
  const [defaultTimeLimit, setDefaultTimeLimit] = useState<'unlimited' | number>('unlimited')
  const [soundEnabled, setSoundEnabled] = useState<boolean>(true)
  const [customLimits, setCustomLimits] = useState<{ [key: number]: number | "unlimited" }>({})
  const audioContextRef = useRef<AudioContext | null>(null)
  const [businessInfo, setBusinessInfo] = useState<{
    name: string
    address: string
    phone: string
    email: string
    vat_number: string
  } | null>(null)
  const [currencySettings, setCurrencySettings] = useState<CurrencySettings | null>(null)
  const [selectedGameForPreview, setSelectedGameForPreview] = useState<Game | null>(null)
  const [activeTablesHeight, setActiveTablesHeight] = useState<number>(0)
  const activeTablesRef = useRef<HTMLDivElement>(null)
  const [todaysSessions, setTodaysSessions] = useState<Game[]>([])
  const [monthSessions, setMonthSessions] = useState<Game[]>([])
  const initialLoadRef = useRef(false)
  const [showDailyReport, setShowDailyReport] = useState(false)
  const [showMonthlyReport, setShowMonthlyReport] = useState(false)
  const [showReportTypeDialog, setShowReportTypeDialog] = useState(false)
  const [pendingReportType, setPendingReportType] = useState<'daily' | 'monthly' | null>(null)
  const [reportIncludes, setReportIncludes] = useState<{games: boolean, orders: boolean}>({games: false, orders: true})
  const [todayGames, setTodayGames] = useState<Game[]>([])
  const [monthGames, setMonthGames] = useState<Game[]>([])
  const [todayOrders, setTodayOrders] = useState<any[]>([])
  const [monthOrders, setMonthOrders] = useState<any[]>([])
  const [allBarTables, setAllBarTables] = useState<any[]>([])
  const [allGameTables, setAllGameTables] = useState<any[]>([])
  const [availableGameTables, setAvailableGameTables] = useState<any[]>([])
  const [allGames, setAllGames] = useState<Game[]>([])
  const [recentOrders, setRecentOrders] = useState<any[]>([])
  const [products, setProducts] = useState<any[]>([])
  const [filteredProducts, setFilteredProducts] = useState<any[]>([])
  const [tableOrders, setTableOrders] = useState<any>({})
  const [selectedTable, setSelectedTable] = useState<number | null>(null)

  // Initialize audio context on first user interaction
  const initAudioContext = () => {
    if (!audioContextRef.current) {
      audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)()
    }
    return audioContextRef.current
  }

  // Play alert sound (default fallback)
  const playDefaultAlertSound = () => {
    if (!soundEnabled) return

    console.log(`🔊 Playing default alert sound`)

    try {
      const audioContext = initAudioContext()

      // Create oscillator for alert sound
      const oscillator = audioContext.createOscillator()
      const gainNode = audioContext.createGain()

      oscillator.type = "sine"
      oscillator.frequency.setValueAtTime(880, audioContext.currentTime) // A5 note

      gainNode.gain.setValueAtTime(0.3, audioContext.currentTime)
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 1)

      oscillator.connect(gainNode)
      gainNode.connect(audioContext.destination)

      oscillator.start()
      oscillator.stop(audioContext.currentTime + 1)

      console.log(`✅ Default alert sound played successfully`)
    } catch (error) {
      console.error("❌ Error playing default alert sound:", error)
    }
  }

  // Play custom alert sound for specific table
  const playCustomAlertSound = (tableNumber: number) => {
    if (!soundEnabled) {
      console.log(`🔇 Sound disabled, skipping alert for table ${tableNumber}`)
      return
    }

    console.log(`🔊 Playing alert sound for table ${tableNumber}`)

    // Find the table configuration - check all possible sources
    const table = gameTables.find(t => t.number === tableNumber) ||
                  allGameTables.find(t => t.number === tableNumber) ||
                  availableGameTables.find(t => t.number === tableNumber)

    console.log(`🔍 Table ${tableNumber} config:`, table)

    // If table has custom sound URL, try to play it
    if (table?.customSoundUrl) {
      console.log(`🎵 Playing custom sound for table ${tableNumber}: ${table.customSoundUrl}`)
      try {
        const audio = new Audio(table.customSoundUrl)
        audio.volume = 0.3
        audio.play().then(() => {
          console.log(`✅ Custom sound played successfully for table ${tableNumber}`)
        }).catch(error => {
          console.error(`❌ Failed to play custom sound for table ${tableNumber}:`, error)
          // Fallback to default sound if custom sound fails
          console.log(`🔄 Falling back to default sound for table ${tableNumber}`)
          playDefaultAlertSound()
        })
        return
      } catch (error) {
        console.error(`❌ Error creating audio for custom sound (table ${tableNumber}):`, error)
        console.log(`🔄 Falling back to default sound for table ${tableNumber}`)
      }
    } else {
      console.log(`🔊 No custom sound set for table ${tableNumber}, using default sound`)
    }

    // Fallback to default sound if no custom sound or if custom sound failed
    playDefaultAlertSound()
  }

  // Optimized timer with reduced frequency and batched updates
  useEffect(() => {
    // Only run timer if there are active games
    if (activeGames.length === 0) {
      return
    }

    const interval = setInterval(() => {
      // Use requestIdleCallback for maximum non-blocking behavior
      if (typeof window !== 'undefined' && 'requestIdleCallback' in window) {
        window.requestIdleCallback(() => {
          const now = Date.now()
          const newTimes: { [key: string]: number } = {}
          const alertedTables: number[] = []
          let hasChanges = false

        // Process active games efficiently
        for (const game of activeGames) {
          if (game.status !== "active") continue

          // Ensure startTime is a valid Date object
          const startTime = game.startTime instanceof Date ? game.startTime : new Date(game.startTime)
          if (isNaN(startTime.getTime())) {
            console.warn('Invalid startTime for game:', game.id, game.startTime)
            continue
          }

          // Calculate elapsed time in minutes
          const elapsedMinutes = Math.floor((now - startTime.getTime()) / (1000 * 60))

          // Only update if time has actually changed
          if (currentTimes[game.id] !== elapsedMinutes) {
            newTimes[game.id] = elapsedMinutes
            hasChanges = true
          }

          // Check if time limit is reached and alert hasn't been triggered yet
          // game.timeLimit is in minutes, so convert elapsedMinutes to match
          if (typeof game.timeLimit === "number" && elapsedMinutes >= game.timeLimit && !game.hasAlerted) {
            alertedTables.push(game.tableNumber)
            console.log(`⏰ Time limit reached for table ${game.tableNumber}: ${elapsedMinutes}min >= ${game.timeLimit}min`)
          }

          // Debug logging for time limit checking (only for tables with time limits)
          if (typeof game.timeLimit === "number" && game.timeLimit !== 999999) {
            console.log(`⏱️ Table ${game.tableNumber}: ${elapsedMinutes}/${game.timeLimit}min, alerted: ${game.hasAlerted}`)
          }
        }

        // Batch state updates to prevent multiple re-renders
        if (hasChanges) {
          setCurrentTimes(prev => ({ ...prev, ...newTimes }))
        }

        // Handle alerts separately to avoid blocking the timer
        if (alertedTables.length > 0) {
          // Update alerted status in a single batch
          setActiveGames((prevGames: Game[]) =>
            prevGames.map((g: Game) =>
              alertedTables.includes(g.tableNumber) ? { ...g, hasAlerted: true } : g
            )
          )

          // Play sounds asynchronously
          setTimeout(() => {
            alertedTables.forEach(tableNumber => {
              playCustomAlertSound(tableNumber)
            })

            if ("Notification" in window && Notification.permission === "granted") {
              new Notification(t('games.timeLimitReached'), {
                body: t('games.timeLimitReachedBody'),
                icon: "/favicon.ico",
              })
            }
          }, 0)
        }
      }, { timeout: 10000 }) // 10 second timeout for requestIdleCallback
      } else {
        // Fallback for environments without requestIdleCallback
        requestAnimationFrame(() => {
          const now = Date.now()
          const newTimes: { [key: string]: number } = {}
          const alertedTables: number[] = []
          let hasChanges = false

          activeGames.forEach((game) => {
            if (game.status === "active") {
              const elapsedSeconds = Math.floor((now - game.startTime.getTime()) / 1000)
              const elapsedMinutes = Math.floor(elapsedSeconds / 60)
              const currentElapsed = currentTimes[game.id] || 0

              // Only update if there's a meaningful change (reduce unnecessary updates)
              if (Math.abs(elapsedSeconds - currentElapsed) >= 1) {
                newTimes[game.id] = elapsedSeconds
                hasChanges = true

                // Check for time limit alerts (only if not already alerted)
                // game.timeLimit is in minutes, so compare with elapsedMinutes
                if (game.timeLimit !== "unlimited" && typeof game.timeLimit === "number" && !game.hasAlerted) {
                  if (elapsedMinutes >= game.timeLimit) {
                    alertedTables.push(game.tableNumber)
                    console.log(`⏰ Time limit reached for table ${game.tableNumber}: ${elapsedMinutes}min >= ${game.timeLimit}min`)
                  }

                  // Debug logging for time limit checking (only for tables with time limits)
                  if (game.timeLimit !== 999999) {
                    console.log(`⏱️ Table ${game.tableNumber}: ${elapsedMinutes}/${game.timeLimit}min, alerted: ${game.hasAlerted}`)
                  }
                }
              }
            }
          })

          // Batch state updates to prevent multiple re-renders
          if (hasChanges) {
            setCurrentTimes(prev => ({ ...prev, ...newTimes }))
          }

          // Handle alerts separately to avoid blocking the timer
          if (alertedTables.length > 0) {
            // Update alerted status in a single batch
            setActiveGames((prevGames: Game[]) =>
              prevGames.map((g: Game) =>
                alertedTables.includes(g.tableNumber) ? { ...g, hasAlerted: true } : g
              )
            )

            // Play sounds asynchronously
            setTimeout(() => {
              alertedTables.forEach(tableNumber => {
                playCustomAlertSound(tableNumber)
              })

              if ("Notification" in window && Notification.permission === "granted") {
                new Notification(t('games.timeLimitReached'), {
                  body: t('games.timeLimitReachedBody'),
                  icon: "/favicon.ico",
                })
              }
            }, 0)
          }
        })
      }
    }, 5000) // Check every 5 seconds for more responsive time limit alerts

    return () => clearInterval(interval)
  }, [activeGames.length]) // Simplified dependency array

  // Separate high-frequency timer for UI updates only (when component is visible)
  useEffect(() => {
    if (activeGames.length === 0) return

    let animationFrame: number
    let lastUpdate = 0
    const UPDATE_INTERVAL = 30000 // Update UI every 30 seconds

    const updateUI = (timestamp: number) => {
      if (timestamp - lastUpdate >= UPDATE_INTERVAL) {
        const now = Date.now()
        const newTimes: { [key: string]: number } = {}

        // Quick UI-only update for display
        for (const game of activeGames) {
          if (game.status === "active") {
            const startTime = game.startTime instanceof Date ? game.startTime : new Date(game.startTime)
            if (!isNaN(startTime.getTime())) {
              newTimes[game.id] = Math.floor((now - startTime.getTime()) / (1000 * 60))
            }
          }
        }

        setCurrentTimes(prev => ({ ...prev, ...newTimes }))
        lastUpdate = timestamp
      }

      animationFrame = requestAnimationFrame(updateUI)
    }

    animationFrame = requestAnimationFrame(updateUI)
    return () => cancelAnimationFrame(animationFrame)
  }, [activeGames.length])

  // Load time limits from cookies
  const loadTimeLimitsFromCookies = () => {
    const cookieLimits: { [key: number]: number | "unlimited" } = {}

    // Get all cookies and find table time limit cookies
    const allCookies = Cookies.get()

    Object.keys(allCookies).forEach(cookieName => {
      if (cookieName.startsWith('table_') && cookieName.endsWith('_time_limit')) {
        const tableNumberMatch = cookieName.match(/table_(\d+)_time_limit/)
        if (tableNumberMatch) {
          const tableNumber = parseInt(tableNumberMatch[1])
          const cookieValue = allCookies[cookieName]
          const limit = cookieValue === 'unlimited' ? 'unlimited' : parseInt(cookieValue)
          cookieLimits[tableNumber] = limit
        }
      }
    })

    return cookieLimits
  }

  // Clear time limit cookies when game stops
  const clearTableTimeLimitCookie = (tableNumber: number) => {
    const cookieKey = `table_${tableNumber}_time_limit`
    Cookies.remove(cookieKey)
    console.log(`🗑️ Cleared time limit cookie for table ${tableNumber}`)
  }



  // Load time limit settings from optimized cache and cookies on component mount
  useEffect(() => {
    const loadTimeLimitSettings = async () => {
      try {
        if (initialLoadRef.current) return
        initialLoadRef.current = true

        // Load default time limit
        let savedDefaultTimeLimit = await optimizedStorage.getJSON<number | "unlimited">('billard_default_time_limit')
        if (savedDefaultTimeLimit === null) {
          const cookieValue = Cookies.get('billard_default_time_limit')
          if (cookieValue) {
            savedDefaultTimeLimit = cookieValue === 'unlimited' ? 'unlimited' : Number(cookieValue)
          setDefaultTimeLimit(savedDefaultTimeLimit)
            await optimizedStorage.setJSON('billard_default_time_limit', savedDefaultTimeLimit)
        } else {
            setDefaultTimeLimit('unlimited')
        }
        } else {
          setDefaultTimeLimit(savedDefaultTimeLimit)
        }

        // Load custom table limits from storage
        const savedCustomLimits = await optimizedStorage.getJSON<{ [key: number]: number | "unlimited" }>('billard_custom_limits')

        // Load time limits from cookies (higher priority for persistence)
        const cookieLimits = loadTimeLimitsFromCookies()

        // Merge storage and cookie limits (cookies take priority)
        const mergedLimits = {
          ...(savedCustomLimits || {}),
          ...cookieLimits
        }



        setCustomLimits(mergedLimits)

        if (Object.keys(cookieLimits).length > 0) {
          // Immediately apply cookie time limits to any existing active games
          if (activeGames.length > 0) {
            const updatedGames = activeGames.map(game => {
              if (game.status === 'active') {
                const cookieTimeLimit = cookieLimits[game.tableNumber]
                if (cookieTimeLimit !== undefined) {
                  return { ...game, timeLimit: cookieTimeLimit, hasAlerted: false }
                }
              }
              return game
            })

            const hasChanges = updatedGames.some((game, index) =>
              game.timeLimit !== activeGames[index].timeLimit
            )

            if (hasChanges) {
              setActiveGames(updatedGames)
            }
          }
        }

      } catch (error) {
        console.error("❌ Failed to load time limit settings from optimized cache:", error)
      }
    }
    loadTimeLimitSettings()
  }, [])

  // Apply cookie time limits to active games when they change
  useEffect(() => {
    if (activeGames.length > 0) {
      const updatedGames = activeGames.map(game => {
        if (game.status === 'active') {
          const cookieTimeLimit = customLimits[game.tableNumber]

          if (cookieTimeLimit !== undefined && game.timeLimit !== cookieTimeLimit) {
            return { ...game, timeLimit: cookieTimeLimit, hasAlerted: false }
          }
        }
        return game
      })

      // Only update if there are actual changes
      const hasChanges = updatedGames.some((game, index) =>
        game.timeLimit !== activeGames[index].timeLimit
      )

      if (hasChanges) {
        setActiveGames(updatedGames)
      }
    }
  }, [customLimits, activeGames]) // Run when customLimits load or when activeGames change

  // Additional effect to apply cookies when activeGames are first loaded
  useEffect(() => {
    if (activeGames.length > 0 && Object.keys(customLimits).length > 0) {
      // Load fresh cookies in case they weren't loaded yet
      const freshCookieLimits = loadTimeLimitsFromCookies()

      if (Object.keys(freshCookieLimits).length > 0) {
        const updatedGames = activeGames.map(game => {
          if (game.status === 'active') {
            const cookieTimeLimit = freshCookieLimits[game.tableNumber]
            if (cookieTimeLimit !== undefined && game.timeLimit !== cookieTimeLimit) {
              return { ...game, timeLimit: cookieTimeLimit, hasAlerted: false }
            }
          }
          return game
        })

        const hasChanges = updatedGames.some((game, index) =>
          game.timeLimit !== activeGames[index].timeLimit
        )

        if (hasChanges) {
          setActiveGames(updatedGames)
        }
      }
    }
  }, [activeGames.length]) // Run when activeGames count changes

  // Save defaultTimeLimit to storage and cookie whenever it changes
  useEffect(() => {
    optimizedStorage.setJSON('billard_default_time_limit', defaultTimeLimit)
    Cookies.set('billard_default_time_limit', defaultTimeLimit.toString(), { expires: 365 })
  }, [defaultTimeLimit])

  // Manual save function for critical events only (no automatic saves)
  const saveToOptimizedCache = async (type: 'gameStart' | 'gameStop' | 'settings') => {
    try {
      if (type === 'settings') {
        // Save settings with optimized cache flow
        await optimizedStorage.setJSON('billard_default_time_limit', defaultTimeLimit)

        if (Object.keys(customLimits).length > 0) {
          await optimizedStorage.setJSON('billard_custom_limits', customLimits)
        }
      } else if (type === 'gameStart' || type === 'gameStop') {
        // Save active games with optimized cache flow
        const activeGamesTimeLimits: { [gameId: string]: number | "unlimited" } = {}
        activeGames.forEach(game => {
          if (game.status === 'active' && game.timeLimit !== undefined) {
            activeGamesTimeLimits[game.id] = game.timeLimit
          }
        })

        if (Object.keys(activeGamesTimeLimits).length > 0) {
          await optimizedStorage.setJSON('billard_active_games_time_limits', activeGamesTimeLimits)
        }
      }

      console.log(`✅ Optimized cache save completed: ${type}`)
    } catch (error) {
      console.error(`❌ Optimized cache save failed for ${type}:`, error)
    }
  }

  // Load game tables and business info from API
  useEffect(() => {
    const loadData = async () => {
      try {
        // Load business information
        const businessResponse = await fetch('/api/business-info')
        if (businessResponse.ok) {
          const businessData = await businessResponse.json()
          setBusinessInfo(businessData)
        }

        // Load currency settings
        const currencyData = await fetchCurrencySettings()
        setCurrencySettings(currencyData)

        // Load game tables from database (user-specific tables only)
        const response = await fetch('/api/gametables/user', {
          credentials: 'include' // Use cookies for authentication
        })
        if (response.ok) {
          const gameTablesData = await response.json()
          setGameTables(gameTablesData.map((t: any) => ({
            id: t.id.toString(),
            number: t.number,
            name: t.name,
            isActive: t.is_active,
            hourlyRate: t.hourly_rate,
            tableType: t.table_type,
            customSoundUrl: t.custom_sound_url
          })))
        }

        // Load bar tables from database (user-specific tables only)
        const barTablesResponse = await fetch('/api/tables/user', {
          credentials: 'include' // Use cookies for authentication
        })
        if (barTablesResponse.ok) {
          const barTablesData = await barTablesResponse.json()
          const mappedTables = barTablesData
            .filter((t: any) => t.is_active)
            .map((t: any) => ({
              id: t.id.toString(),
              number: t.number,
              name: t.name,
              isActive: t.is_active
            }))
          setAvailableTables(mappedTables)
        }

        // Load all games data for reports
        const gamesResponse = await fetch('/api/games', {
          credentials: 'include' // Use cookies for authentication
        })
        if (gamesResponse.ok) {
          const gamesData = await gamesResponse.json()
          const gamesWithDates = gamesData.map((game: any) => ({
            ...game,
            id: game.id.toString(),
            startTime: new Date(game.start_time),
            endTime: game.end_time ? new Date(game.end_time) : undefined,
            tableNumber: game.table_number,
            status: game.status,
            created_by_username: game.created_by_username,
            created_by_name: game.created_by_name,
            created_by_role: game.created_by_role
          }))
          setAllGames(gamesWithDates)
        }

        // Load all bar tables for report lookups
        const allBarTablesResponse = await fetch('/api/tables')
        if (allBarTablesResponse.ok) {
          const allBarTablesData = await allBarTablesResponse.json()
          setAllBarTables(
            allBarTablesData.map((t: any) => ({
              id: t.id,
              number: t.number,
              name: t.name,
              isActive: t.is_active,
            }))
          )
        }

        // Load all game tables for report lookups
        const allGameTablesResponse = await fetch('/api/gametables')
        if (allGameTablesResponse.ok) {
          const allGameTablesData = await allGameTablesResponse.json()
          setAllGameTables(
            allGameTablesData.map((t: any) => ({
              id: t.id,
              number: t.number,
              name: t.name,
              isActive: t.is_active,
              customSoundUrl: t.custom_sound_url,
            }))
          )
        }

        // Load today's sessions
        await fetchTodaysSessions()

        // Load active games on initial mount
        await loadActiveGames()
      } catch (error) {
        console.error("Failed to load data:", error)
      }
    }

    loadData()
  }, [])

  // Request notification permission on component mount
  useEffect(() => {
    if ("Notification" in window && Notification.permission !== "denied") {
      Notification.requestPermission()
    }
  }, [])

  // Optimized data refresh with debouncing and caching
  useEffect(() => {
    let timeoutId: NodeJS.Timeout

    const refreshGamesData = async () => {
      try {
        console.log('Refreshing games data due to data refresh trigger')

        // Debounce rapid refresh requests
        clearTimeout(timeoutId)
        timeoutId = setTimeout(async () => {
          // Only refresh games data (most frequently changing)
          const gamesResponse = await fetch('/api/games', {
            credentials: 'include'
          })
          if (gamesResponse.ok) {
            const games = await gamesResponse.json()
            const gamesWithDates = games.map((game: any) => ({
              ...game,
              id: game.id.toString(),
              startTime: new Date(game.start_time),
              endTime: game.end_time ? new Date(game.end_time) : undefined,
              tableNumber: game.table_number,
              status: game.status,
              created_by_username: game.created_by_username,
              created_by_name: game.created_by_name,
              created_by_role: game.created_by_role
            }))
            setActiveGames(gamesWithDates)
          }

          // Only refresh today's sessions if needed (less frequent)
          if (refreshTrigger % 3 === 0) { // Every 3rd refresh
            await fetchTodaysSessions()
          }
        }, 300) // 300ms debounce
      } catch (error) {
        console.error('Failed to refresh games data:', error)
      }
    }

    if (refreshTrigger > 0) {
      refreshGamesData()
    }

    return () => clearTimeout(timeoutId)
  }, [refreshTrigger, setActiveGames])

  // Optimized height measurement with debouncing
  useEffect(() => {
    let timeoutId: NodeJS.Timeout

    const measureHeight = () => {
      // Debounce height measurements to prevent excessive updates
      clearTimeout(timeoutId)
      timeoutId = setTimeout(() => {
        if (activeTablesRef.current) {
          const height = activeTablesRef.current.offsetHeight
          setActiveTablesHeight(height)
        }
      }, 100) // 100ms debounce
    }

    // Measure on mount
    measureHeight()

    // Use ResizeObserver with throttling for dynamic height changes
    let resizeObserver: ResizeObserver | null = null

    if (typeof window !== 'undefined' && 'ResizeObserver' in window) {
      resizeObserver = new ResizeObserver(() => {
        measureHeight()
      })

      if (activeTablesRef.current) {
        resizeObserver.observe(activeTablesRef.current)
      }
    }

    return () => {
      clearTimeout(timeoutId)
      if (resizeObserver) {
        resizeObserver.disconnect()
      }
    }
  }, [gameTables.length, activeGames.length]) // Only depend on length, not full arrays

  // Load active games from database
  const loadActiveGames = async () => {
    try {
      const response = await fetch('/api/games', {
        credentials: 'include' // Use cookies for authentication
      })
      if (response.ok) {
        const games = await response.json()

        // Filter for active games only
        const activeGamesFromDB = games.filter((game: any) => game.status === 'active')

        // Load saved time limits from Local Storage
        let savedTimeLimits: { [gameId: string]: number | "unlimited" } = {}
        try {
          console.log("🔍 Loading saved active games time limits from Local Storage...")
          const localStorage = getStorageManager('local')
          const savedData = await localStorage.getJSON<{ [gameId: string]: number | "unlimited" }>('billard_active_games_time_limits')
          if (savedData) {
            savedTimeLimits = savedData
            console.log("✅ Loaded saved active games time limits:", savedTimeLimits)
          } else {
            console.log("ℹ️ No saved active games time limits found")
          }
        } catch (error) {
          console.error("❌ Failed to load saved active games time limits:", error)
        }

        // Convert to proper format with Date objects and restore time limits
        const formattedActiveGames = activeGamesFromDB.map((game: any) => {
          const gameId = game.id.toString()
          const savedTimeLimit = savedTimeLimits[gameId]

          return {
            ...game,
            id: gameId,
            startTime: new Date(game.start_time),
            endTime: game.end_time ? new Date(game.end_time) : undefined,
            tableNumber: game.table_number,
            status: game.status,
            created_by_username: game.created_by_username,
            created_by_name: game.created_by_name,
            created_by_role: game.created_by_role,
            timeLimit: savedTimeLimit !== undefined ? savedTimeLimit : "unlimited", // Restore saved time limit or default to unlimited
            hasAlerted: false
          }
        })

        setActiveGames(formattedActiveGames)
        console.log(`Loaded ${formattedActiveGames.length} active games from database`)

        // Initialize timers for active games
        const initialTimes: { [key: string]: number } = {}
        formattedActiveGames.forEach((game: any) => {
          if (game.status === 'active') {
            const startTime = new Date(game.startTime)
            const elapsedMinutes = Math.floor((Date.now() - startTime.getTime()) / (1000 * 60))
            initialTimes[game.id] = elapsedMinutes
          }
        })
        setCurrentTimes(initialTimes)

        // No need to update table status - tables remain available for other users
      }
    } catch (error) {
      console.error('Failed to load active games:', error)
    }
  }

  // Fetch today's completed game sessions from database
  const fetchTodaysSessions = async () => {
    try {
      const response = await fetch('/api/games', {
        credentials: 'include' // Use cookies for authentication
      })
      if (response.ok) {
        const games = await response.json()
        const today = new Date()

        // Filter for today's completed games only
        const todayCompletedGames = games.filter((game: any) => {
          const gameEndDate = game.end_time ? new Date(game.end_time) : null
          return game.status === 'completed' &&
                 gameEndDate &&
                 gameEndDate.toDateString() === today.toDateString()
        })

        // Apply permissions filter - show only games created by current user (unless admin)
        const permissionFilteredGames = filterGameData(todayCompletedGames)

        // Convert to proper format
        const formattedGames = permissionFilteredGames.map((game: any) => ({
          ...game,
          id: game.id.toString(),
          startTime: new Date(game.start_time),
          endTime: game.end_time ? new Date(game.end_time) : undefined,
          tableNumber: game.table_number,
          status: game.status,
          created_by_username: game.created_by_username,
          created_by_name: game.created_by_name,
          created_by_role: game.created_by_role
        }))

        setTodaysSessions(formattedGames)
        console.log(`Loaded ${formattedGames.length} today's sessions for user: ${user?.username}`)
      }
    } catch (error) {
      console.error('Failed to fetch today\'s sessions:', error)
    }
  }

  // Fetch current month's completed game sessions from database
  const fetchMonthSessions = async () => {
    try {
      const response = await fetch('/api/games', {
        credentials: 'include' // Use cookies for authentication
      })
      if (response.ok) {
        const games = await response.json()
        const currentDate = new Date()
        const currentMonth = currentDate.getMonth()
        const currentYear = currentDate.getFullYear()

        // Filter for current month's completed games only
        const monthCompletedGames = games.filter((game: any) => {
          const gameEndDate = game.end_time ? new Date(game.end_time) : null
          return game.status === 'completed' &&
                 gameEndDate &&
                 gameEndDate.getMonth() === currentMonth &&
                 gameEndDate.getFullYear() === currentYear
        })

        // Apply permissions filter - show only games created by current user (unless admin)
        const permissionFilteredGames = filterGameData(monthCompletedGames)

        // Convert to proper format
        const formattedGames = permissionFilteredGames.map((game: any) => ({
          ...game,
          id: game.id.toString(),
          startTime: new Date(game.start_time),
          endTime: game.end_time ? new Date(game.end_time) : undefined,
          tableNumber: game.table_number,
          status: game.status,
          created_by_username: game.created_by_username,
          created_by_name: game.created_by_name,
          created_by_role: game.created_by_role
        }))

        setMonthSessions(formattedGames)
        console.log(`Loaded ${formattedGames.length} month's sessions for user: ${user?.username}`)
      }
    } catch (error) {
      console.error('Failed to fetch month\'s sessions:', error)
    }
  }

  const getBarTableName = (number: number) => {
    return (
      availableTables.find(t => t.number === number)?.name ||
      allBarTables.find(t => t.number === number)?.name ||
      `${number}`
    )
  }

  const getGameTableName = (number: number) => {
    return (
      availableGameTables.find(t => t.number === number)?.name ||
      allGameTables.find(t => t.number === number)?.name ||
      gameTables.find(t => t.number === number)?.name ||
      `${number}`
    )
  }

  // Fetch today's orders for daily report
  const fetchTodayOrders = async () => {
    try {
      const response = await fetch('/api/orders')
      if (response.ok) {
        const orders = await response.json()
        const today = new Date()
        const todayOrdersFiltered = orders.filter((order: any) => {
          const orderDate = new Date(order.created_at)
          return orderDate.toDateString() === today.toDateString()
        })
        setTodayOrders(todayOrdersFiltered)
      }
    } catch (error) {
      console.error('Failed to fetch today orders:', error)
    }
  }

  // Fetch today's games for daily report
  const fetchTodayGames = async () => {
    try {
      const today = new Date()
      const todayGamesFiltered = allGames.filter((game) =>
        game.status === "completed" &&
        game.endTime &&
        new Date(game.endTime).toDateString() === today.toDateString()
      )
      setTodayGames(todayGamesFiltered)
    } catch (error) {
      console.error('Failed to fetch today games:', error)
    }
  }

  // Fetch current month's orders for monthly report
  const fetchMonthOrders = async () => {
    try {
      const response = await fetch('/api/orders')
      if (response.ok) {
        const orders = await response.json()
        const currentDate = new Date()
        const currentMonth = currentDate.getMonth()
        const currentYear = currentDate.getFullYear()

        const monthOrdersFiltered = orders.filter((order: any) => {
          const orderDate = new Date(order.created_at)
          return orderDate.getMonth() === currentMonth && orderDate.getFullYear() === currentYear
        })
        setMonthOrders(monthOrdersFiltered)
      }
    } catch (error) {
      console.error('Failed to fetch month orders:', error)
    }
  }

  // Fetch current month's games for monthly report
  const fetchMonthGames = async () => {
    try {
      const currentDate = new Date()
      const currentMonth = currentDate.getMonth()
      const currentYear = currentDate.getFullYear()

      const monthGamesFiltered = allGames.filter((game) => {
        if (game.status !== "completed" || !game.endTime) return false
        const gameDate = new Date(game.endTime)
        return gameDate.getMonth() === currentMonth && gameDate.getFullYear() === currentYear
      })
      setMonthGames(monthGamesFiltered)
    } catch (error) {
      console.error('Failed to fetch month games:', error)
    }
  }





  const startGame = async (tableNumber: number) => {
    // Prevent duplicate games - check if table already has an active game
    const existingGame = filteredActiveGames.find(game =>
      game.tableNumber === tableNumber && game.status === 'active'
    )
    if (existingGame) {
      console.log('Table already has an active game, ignoring start request')
      return
    }

    // Get time limit for this table (use custom limit or default)
    const timeLimit = customLimits[tableNumber] || defaultTimeLimit
    const startTime = new Date()

    try {
      const response = await fetch('/api/games', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Use cookies for authentication
        body: JSON.stringify({
          tableNumber,
          startTime,
          status: 'active'
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to create game')
      }

      const newGame = await response.json()

      // Create the game object with proper Date objects and initialize all required fields
      const gameWithStartTime: Game = {
        id: newGame.id.toString(),
        tableNumber: tableNumber,
        startTime: new Date(startTime),
        endTime: undefined,
        duration: 0,
        cost: 0,
        status: 'active',
        timeLimit: timeLimit,
        hasAlerted: false,
        created_by_name: user?.fullName,
        created_by_username: user?.username,
        created_by_role: user?.role
      }

      // Add to active games and initialize timer
      setActiveGames((prevGames: Game[]) => [...prevGames, gameWithStartTime])

      // Initialize the timer immediately with 0
      setCurrentTimes(prev => ({
        ...prev,
        [gameWithStartTime.id]: 0
      }))

      // Manual save for game start (critical event)
      await saveToOptimizedCache('gameStart')

      // Update game table status to occupied (but keep it available for other users)
      try {
        const gameTable = gameTables.find(t => t.number === tableNumber)
        if (gameTable) {
          console.log('🎮 Starting game on table:', {
            tableNumber,
            tableName: gameTable.name,
            currentAssignment: {
              userId: gameTable.assignedUserId,
              username: gameTable.assignedUsername
            },
            keepingActive: true
          })

          // Ensure we preserve assignment fields - don't send undefined values
          const updateData = {
            name: gameTable.name,
            isActive: true, // Keep table active/available even when in use
            hourlyRate: gameTable.hourlyRate,
            tableType: gameTable.tableType,
            // Only include assignment fields if they exist, otherwise omit them to preserve DB values
            ...(gameTable.assignedUserId !== undefined && { assignedUserId: gameTable.assignedUserId }),
            ...(gameTable.assignedUsername !== undefined && { assignedUsername: gameTable.assignedUsername })
          }

          console.log('📤 Sending table update for game start:', updateData)

          const response = await fetch(`/api/gametables/${gameTable.id}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            credentials: 'include', // Use cookies for authentication
            body: JSON.stringify(updateData),
          })

          // Update local state if API call was successful
          if (response.ok) {
            const updatedTable = await response.json()
            console.log('✅ Table remains active during game:', {
              tableNumber,
              preservedAssignment: {
                userId: updatedTable.assigned_user_id,
                username: updatedTable.assigned_username
              }
            })

            setGameTables(prevTables =>
              prevTables.map(table =>
                table.number === tableNumber
                  ? {
                      ...table,
                      isActive: true, // Keep active
                      // Use the values returned from the API to ensure consistency
                      assignedUserId: updatedTable.assigned_user_id,
                      assignedUsername: updatedTable.assigned_username
                    }
                  : table
              )
            )
          } else {
            const errorData = await response.json()
            console.error('Failed to update table status:', errorData)
          }
        }
      } catch (error) {
        console.error('Failed to update game table status:', error)
      }

    } catch (error) {
      console.error('Failed to start game:', error)
    }
  }

  const endGame = async (gameId: string) => {
    const game = activeGames.find(g => g.id === gameId)
    if (!game) return

    // Find the game table to get the correct hourly rate
    const gameTable = gameTables.find(t => t.number === game.tableNumber)
    const hourlyRate = gameTable?.hourlyRate || 400

    const endTime = new Date()
    const durationInSeconds = Math.floor((endTime.getTime() - game.startTime.getTime()) / 1000)
    const durationInMinutes = Math.floor(durationInSeconds / 60) // Convert to integer minutes
    const cost = calculateCostFromMinutes(durationInMinutes, hourlyRate)

    try {
      // First update the game status
      const response = await fetch(`/api/games/${gameId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Use cookies for authentication
        body: JSON.stringify({
          endTime,
          duration: durationInMinutes,
          cost,
          status: 'completed'
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to end game')
      }

      // Update game state immediately
      setActiveGames((prevGames: Game[]) =>
        prevGames.map((g: Game) =>
          g.id === gameId
            ? {
                ...g,
                endTime,
                duration: durationInMinutes,
                cost,
                status: 'completed'
              }
            : g
        )
      )

      // Remove the game from current times
      setCurrentTimes(prev => {
        const newTimes = { ...prev }
        delete newTimes[gameId]
        return newTimes
      })

      // Manual save for game stop (critical event)
      await saveToOptimizedCache('gameStop')

      // Clean up the completed game's time limit from Local Storage
      try {
        console.log(`🗑️ Cleaning up time limit for completed game ${gameId} from Local Storage`)
        const localStorage = getStorageManager('local')
        const savedTimeLimits = await localStorage.getJSON<{ [gameId: string]: number | "unlimited" }>('billard_active_games_time_limits') || {}

        if (savedTimeLimits[gameId] !== undefined) {
          delete savedTimeLimits[gameId]
          await localStorage.setJSON('billard_active_games_time_limits', savedTimeLimits)
          console.log(`✅ Cleaned up time limit for game ${gameId} from Local Storage`)
        }
      } catch (error) {
        console.error(`❌ Failed to clean up time limit for game ${gameId}:`, error)
      }

      // Clear time limit cookie for this table when game stops
      try {
        clearTableTimeLimitCookie(game.tableNumber)
        console.log(`✅ Cleared time limit cookie for table ${game.tableNumber}`)
      } catch (error) {
        console.error(`❌ Failed to clear time limit cookie for table ${game.tableNumber}:`, error)
      }

      // Add cost to total revenue
      setTotalRevenue(prev => prev + cost)

      // Refresh today's sessions to include the newly completed game
      await fetchTodaysSessions()

      // Generate receipt with dynamic currency formatting
      const currency = currencySettings || {
        currency: t('games.albanianLek'),
        symbol: 'L',
        showDecimals: false,
        taxIncluded: false,
        taxEnabled: true,
        taxRate: 20,
        qrCodeEnabled: false,
        qrCodeUrl: '',
      }

      // Generate QR code if enabled
      let qrCodeDataURL = ''
      console.log('DEBUG: Starting QR code generation, currency.qrCodeEnabled:', currency.qrCodeEnabled)

      if (currency.qrCodeEnabled) {
        console.log('QR Code enabled, generating QR code...')
        // Fetch business info for QR code
        let businessInfo = null
        try {
          const businessResponse = await fetch('/api/business-info')
          if (businessResponse.ok) {
            businessInfo = await businessResponse.json()
            console.log('Business info fetched:', businessInfo)
          }
        } catch (error) {
          console.error('Failed to fetch business info for QR code:', error)
        }

        qrCodeDataURL = await generateQRCode(currency.qrCodeUrl, businessInfo)
        console.log('QR Code generated:', qrCodeDataURL ? 'Success' : 'Failed')
        if (qrCodeDataURL) {
          console.log('QR Code length:', qrCodeDataURL.length)
          console.log('QR Code starts with:', qrCodeDataURL.substring(0, 50))
        }
      } else {
        console.log('QR Code disabled in currency settings')
      }

      // Generate and print receipt
      const receiptHtml = `
        <html>
          <head>
            <title>${t('games.gameReceipt')}</title>
            <style>
              @page { size: 80mm auto; margin: 0; }
              body {
                font-family: 'Courier New', monospace;
                width: 80mm;
                margin: 0;
                padding: 5mm;
                font-size: 12px;
                line-height: 1.2;
              }
              .header { text-align: center; margin-bottom: 10px; }
              .header h2 { margin: 0; font-size: 14px; }
              .header p { margin: 2px 0; }
              .item { margin: 3px 0; }
              .total { margin-top: 10px; border-top: 1px dashed #000; padding-top: 5px; }
              .footer { text-align: center; margin-top: 10px; font-size: 10px; }

              /* QR Code specific styles for printing */
              img {
                -webkit-print-color-adjust: exact !important;
                print-color-adjust: exact !important;
                color-adjust: exact !important;
                display: block !important;
                visibility: visible !important;
                opacity: 1 !important;
                max-width: none !important;
                max-height: none !important;
              }

              @media print {
                img {
                  -webkit-print-color-adjust: exact !important;
                  print-color-adjust: exact !important;
                  color-adjust: exact !important;
                  display: block !important;
                  visibility: visible !important;
                  opacity: 1 !important;
                  max-width: none !important;
                  max-height: none !important;
                }
              }
            </style>
          </head>
          <body>
            <div class="header">
              <h2>${businessInfo?.name || t('games.billiardClub')}</h2>
              <p>${t('games.gameSessionReceipt')}</p>
              ${businessInfo?.phone ? `<p>Tel: ${businessInfo.phone}</p>` : ''}
              ${businessInfo?.address ? `<p>${t('games.address')}: ${businessInfo.address}</p>` : ''}
              ${businessInfo?.vat_number ? `<p>${t('games.vat')}: ${businessInfo.vat_number}</p>` : ''}
            </div>
            <div class="info">
              <p>${t('games.date')}: ${formatLocalizedDate(endTime, "MMM dd, yyyy HH:mm", i18n.language)}</p>
              <p>${t('games.table')}: ${gameTables.find(t => t.number === game.tableNumber)?.name || `Table ${game.tableNumber}`}</p>
            </div>
            <div class="items">
              <div class="item">
                ${t('games.startTime')}: ${formatLocalizedTime(game.startTime, i18n.language)}
              </div>
              <div class="item">
                ${t('games.endTime')}: ${formatLocalizedTime(endTime, i18n.language)}
              </div>
              <div class="item">
                ${t('games.duration')}: ${Math.floor(durationInMinutes / 60)}h ${durationInMinutes % 60}m
              </div>
            </div>
            <div class="total">
              ${(() => {
                const totalsLines = formatReceiptTotals(cost, currency)
                return totalsLines.map(line => `<p${line.includes('Total:') ? ' style="font-weight: bold;"' : ''}>${line}</p>`).join('')
              })()}
            </div>
            ${qrCodeDataURL ? `
              <div class="footer">
                <div style="margin: 15px 0; text-align: center; page-break-inside: avoid;">
                  <img src="${qrCodeDataURL}" alt="QR Code" style="width: 80px; height: 80px; display: block; margin: 0 auto; -webkit-print-color-adjust: exact; print-color-adjust: exact;" />
                  <p style="font-size: 10px; margin-top: 5px;">
                    ${currency.qrCodeUrl ? t('games.scanForMoreInfo') : t('games.scanForContactInfo')}
                  </p>
                </div>
              </div>
            ` : ''}
          </body>
        </html>
      `

      // Debug: Check if QR code is in the HTML
      if (qrCodeDataURL) {
        console.log('QR code found in receipt HTML:', receiptHtml.includes('img src="data:image'))
      }

      // Create a new window for printing with minimal visibility
      const printWindow = window.open('', '_blank', 'width=300,height=400,scrollbars=yes,resizable=yes')
      if (printWindow) {
        printWindow.document.write(receiptHtml)
        printWindow.document.close()

        // Don't focus the window to keep it less intrusive
        // printWindow.focus()

        // Wait for images (including QR code) to load before printing
        let hasPrinted = false

        if (qrCodeDataURL) {
          // Use window.onload to ensure all content including images are loaded
          printWindow.onload = () => {
            if (!hasPrinted) {
              hasPrinted = true
              setTimeout(() => {
                printWindow.print()
                setTimeout(() => {
                  printWindow.close()
                }, 100) // Small delay before closing
              }, 100) // Small delay after onload
            }
          }

          // Fallback timeout in case onload doesn't fire
          setTimeout(() => {
            if (!hasPrinted && !printWindow.closed) {
              hasPrinted = true
              printWindow.print()
              setTimeout(() => {
                printWindow.close()
              }, 100)
            }
          }, 1000)
        } else {
          setTimeout(() => {
            printWindow.print()
            setTimeout(() => {
              printWindow.close()
            }, 100)
          }, 50)
        }
      }

      // Log receipt to history
      logReceiptToHistory({
        type: 'game',
        data: {
          tableNumber: game.tableNumber,
          cost: cost,
          duration: durationInMinutes,
          items: [],
          startTime: game.startTime,
          endTime: endTime,
          status: 'completed',
          created_at: new Date()
        },
        printedBy: t('hardcoded.system'),
        printedAt: endTime
      })

      // Game ended - no reservation system needed

      // Update game table status to available (keep table active after stopping)
      try {
        const gameTable = gameTables.find(t => t.number === game.tableNumber)
        if (gameTable) {
          console.log('🔄 Keeping table active after stop:', {
            tableNumber: game.tableNumber,
            tableName: gameTable.name,
            currentAssignment: {
              userId: gameTable.assignedUserId,
              username: gameTable.assignedUsername
            },
            settingActive: true
          })

          // Ensure we preserve assignment fields - don't send undefined values
          const updateData = {
            name: gameTable.name,
            isActive: true, // Keep table active/available after stopping
            hourlyRate: gameTable.hourlyRate,
            tableType: gameTable.tableType,
            // Only include assignment fields if they exist, otherwise omit them to preserve DB values
            ...(gameTable.assignedUserId !== undefined && { assignedUserId: gameTable.assignedUserId }),
            ...(gameTable.assignedUsername !== undefined && { assignedUsername: gameTable.assignedUsername })
          }

          console.log('📤 Sending table update:', updateData)

          const tableResponse = await fetch(`/api/gametables/${gameTable.id}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            credentials: 'include', // Use cookies for authentication
            body: JSON.stringify(updateData),
          })

          if (!tableResponse.ok) {
            const errorData = await tableResponse.json()
            console.error('Failed to update table status:', errorData)
            throw new Error(`Failed to update game table status: ${errorData.error}`)
          }

          const updatedTable = await tableResponse.json()
          console.log('✅ Table kept active after stop:', {
            tableNumber: game.tableNumber,
            preservedAssignment: {
              userId: updatedTable.assigned_user_id,
              username: updatedTable.assigned_username
            }
          })

          // Update local state with the response from the API to ensure consistency
          setGameTables(prevTables =>
            prevTables.map(table =>
              table.number === game.tableNumber
                ? {
                    ...table,
                    isActive: true,
                    // Use the values returned from the API to ensure consistency
                    assignedUserId: updatedTable.assigned_user_id,
                    assignedUsername: updatedTable.assigned_username
                  }
                : table
            )
          )
        }
      } catch (error) {
        console.error('Failed to update game table status:', error)
      }

    } catch (error) {
      console.error('Failed to end game:', error)
    }
  }

  const formatTime = (minutes: number) => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    return `${hours.toString().padStart(2, "0")}:${mins.toString().padStart(2, "0")}`
  }

  // Calculate cost based on actual time passed (precise minute-based billing)
  const calculateCostFromMinutes = (minutes: number, hourlyRate: number = 400) => {
    // Minimum billing is 1 minute
    const billableMinutes = Math.max(1, minutes)
    // Calculate cost per minute based on hourly rate
    const costPerMinute = hourlyRate / 60
    return Math.round(billableMinutes * costPerMinute)
  }

  // Real-time cost calculation for display (precise)
  const calculateCurrentCost = (minutes: number) => {
    // Get more precise timing for current cost display
    const gameId = Object.keys(currentTimes).find(id => {
      const game = activeGames.find(g => g.id === id)
      return game && currentTimes[id] === minutes
    })

    if (gameId) {
      const game = activeGames.find(g => g.id === gameId)
      if (game) {
        const preciseSeconds = (Date.now() - game.startTime.getTime()) / 1000
        const preciseMinutes = preciseSeconds / 60
        return calculateCostFromMinutes(preciseMinutes)
      }
    }

    return calculateCostFromMinutes(minutes)
  }

  const setTableTimeLimit = async (tableNumber: number, limit: number | "unlimited") => {
    console.log(`🔄 Setting time limit for table ${tableNumber}: ${limit}`)

    // Update state immediately for responsive UI
    setCustomLimits((prev) => ({
      ...prev,
      [tableNumber]: limit,
    }))

    // If there's an active game on this table, update its time limit
    const updatedGames = activeGames.map((game: Game) => {
      if (game.tableNumber === tableNumber && game.status === "active") {
        const updatedGame = { ...game, timeLimit: limit, hasAlerted: false }

        // Check if the time limit has already been exceeded
        if (typeof limit === "number") {
          const elapsedMinutes = Math.floor((Date.now() - game.startTime.getTime()) / (1000 * 60))
          if (elapsedMinutes >= limit) {
            console.log(`⏰ Time limit already exceeded for table ${tableNumber}: ${elapsedMinutes}min >= ${limit}min - triggering immediate alert`)

            // Trigger immediate sound alert
            setTimeout(() => {
              playCustomAlertSound(tableNumber)

              if ("Notification" in window && Notification.permission === "granted") {
                new Notification(t('games.timeLimitReached'), {
                  body: t('games.timeLimitReachedBody'),
                  icon: "/favicon.ico",
                })
              }
            }, 100) // Small delay to ensure state is updated

            // Mark as alerted so it doesn't trigger again
            updatedGame.hasAlerted = true
          }
        }

        return updatedGame
      }
      return game
    })
    setActiveGames(updatedGames)

    // Save to cookies for persistence across page refreshes
    try {
      const cookieKey = `table_${tableNumber}_time_limit`
      const cookieValue = limit === "unlimited" ? "unlimited" : limit.toString()

      // Set cookie with 7 days expiration
      Cookies.set(cookieKey, cookieValue, {
        expires: 7,
        sameSite: 'strict',
        secure: window.location.protocol === 'https:'
      })

      console.log(`✅ Time limit ${limit} saved to cookie for table ${tableNumber}`)
    } catch (cookieError) {
      console.error(`❌ Failed to save time limit to cookie:`, cookieError)
    }

    // Also save to optimized storage as backup
    try {
      const updatedLimits = {
        ...customLimits,
        [tableNumber]: limit
      }
      await optimizedStorage.setJSON('billard_custom_limits', updatedLimits)
      console.log(`✅ Time limit ${limit} saved to storage for table ${tableNumber}`)
    } catch (storageError) {
      console.error(`❌ Failed to save time limit to storage:`, storageError)
    }

    // Try to save to database if there's an active game
    const activeGameOnTable = updatedGames.find(game =>
      game.tableNumber === tableNumber && game.status === "active"
    )

    if (activeGameOnTable) {
      try {
        console.log(`💾 Saving time limit ${limit} to database for game ${activeGameOnTable.id}`)

        const response = await fetch(`/api/games/${activeGameOnTable.id}`, {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({
            timeLimit: limit
          })
        })

        if (response.ok) {
          console.log(`✅ Time limit ${limit} saved to database for game ${activeGameOnTable.id}`)
        } else {
          console.error(`❌ Database save failed - Status: ${response.status} ${response.statusText}`)

          const errorText = await response.text()
          console.error(`❌ Failed to save time limit to database:`, errorText || 'Empty response')

          // Check if it's likely a missing column error
          if (response.status === 500 || errorText.includes('column') || errorText.includes('time_limit') || !errorText) {
            console.error(`🚨 DATABASE MIGRATION LIKELY NEEDED!`)
            console.error(`🔄 Attempting simple migration...`)

            try {
              const errorData = errorText ? JSON.parse(errorText) : { needsMigration: true }
              if (errorData.needsMigration || !errorText) {
                // Try simple migration
                try {
                  const migrationResponse = await fetch('/api/simple-migrate', {
                    method: 'POST',
                    credentials: 'include'
                  })
                  const migrationResult = await migrationResponse.json()

                  if (migrationResult.success) {
                    console.log(`✅ Simple migration completed! Retrying time limit save...`)

                    // Retry the time limit save
                    const retryResponse = await fetch(`/api/games/${activeGameOnTable.id}`, {
                      method: 'PATCH',
                      headers: {
                        'Content-Type': 'application/json',
                      },
                      credentials: 'include',
                      body: JSON.stringify({
                        timeLimit: limit
                      })
                    })

                    if (retryResponse.ok) {
                      console.log(`✅ Time limit ${limit} saved to database after migration!`)
                    } else {
                      console.error(`❌ Time limit save failed even after migration`)
                    }
                  } else {
                    console.error(`❌ Migration failed:`, migrationResult.message)
                    console.warn(`⚠️ Time limit saved to cookies only - will persist until game stops`)
                  }
                } catch (migrationError) {
                  console.error(`❌ Migration error:`, migrationError)
                  console.warn(`⚠️ Time limit saved to cookies only - will persist until game stops`)
                }
              } else {
                console.warn(`⚠️ Database error but not migration related - time limit saved to cookies only`)
              }
            } catch (parseError) {
              console.error(`❌ Error parsing response:`, parseError)
              console.warn(`⚠️ Time limit saved to cookies only - will persist until game stops`)
            }
          }
        }
      } catch (error) {
        console.error(`❌ Error saving time limit to database:`, error)
        console.warn(`⚠️ Time limit saved to cookies only - will persist until game stops`)
      }
    }
  }

  const getTimeLeftPercentage = (game: Game) => {
    if (game.timeLimit === "unlimited" || typeof game.timeLimit !== "number") return 100
    const elapsed = currentTimes[game.id] || 0
    const percentage = Math.max(0, 100 - (elapsed / game.timeLimit) * 100)
    return percentage
  }



  // Use actual game tables from database instead of hardcoded array
  const filteredActiveGames = filterGameData(activeGames)
  const activeGamesByTable = filteredActiveGames
    .filter(game => game.status === "active")
    .reduce(
      (acc, game) => {
        // Only keep the first active game per table (prevent duplicates)
        if (!acc[game.tableNumber]) {
          acc[game.tableNumber] = game
        }
        return acc
      },
      {} as { [key: number]: Game },
    )

  const timeLimitOptions = [
    { value: "unlimited", label: <div className="flex items-center gap-1"><Infinity className="h-3 w-3" />{t('games.unlimited')}</div> },
    { value: 30, label: `30 ${t('common.minutes')}` },
    { value: 60, label: `1 ${t('common.hour')}` },
    { value: 90, label: `1.5 ${t('common.hours')}` },
    { value: 120, label: `2 ${t('common.hours')}` },
    { value: 180, label: `3 ${t('common.hours')}` },
    { value: 240, label: `4 ${t('common.hours')}` },
  ]

  const makeAllTablesAvailable = async () => {
    try {
      // End all active games
      const activeGamesToEnd = activeGames.filter(game => game.status === 'active')

      for (const game of activeGamesToEnd) {
        const endTime = new Date()
        const durationInSeconds = Math.floor((endTime.getTime() - game.startTime.getTime()) / 1000)
        const durationInMinutes = Math.floor(durationInSeconds / 60)
        const cost = calculateCostFromMinutes(durationInMinutes)

        // Update game status
        await fetch(`/api/games/${game.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include', // Use cookies for authentication
          body: JSON.stringify({
            endTime,
            duration: durationInMinutes,
            cost,
            status: 'completed'
          }),
        })

        // No need to update table status - tables remain available for other users
      }

      // Update local state
      setActiveGames(prevGames =>
        prevGames.map(game =>
          game.status === 'active'
            ? {
                ...game,
                status: 'completed',
                endTime: new Date(),
                duration: Math.floor((Date.now() - game.startTime.getTime()) / 60000),
                cost: calculateCostFromMinutes(Math.floor((Date.now() - game.startTime.getTime()) / 60000))
              }
            : game
        )
      )

      // Clear all current times
      setCurrentTimes({})

    } catch (error) {
      console.error('Failed to make all tables available:', error)
    }
  }

  return (
    <div className="space-y-6">




      {/* Professional Responsive Layout */}
      <div className="grid grid-cols-1 lg:grid-cols-12 gap-4 lg:gap-6">
        {/* Left Section - Clean Active Tables */}
        <div className="lg:col-span-8">
          <div ref={activeTablesRef} className="bg-white border border-gray-200 rounded-2xl shadow-lg overflow-hidden">
            {/* Compact Section Header with Time Limit Settings */}
            <div className="p-3 sm:p-4 bg-gradient-to-r from-green-50 to-emerald-50 border-b border-gray-200">
              <div className="flex flex-col gap-3">
                {/* Main Header Row */}
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-gradient-to-br from-green-600 to-green-700 rounded-lg flex items-center justify-center shadow-sm">
                      <div className="w-4 h-4 bg-white rounded-full"></div>
                    </div>
                    <div>
                      <h3 className="text-base sm:text-lg font-bold text-gray-900">
                        🎱 {t('games.activeTables')}
                      </h3>
                      <p className="text-gray-600 text-xs sm:text-sm">{t('games.liveControlMonitoring')}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="bg-white rounded-lg px-2 py-1 border border-gray-200 shadow-sm">
                      <span className="text-sm font-bold text-green-600">{gameTables.filter(t => t.isActive).length}</span>
                      <span className="text-xs text-gray-500 ml-1">{t('games.tables')}</span>
                    </div>
                    <div className="bg-white rounded-lg px-2 py-1 border border-gray-200 shadow-sm">
                      <span className="text-sm font-bold text-blue-600">{filteredActiveGames.filter(g => g.status === 'active').length}</span>
                      <span className="text-xs text-gray-500 ml-1">{t('games.playing')}</span>
                    </div>
                  </div>
                </div>





                {/* User-Friendly Time Limit Controls */}
                <div className="flex flex-col gap-3 pt-2 border-t border-gray-200">
                  {/* First Row - Time Limit and Sound */}
                  <div className="flex flex-col sm:flex-row sm:items-center gap-3">
                    <div className="flex items-center gap-2">
                      <Timer className="h-4 w-4 text-gray-600" />
                      <Label htmlFor="default-time-limit" className="text-sm font-medium text-gray-700">
                        {t('games.defaultTimeLimit')}:
                      </Label>
                      <Select
                        value={defaultTimeLimit.toString()}
                        onValueChange={async (value) => {
                          const newLimit = value === "unlimited" ? "unlimited" : Number(value)
                          setDefaultTimeLimit(newLimit)
                          // Manual save for settings change
                          await saveToOptimizedCache('settings')
                        }}
                      >
                        <SelectTrigger className="w-28 sm:w-32 h-8 sm:h-8 text-xs bg-white border-gray-200" id="default-time-limit">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {timeLimitOptions.map((option) => (
                            <SelectItem key={option.value} value={option.value.toString()}>
                              {typeof option.label === 'string' ? option.label : option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="flex items-center gap-2">
                      <Switch
                        id="sound-toggle"
                        checked={soundEnabled}
                        onCheckedChange={setSoundEnabled}
                        className="scale-75"
                      />
                      <Label htmlFor="sound-toggle" className="flex items-center gap-1 text-xs text-gray-600">
                        {soundEnabled ? (
                          <>
                            <Bell className="h-3 w-3" />
                            {t('games.soundOn')}
                          </>
                        ) : (
                          <>
                            <BellOff className="h-3 w-3" />
                            {t('games.soundOff')}
                          </>
                        )}
                      </Label>
                    </div>
                  </div>

                  {/* Second Row - Action Buttons (Mobile: Grid, Desktop: Flex) */}
                  <div className="grid grid-cols-3 gap-2 sm:flex sm:items-center sm:gap-3">
                    <Button
                      onClick={makeAllTablesAvailable}
                      variant="outline"
                      size="sm"
                      className="h-8 text-xs px-2 sm:px-3 border-gray-200 hover:bg-gray-50 whitespace-nowrap"
                    >
                      <span className="hidden sm:inline">{t('games.makeAllAvailable')}</span>
                      <span className="sm:hidden">All Ready</span>
                    </Button>

                    <Button
                      onClick={() => {
                        setPendingReportType('daily')
                        setReportIncludes({games: true, orders: true})
                        setShowReportTypeDialog(true)
                      }}
                      variant="outline"
                      size="sm"
                      className="h-7 text-xs px-2"
                    >
                      📊 {t('bar.reports')}
                    </Button>

                    <Button
                      onClick={() => {
                        setPendingReportType('monthly')
                        setReportIncludes({games: true, orders: true})
                        setShowReportTypeDialog(true)
                      }}
                      variant="outline"
                      size="sm"
                      className="h-7 text-xs px-2"
                    >
                      📅 {t('bar.monthlyReports')}
                    </Button>

                  </div>
                </div>
              </div>
            </div>

            {/* User-Friendly Tables Grid */}
            <div className="p-4 sm:p-5 bg-white">
              <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-2 sm:gap-6">
            {gameTables
              .sort((a, b) => a.name.localeCompare(b.name, undefined, { numeric: true, sensitivity: 'base' })) // Sort by table name with natural numerical sorting
              .map((gameTable) => {
              const tableNumber = gameTable.number
              const activeGame = activeGamesByTable[tableNumber]
              const hasActiveGame = !!activeGame
              const isTableActive = gameTable.isActive
              const currentTime = activeGame ? currentTimes[activeGame.id] || 0 : 0
              const timeLimit = activeGame?.timeLimit || customLimits[tableNumber] || defaultTimeLimit



              // Simplified table availability - table is always available if active
              const canUseTable = isTableActive

              return (
                <div key={gameTable.id} className="relative">
                  {/* Bigger Modern Responsive Table Card */}
                  <div
                    className={`
                      group relative w-full rounded-2xl transition-all duration-300 overflow-hidden shadow-xl border-2 backdrop-blur-sm
                      min-h-[180px] aspect-[1.2] sm:min-h-[250px] sm:aspect-[1.4]
                      ${hasActiveGame
                        ? 'bg-gradient-to-br from-emerald-500 via-green-500 to-teal-600 border-emerald-300 shadow-emerald-200/50'
                        : canUseTable
                          ? 'bg-gradient-to-br from-blue-500 via-indigo-500 to-purple-600 border-blue-300 shadow-blue-200/50 hover:shadow-2xl hover:scale-[1.02] cursor-pointer'
                          : 'bg-gradient-to-br from-gray-400 via-gray-500 to-slate-600 border-gray-300 opacity-80 hover:opacity-90'
                      }
                    `}
                    onClick={() => {
                      if (canUseTable && !hasActiveGame) {
                        startGame(tableNumber)
                      }
                    }}
                  >
                    {/* Modern Glass Overlay */}
                    <div className="absolute inset-0 bg-white/10 backdrop-blur-[2px]"></div>

                    {/* Animated Background Pattern */}
                    <div className="absolute inset-0 opacity-10">
                      <div className="absolute top-4 left-4 w-3 h-3 bg-white rounded-full animate-pulse"></div>
                      <div className="absolute top-4 right-4 w-3 h-3 bg-white rounded-full animate-pulse delay-100"></div>
                      <div className="absolute bottom-4 left-4 w-3 h-3 bg-white rounded-full animate-pulse delay-200"></div>
                      <div className="absolute bottom-4 right-4 w-3 h-3 bg-white rounded-full animate-pulse delay-300"></div>
                      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-12 h-12 border-2 border-white rounded-full"></div>
                    </div>

                    {/* Centered Billiard Icon - Static */}
                    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-4xl opacity-20">
                      🎱
                    </div>
                    {/* Perfectly Fitted Content Layout */}
                    <div className="relative h-full flex flex-col text-white p-1.5 sm:p-2 z-10">
                      {/* Compact Header */}
                      <div className="flex items-center justify-between mb-1">
                        <div className="flex items-center gap-1">
                          <div className={`w-1 h-1 rounded-full ${
                            hasActiveGame ? 'bg-green-400 animate-pulse'
                            : isTableActive ? 'bg-blue-400'
                            : 'bg-gray-400'
                          }`}></div>
                          <div className="text-xs font-bold truncate max-w-[80px]" title={gameTable.name}>
                            {gameTable.name || `Table ${tableNumber}`}
                          </div>
                        </div>
                        <div className={`px-1 py-0.5 rounded text-xs font-medium ${
                          hasActiveGame ? 'bg-green-500/30 text-green-100'
                          : isTableActive ? 'bg-blue-500/30 text-blue-100'
                          : 'bg-gray-500/30 text-gray-100'
                        }`}>
                          {hasActiveGame ? t('games.live')
                           : isTableActive ? t('games.ready')
                           : t('games.off')}
                        </div>
                      </div>

                      {/* Compact Default Time Limit */}
                      <div className="text-center mb-1">
                        <div className="text-xs opacity-75 font-medium">
                          {t('games.default')}: {timeLimit === "unlimited" ? "∞" : `${Math.floor(timeLimit / 60)}h`}
                        </div>
                      </div>

                      {/* Time Limit Progress Bar for Active Games */}
                      {hasActiveGame && activeGame.timeLimit !== "unlimited" && (
                        <div className="mb-1">
                          <div className="w-full bg-white/20 rounded-full h-1">
                            <div
                              className="bg-gradient-to-r from-green-400 via-yellow-400 to-red-500 h-1 rounded-full transition-all duration-60000 ease-linear"
                              style={{
                                width: `${(() => {
                                  const totalMinutes = activeGame.timeLimit as number // timeLimit is in minutes
                                  const elapsedMinutes = (Date.now() - activeGame.startTime.getTime()) / (1000 * 60) // Convert to minutes
                                  const remainingMinutes = Math.max(0, totalMinutes - elapsedMinutes)
                                  const percentage = totalMinutes > 0 ? Math.max(0, (remainingMinutes / totalMinutes) * 100) : 0

                                  // When time is up, ensure the bar is completely empty
                                  if (remainingMinutes <= 0) {
                                    return 0
                                  }

                                  return percentage
                                })()}%`
                              }}
                            />
                          </div>
                          <div className="flex justify-between text-xs opacity-60 mt-0.5">
                            <span>
                              {(() => {
                                const totalMinutes = activeGame.timeLimit as number // timeLimit is in minutes
                                const elapsedMinutes = (Date.now() - activeGame.startTime.getTime()) / (1000 * 60) // Convert to minutes
                                const remainingMinutes = Math.max(0, totalMinutes - elapsedMinutes)
                                const hours = Math.floor(remainingMinutes / 60)
                                const mins = Math.floor(remainingMinutes % 60)
                                const secs = Math.floor((remainingMinutes % 1) * 60) // Get remaining seconds from fractional minutes

                                // When time is up, show "Time's Up!"
                                if (remainingMinutes <= 0) {
                                  return t('games.timesUp')
                                }

                                // Show seconds when less than 1 minute remaining
                                if (remainingMinutes < 1) {
                                  return t('games.secondsLeft', { seconds: secs })
                                }

                                return hours > 0 ? t('games.hoursMinutesLeft', { hours, minutes: mins }) : t('games.minutesLeft', { minutes: mins })
                              })()}
                            </span>
                            <span>
                              {(() => {
                                const totalMinutes = activeGame.timeLimit as number
                                const hours = Math.floor(totalMinutes / 60)
                                const mins = totalMinutes % 60
                                if (hours > 0 && mins > 0) {
                                  return `${hours}h ${mins}m ${t('games.limit')}`
                                } else if (hours > 0) {
                                  return `${hours}h ${t('games.limit')}`
                                } else {
                                  return `${mins}m ${t('games.limit')}`
                                }
                              })()}
                            </span>
                          </div>
                        </div>
                      )}

                      {/* Perfectly Fitted Main Content */}
                      <div className="flex-1 flex flex-col justify-evenly min-h-0">
                        {hasActiveGame ? (
                          <>
                            {/* Game Time */}
                            <div className="text-center">
                              <div className="text-sm sm:text-base font-black tracking-wide">
                                {formatTime(currentTime)}
                              </div>
                            </div>

                            {/* User Badge */}
                            <div className="flex justify-center">
                              <div className="flex items-center gap-1 bg-white/25 px-1.5 sm:px-2 py-0.5 sm:py-1 rounded-full text-xs">
                                <div className="w-1 h-1 bg-green-400 rounded-full"></div>
                                <span className="font-medium truncate max-w-16 sm:max-w-20">
                                  {activeGame.created_by_name || activeGame.created_by_username || t('hardcoded.player')}
                                </span>
                              </div>
                            </div>

                            {/* Cost Display */}
                            <div className="flex justify-center">
                              <div className="bg-white/25 px-2 sm:px-3 py-0.5 sm:py-1 rounded-full text-center">
                                <div className="text-xs sm:text-sm font-bold text-white">
                                  {(() => {
                                    const preciseSeconds = (Date.now() - activeGame.startTime.getTime()) / 1000
                                    const preciseMinutes = preciseSeconds / 60
                                    const cost = calculateCostFromMinutes(preciseMinutes, gameTable.hourlyRate)
                                    const currency = currencySettings || {
                                      currency: t('hardcoded.albanianLek'),
                                      symbol: 'L',
                                      showDecimals: false,
                                      taxIncluded: false,
                                      taxEnabled: true,
                                      taxRate: 20,
                                      qrCodeEnabled: false,
                                      qrCodeUrl: '',
                                    }
                                    return formatCurrency(cost, currency)
                                  })()}
                                </div>
                              </div>
                            </div>
                          </>
                        ) : canUseTable ? (
                          <>
                            <div className="text-center space-y-0.5 sm:space-y-1">
                              <div className="text-xs sm:text-sm font-bold">
                                {t('games.readyToPlay')}
                              </div>
                              <div className="text-xs opacity-90">
                                {t('games.tapToStart')}
                              </div>
                              <div className="bg-white/20 px-1.5 sm:px-2 py-0.5 rounded-full text-xs font-semibold">
                                {(() => {
                                  const currency = currencySettings || {
                                    currency: t('hardcoded.albanianLek'),
                                    symbol: 'L',
                                    showDecimals: false,
                                    taxIncluded: false,
                                    taxEnabled: true,
                                    taxRate: 20,
                                    qrCodeEnabled: false,
                                    qrCodeUrl: '',
                                  }
                                  return formatCurrency(gameTable.hourlyRate, currency)
                                })()} {t('games.perHour')}
                              </div>
                            </div>
                          </>
                        ) : (
                          <>
                            {/* Compact Inactive State */}
                            <div className="text-xl opacity-50">🎱</div>

                            <div className="text-center">
                              <div className="text-xs font-semibold opacity-70">
                                {t('games.offline')}
                              </div>
                              <div className="text-xs opacity-50">
                                {t('games.contactStaff')}
                              </div>
                            </div>
                          </>
                        )}
                      </div>

                      {/* Compact Action Controls */}
                      {hasActiveGame && (
                        <div className="space-y-0.5 sm:space-y-1 mt-0.5 sm:mt-1">
                          {/* Compact Time Limit Control */}
                          <Select
                            value={
                              activeGame.timeLimit === "unlimited"
                                ? "unlimited"
                                : activeGame.timeLimit?.toString() || timeLimit.toString()
                            }
                            onValueChange={(value) =>
                              setTableTimeLimit(tableNumber, value === "unlimited" ? "unlimited" : Number(value))
                            }
                          >
                            <SelectTrigger
                              className="w-full h-4 sm:h-6 text-xs bg-white/25 border-white/40 text-white hover:bg-white/35 rounded font-medium"
                              onClick={(e) => e.stopPropagation()}
                            >
                              <div className="flex items-center justify-center gap-0.5 sm:gap-1 w-full">
                                <Timer className="w-1.5 h-1.5 sm:w-2.5 sm:h-2.5" />
                                <span className="text-white/80 text-[10px] sm:text-xs">{t('games.change')}:</span>
                                <span className="font-bold text-white text-[10px] sm:text-xs">
                                  {activeGame.timeLimit === "unlimited"
                                    ? "∞"
                                    : `${Math.floor((activeGame.timeLimit as number) / 60)}h`
                                  }
                                </span>
                              </div>
                            </SelectTrigger>
                            <SelectContent>
                              {timeLimitOptions.map((option) => (
                                <SelectItem key={option.value} value={option.value.toString()}>
                                  {typeof option.label === 'string' ? option.label : option.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>

                          {/* Compact Stop Button */}
                          <Button
                            onClick={(e) => {
                              e.stopPropagation()
                              endGame(activeGame.id)
                            }}
                            className="w-full bg-red-500 hover:bg-red-600 text-white border-0 py-1 sm:py-1.5 text-xs sm:text-xs font-bold rounded h-5 sm:h-auto"
                          >
                            🔴 {t('games.stop')}
                          </Button>
                        </div>
                      )}
                    </div>


                  </div>
                </div>
              )
            })}
              </div>
            </div>
          </div>
        </div>

        {/* Right Section - Compact Game History Table */}
        <div className="lg:col-span-4">
          <div>
            {/* Small User-Friendly Game History Table */}
            <div
              className="bg-white border border-gray-200 rounded-2xl shadow-lg overflow-hidden flex flex-col"
              style={{ height: activeTablesHeight > 0 ? `${activeTablesHeight}px` : 'auto' }}
            >
              {/* Compact Header */}
              <div className="px-3 py-2 bg-gray-50 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Clock className="h-3 w-3 text-gray-600" />
                    <h3 className="text-xs font-semibold text-gray-900">{t('games.todaysSessions')}</h3>
                  </div>
                  <div className="flex items-center gap-3 text-xs text-gray-500">
                    {(() => {
                      // Use the dedicated todaysSessions state instead of filtering activeGames
                      const todayGames = todaysSessions

                      const totalCost = todayGames.reduce((sum, game) => {
                        const cost = typeof game.cost === 'string' ? parseFloat(game.cost) : (game.cost || 0)
                        return sum + cost
                      }, 0)
                      const totalMinutes = todayGames.reduce((sum, game) => sum + (game.duration || 0), 0)
                      const totalHours = Math.floor(totalMinutes / 60)
                      const remainingMinutes = totalMinutes % 60

                      const currency = currencySettings || {
                        currency: t('hardcoded.albanianLek'),
                        symbol: 'L',
                        showDecimals: false,
                        taxIncluded: false,
                        taxEnabled: true,
                        taxRate: 20,
                        qrCodeEnabled: false,
                        qrCodeUrl: '',
                      }

                      return (
                        <>
                          <span>{todayGames.length} {t('games.gamesCount')}</span>
                          <span>•</span>
                          <span className="font-semibold text-green-600">
                            {formatCurrency(totalCost, currency)}
                          </span>
                          <span>•</span>
                          <span>
                            {totalHours > 0 ? `${totalHours}h ${remainingMinutes}m` : `${remainingMinutes}m`}
                          </span>
                        </>
                      )
                    })()}
                  </div>
                </div>
              </div>

              {/* Compact Table Content */}
              <div className="flex-1 overflow-hidden">
                <div className="h-full overflow-y-auto">
                {(() => {
                  // Use the dedicated todaysSessions state instead of filtering activeGames
                  const todayGames = todaysSessions

                  if (todayGames.length === 0) {
                    return (
                      <div className="text-center py-4 px-3">
                        <Clock className="w-6 h-6 mx-auto mb-1 text-gray-400" />
                        <div className="text-xs text-gray-500">{t('games.noGamesToday')}</div>
                      </div>
                    )
                  }

                  return (
                    <table className="w-full text-xs">
                      <thead className="bg-gray-50 border-b border-gray-200 sticky top-0">
                        <tr>
                          <th className="px-2 py-1 text-left text-xs font-medium text-gray-500">{t('games.tableShort')}</th>
                          <th className="px-2 py-1 text-left text-xs font-medium text-gray-500">{t('games.time')}</th>
                          <th className="px-2 py-1 text-left text-xs font-medium text-gray-500">{t('games.cost')}</th>
                          <th className="px-2 py-1 text-left text-xs font-medium text-gray-500">{t('games.player')}</th>
                          <th className="px-2 py-1 text-left text-xs font-medium text-gray-500">{t('games.end')}</th>
                          <th className="px-2 py-1 text-left text-xs font-medium text-gray-500">•</th>
                        </tr>
                      </thead>
                      <tbody className="divide-y divide-gray-100">
                        {todayGames
                          .sort((a, b) => new Date(b.endTime || b.startTime).getTime() - new Date(a.endTime || a.startTime).getTime()) // Latest first
                          .map((game) => (
                            <tr key={game.id} className="hover:bg-gray-50 transition-colors">
                              <td className="px-2 py-1">
                                <div className="min-w-8 h-4 px-1 bg-blue-600 rounded text-white text-xs font-bold flex items-center justify-center whitespace-nowrap" title={getGameTableName(game.tableNumber)}>
                                  {getGameTableName(game.tableNumber)}
                                </div>
                              </td>
                              <td className="px-2 py-1">
                                <div className="text-xs font-medium text-gray-900">
                                  {formatTime(game.duration)}
                                </div>
                              </td>
                              <td className="px-2 py-1">
                                <div className="text-xs font-semibold text-green-600">
                                  {(() => {
                                    const currency = currencySettings || {
                                      currency: t('hardcoded.albanianLek'),
                                      symbol: 'L',
                                      showDecimals: false,
                                      taxIncluded: false,
                                      taxEnabled: true,
                                      taxRate: 20,
                                      qrCodeEnabled: false,
                                      qrCodeUrl: '',
                                    }
                                    return formatCurrency(game.cost, currency)
                                  })()}
                                </div>
                              </td>
                              <td className="px-2 py-1">
                                <div className="text-xs text-gray-600 truncate max-w-16">
                                  {game.created_by_name ? game.created_by_name.split(' ')[0] : t('games.unknown')}
                                </div>
                              </td>
                              <td className="px-2 py-1">
                                <div className="text-xs text-gray-500">
                                  {game.endTime ? formatLocalizedTime(new Date(game.endTime), i18n.language) : t('hardcoded.now')}
                                </div>
                              </td>
                              <td className="px-2 py-1">
                                <div className="flex items-center gap-1">
                                  <Dialog>
                                    <DialogTrigger asChild>
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        className="h-6 w-6 p-0 hover:bg-blue-50 hover:border-blue-300"
                                        onClick={() => setSelectedGameForPreview(game)}
                                      >
                                        <Eye className="h-3 w-3 text-blue-600" />
                                      </Button>
                                    </DialogTrigger>
                                    <DialogContent className="max-w-md">
                                      <DialogHeader>
                                        <DialogTitle className="flex items-center gap-2">
                                          <div className="w-6 h-6 bg-blue-600 rounded text-white text-xs font-bold flex items-center justify-center">
                                            {getGameTableName(game.tableNumber)}
                                          </div>
                                          {t('games.gameSessionReceipt')} - {getGameTableName(game.tableNumber)}
                                        </DialogTitle>
                                      </DialogHeader>
                                      <div className="space-y-4">
                                        {/* Receipt Preview - Identical to Transaction History format */}
                                        <div
                                          className="bg-white border rounded mx-auto"
                                          style={{
                                            fontFamily: "'Courier New', monospace",
                                            fontSize: '12px',
                                            lineHeight: '1.4',
                                            maxWidth: '300px',
                                            padding: '10px'
                                          }}
                                        >
                                          {/* Header - Exact match to Transaction History format */}
                                          <div
                                            className="text-center"
                                            style={{
                                              borderBottom: '2px solid #000',
                                              paddingBottom: '10px',
                                              marginBottom: '15px'
                                            }}
                                          >
                                            <div style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '5px' }}>
                                              {businessInfo?.name || 'BILLIARD CLUB'}
                                            </div>
                                            <div style={{ fontSize: '10px', marginBottom: '5px' }}>
                                              {t('games.gameSessionReceipt')}
                                            </div>
                                            {businessInfo?.vat_number && (
                                              <div style={{ fontSize: '10px', marginBottom: '5px' }}>
                                                VAT: {businessInfo.vat_number}
                                              </div>
                                            )}
                                          </div>

                                          {/* Receipt Info - Exact match to Transaction History format */}
                                          <div style={{ marginBottom: '15px' }}>
                                            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>
                                              <span>Date:</span>
                                              <span>{game.endTime ? formatLocalizedDate(new Date(game.endTime), "MMM dd, yyyy HH:mm", i18n.language) : formatLocalizedDate(new Date(), "MMM dd, yyyy HH:mm", i18n.language)}</span>
                                            </div>
                                            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>
                                              <span>{t('games.table')}:</span>
                                              <span>{getGameTableName(game.tableNumber)}</span>
                                            </div>
                                          </div>

                                          {/* Game Details - Exact match to Transaction History format */}
                                          <div style={{ marginBottom: '15px' }}>
                                            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>
                                              <span>{t('games.startTime')}:</span>
                                              <span>{formatLocalizedTime(new Date(game.startTime), i18n.language)}</span>
                                            </div>
                                            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>
                                              <span>{t('games.endTime')}:</span>
                                              <span>{game.endTime ? formatLocalizedTime(new Date(game.endTime), i18n.language) : t('hardcoded.now')}</span>
                                            </div>
                                            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px' }}>
                                              <span>{t('games.duration')}:</span>
                                              <span>{Math.floor(game.duration / 60)}h {game.duration % 60}m</span>
                                            </div>
                                          </div>

                                          {/* Dashed separator line */}
                                          <div style={{
                                            borderTop: '1px dashed #000',
                                            margin: '10px 0',
                                            width: '100%'
                                          }}></div>

                                          {/* Totals section matching Transaction History format exactly */}
                                          <div style={{ marginBottom: '15px' }}>
                                            {(() => {
                                              const total = game.cost
                                              const taxEnabled = currencySettings?.taxEnabled !== false // Default to true if not specified
                                              const taxRate = currencySettings?.taxRate || 20
                                              // Since prices include tax, calculate subtotal by removing tax from total
                                              const subtotal = taxEnabled ? Math.round(total / (1 + taxRate / 100)) : total
                                              const taxAmount = taxEnabled ? total - subtotal : 0

                                              return (
                                                <>
                                                  {taxEnabled && (
                                                    <>
                                                      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px', fontSize: '12px' }}>
                                                        <span>{t('games.subtotalExclTax')}:</span>
                                                        <span>{subtotal} L</span>
                                                      </div>
                                                      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '5px', fontSize: '12px' }}>
                                                        <span>{t('games.tax', { rate: taxRate })}:</span>
                                                        <span>{taxAmount} L</span>
                                                      </div>
                                                    </>
                                                  )}
                                                  <div style={{
                                                    display: 'flex',
                                                    justifyContent: 'space-between',
                                                    marginBottom: '5px',
                                                    fontSize: '14px',
                                                    fontWeight: 'bold'
                                                  }}>
                                                    <span>{t('games.total')}:</span>
                                                    <span>{total} L</span>
                                                  </div>
                                                </>
                                              )
                                            })()}
                                          </div>

                                          {/* QR Code Section - Exact match to Transaction History format */}
                                          {currencySettings?.qrCodeEnabled && (
                                            <div style={{ textAlign: 'center', marginBottom: '15px' }}>
                                              <img
                                                src={`https://api.qrserver.com/v1/create-qr-code/?size=100x100&data=${encodeURIComponent(
                                                  currencySettings?.qrCodeUrl ||
                                                  `${businessInfo?.name || t('hardcoded.barBilardo')}\nTel: ${businessInfo?.phone || ''}\nAddress: ${businessInfo?.address || ''}`
                                                )}`}
                                                alt="QR Code"
                                                style={{ width: '100px', height: '100px', margin: '0 auto', display: 'block' }}
                                              />
                                            </div>
                                          )}

                                          {/* Dashed separator before footer */}
                                          <div style={{
                                            borderTop: '1px dashed #000',
                                            margin: '15px 0',
                                            width: '100%'
                                          }}></div>


                                        </div>

                                        {/* Print Button */}
                                        <div className="flex justify-center">
                                          <Button
                                            onClick={async () => {
                                              // Generate QR code if enabled (using the same method as main print function)
                                              let qrCodeDataURL = ''
                                              if (currencySettings?.qrCodeEnabled) {
                                                try {
                                                  qrCodeDataURL = await generateQRCode(currencySettings.qrCodeUrl || '', businessInfo)
                                                  console.log('QR code generated for Today\'s Sessions print:', qrCodeDataURL ? 'Success' : 'Failed')
                                                } catch (error) {
                                                  console.error('Failed to generate QR code for Today\'s Sessions:', error)
                                                }
                                              }

                                              // Print the receipt
                                              const receiptHtml = `
                                                <html>
                                                  <head>
                                                    <title>Game Receipt</title>
                                                    <style>
                                                      body { font-family: monospace; font-size: 12px; margin: 20px; }
                                                      .center { text-align: center; }
                                                      .bold { font-weight: bold; }
                                                      .border-bottom { border-bottom: 1px solid #000; padding-bottom: 5px; margin-bottom: 10px; }
                                                      .border-top { border-top: 1px solid #000; padding-top: 5px; margin-top: 10px; }
                                                    </style>
                                                  </head>
                                                  <body>
                                                    <div class="center border-bottom">
                                                      <div class="bold">${businessInfo?.name || 'BILLIARD CLUB'}</div>
                                                      <div>${t('games.gameSessionReceipt')}</div>
                                                    </div>
                                                    <div>
                                                      <div>${t('hardcoded.date')}: ${game.endTime ? formatLocalizedDate(new Date(game.endTime), "MMM dd, yyyy HH:mm", i18n.language) : t('hardcoded.recently')}</div>
                                                      <div>${t('games.table')}: ${getGameTableName(game.tableNumber)}</div>
                                                    </div>
                                                    <br>
                                                    <div>
                                                      <div>${t('games.startTime')}: ${formatLocalizedTime(game.startTime, i18n.language)}</div>
                                                      <div>${t('games.endTime')}: ${game.endTime ? formatLocalizedTime(new Date(game.endTime), i18n.language) : t('hardcoded.recently')}</div>
                                                      <div>${t('games.duration')}: ${Math.floor(game.duration / 60)}h ${game.duration % 60}m</div>
                                                    </div>
                                                    <div class="border-top">
                                                      <div class="bold">${t('hardcoded.total').toUpperCase()}: ${(() => {
                                                        const currency = currencySettings || {
                                                          currency: t('hardcoded.albanianLek'),
                                                          symbol: 'L',
                                                          showDecimals: false,
                                                          taxIncluded: false,
                                                          taxEnabled: true,
                                                          taxRate: 20,
                                                          qrCodeEnabled: false,
                                                          qrCodeUrl: '',
                                                        }
                                                        return formatCurrency(game.cost, currency)
                                                      })()}</div>
                                                    </div>
                                                    ${qrCodeDataURL ? `
                                                      <div style="margin: 15px 0; text-align: center; page-break-inside: avoid;">
                                                        <img src="${qrCodeDataURL}" alt="QR Code" style="width: 80px; height: 80px; display: block; margin: 0 auto; -webkit-print-color-adjust: exact; print-color-adjust: exact;" />
                                                        <p style="font-size: 10px; margin-top: 5px;">
                                                          ${currencySettings?.qrCodeUrl ? t('games.scanForMoreInfo') : t('games.scanForContactInfo')}
                                                        </p>
                                                      </div>
                                                    ` : ''}
                                                    ${game.created_by_name ? `<div class="center border-top">${t('games.servedBy')}: ${game.created_by_name}</div>` : ''}
                                                  </body>
                                                </html>
                                              `
                                              const printWindow = window.open('', '_blank', 'width=300,height=400')
                                              if (printWindow) {
                                                printWindow.document.write(receiptHtml)
                                                printWindow.document.close()

                                                // Wait for images (including QR code) to load before printing
                                                let hasPrinted = false

                                                if (qrCodeDataURL) {
                                                  // Use window.onload to ensure all content including images are loaded
                                                  printWindow.onload = () => {
                                                    if (!hasPrinted) {
                                                      hasPrinted = true
                                                      setTimeout(() => {
                                                        printWindow.print()
                                                        setTimeout(() => {
                                                          printWindow.close()
                                                        }, 100) // Small delay before closing
                                                      }, 100) // Small delay after onload
                                                    }
                                                  }

                                                  // Fallback timeout in case onload doesn't fire
                                                  setTimeout(() => {
                                                    if (!hasPrinted && !printWindow.closed) {
                                                      hasPrinted = true
                                                      printWindow.print()
                                                      setTimeout(() => {
                                                        printWindow.close()
                                                      }, 100)
                                                    }
                                                  }, 1000)
                                                } else {
                                                  setTimeout(() => {
                                                    printWindow.print()
                                                    setTimeout(() => {
                                                      printWindow.close()
                                                    }, 100)
                                                  }, 50)
                                                }
                                              }
                                            }}
                                            className="flex items-center gap-2"
                                          >
                                            <Printer className="h-4 w-4" />
                                            {t('bar.printReceipt')}
                                          </Button>
                                        </div>
                                      </div>
                                    </DialogContent>
                                  </Dialog>
                                </div>
                              </td>
                            </tr>
                          ))}
                      </tbody>
                    </table>
                  )
                })()}
                </div>
              </div>

            </div>
          </div>
        </div>
      </div>

      {/* Daily Report Dialog */}
      {showDailyReport && (
        <Dialog open={showDailyReport} onOpenChange={setShowDailyReport}>
          <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                📊 {t('games.dailyReport')} - {(() => {
                  const today = new Date()
                  const day = today.getDate().toString().padStart(2, '0')
                  const month = (today.getMonth() + 1).toString().padStart(2, '0')
                  const year = today.getFullYear()
                  return `${day}/${month}/${year}`
                })()}
              </DialogTitle>
            </DialogHeader>
            <div className="space-y-6">
              <div className="bg-gray-50 p-6 rounded-lg font-mono text-sm">
                <div className="text-center border-b border-gray-300 pb-4 mb-4">
                  <div className="font-bold text-lg">{businessInfo?.name || 'BILLIARD CLUB'}</div>
                  <div className="text-sm">{t('games.dailyReport')}</div>
                  <div className="text-xs">{(() => {
                    const today = new Date()
                    const day = today.getDate().toString().padStart(2, '0')
                    const month = (today.getMonth() + 1).toString().padStart(2, '0')
                    const year = today.getFullYear()
                    return `${day}/${month}/${year}`
                  })()}</div>
                  {businessInfo?.phone && <div className="text-xs">Tel: {businessInfo.phone}</div>}
                  {businessInfo?.address && <div className="text-xs">Address: {businessInfo.address}</div>}
                </div>

                <div className="mb-4">
                  <div className="font-bold border-b border-gray-300 pb-1 mb-2">{t('games.billiardGames')} ({todaysSessions.length})</div>
                  {todaysSessions.length === 0 ? (
                    <div className="text-gray-500 text-center py-2">{t('games.noGamesToday')}</div>
                  ) : (
                    <div className="space-y-1">
                      {todaysSessions.map((game) => (
                        <div key={game.id} className="flex justify-between">
                          <span>{getGameTableName(game.tableNumber)} - {Math.floor(game.duration / 60)}h {game.duration % 60}m</span>
                          <span>{formatCurrency(game.cost, currencySettings || {
                            currency: t('hardcoded.albanianLek'),
                            symbol: 'L',
                            showDecimals: false,
                            taxIncluded: false,
                            taxEnabled: true,
                            taxRate: 20,
                            qrCodeEnabled: false,
                            qrCodeUrl: '',
                          })}</span>
                        </div>
                      ))}
                      <div className="flex justify-between font-bold border-t border-gray-300 pt-1 mt-2">
                        <span>{t('games.total')}</span>
                        <span>{formatCurrency(todaysSessions.reduce((sum, game) => sum + game.cost, 0), currencySettings || {
                          currency: t('hardcoded.albanianLek'),
                          symbol: 'L',
                          showDecimals: false,
                          taxIncluded: false,
                          taxEnabled: true,
                          taxRate: 20,
                          qrCodeEnabled: false,
                          qrCodeUrl: '',
                        })}</span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}

      {/* Report Type Selection Dialog */}
      <Dialog open={showReportTypeDialog} onOpenChange={setShowReportTypeDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>
              {pendingReportType === 'daily' ? `📊 ${t('bar.dailyReport')}` : `📅 ${t('bar.monthlyReport')}`}
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <p className="text-sm text-gray-600">
              {t('bar.chooseWhatToInclude', { reportType: pendingReportType })}
            </p>

            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <input
                  type="radio"
                  id="games-only"
                  name="reportType"
                  value="games"
                  checked={reportIncludes.games && !reportIncludes.orders}
                  onChange={() => setReportIncludes({games: true, orders: false})}
                  className="w-4 h-4 text-blue-600"
                />
                <label htmlFor="games-only" className="text-sm font-medium text-gray-700">
                  🎱 {t('bar.gamesReportOnly')}
                </label>
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="radio"
                  id="orders-only"
                  name="reportType"
                  value="orders"
                  checked={!reportIncludes.games && reportIncludes.orders}
                  onChange={() => setReportIncludes({games: false, orders: true})}
                  className="w-4 h-4 text-blue-600"
                />
                <label htmlFor="orders-only" className="text-sm font-medium text-gray-700">
                  🍺 {t('bar.barReportOnly')}
                </label>
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="radio"
                  id="both"
                  name="reportType"
                  value="both"
                  checked={reportIncludes.games && reportIncludes.orders}
                  onChange={() => setReportIncludes({games: true, orders: true})}
                  className="w-4 h-4 text-blue-600"
                />
                <label htmlFor="both" className="text-sm font-medium text-gray-700">
                  📊 {t('bar.bothGamesAndBar')}
                </label>
              </div>
            </div>

            <div className="flex justify-end gap-2 pt-4">
              <Button
                variant="outline"
                onClick={() => setShowReportTypeDialog(false)}
                size="sm"
              >
                {t('bar.cancel')}
              </Button>
              <Button
                onClick={() => {
                  if (pendingReportType === 'daily') {
                    fetchTodayOrders()
                    fetchTodayGames()
                    setShowDailyReport(true)
                  } else if (pendingReportType === 'monthly') {
                    fetchMonthOrders()
                    fetchMonthGames()
                    setShowMonthlyReport(true)
                  }
                  setShowReportTypeDialog(false)
                }}
                size="sm"
              >
                {t('bar.generateReport')}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
      {/* Daily Report Dialog */}
      <Dialog open={showDailyReport} onOpenChange={setShowDailyReport}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              📊 {t('bar.dailyBarReport')} - {(() => {
                const today = new Date()
                const day = today.getDate().toString().padStart(2, '0')
                const month = (today.getMonth() + 1).toString().padStart(2, '0')
                const year = today.getFullYear()
                return `${day}/${month}/${year}`
              })()}
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-6">
            {(() => {
              const totalGamesCost = todayGames.reduce((sum, game) => {
                const cost = typeof game.cost === 'string' ? parseFloat(game.cost) : (game.cost || 0)
                return sum + cost
              }, 0)

              const totalOrdersCost = todayOrders.reduce((sum, order) => {
                const orderTotal = typeof order.total === 'string' ? parseFloat(order.total) : (order.total || 0)
                return sum + orderTotal
              }, 0)
              const grandTotal = (reportIncludes.games ? totalGamesCost : 0) + (reportIncludes.orders ? totalOrdersCost : 0)

              const currency = currencySettings || {
                currency: t('bar.albanianLek'),
                symbol: 'L',
                showDecimals: false,
                taxIncluded: false,
                taxEnabled: true,
                taxRate: 20,
                qrCodeEnabled: false,
                qrCodeUrl: '',
              }

              return (
                <>
                  {/* Daily Report Preview */}
                  <div className="bg-gray-50 p-6 rounded-lg font-mono text-sm">
                    <div className="text-center border-b border-gray-300 pb-4 mb-4">
                      <div className="font-bold text-lg">{businessInfo?.name || 'BILLIARD CLUB'}</div>
                      <div className="text-sm">{t('bar.dailyReport')}</div>
                      <div className="text-xs">{(() => {
                        const today = new Date()
                        const day = today.getDate().toString().padStart(2, '0')
                        const month = (today.getMonth() + 1).toString().padStart(2, '0')
                        const year = today.getFullYear()
                        return `${day}/${month}/${year}`
                      })()}</div>
                      {businessInfo?.phone && <div className="text-xs">Tel: {businessInfo.phone}</div>}
                      {businessInfo?.address && <div className="text-xs">Address: {businessInfo.address}</div>}
                    </div>

                    {/* Games Section */}
                    {reportIncludes.games && (
                      <div className="mb-4">
                        <div className="font-bold border-b border-gray-300 pb-1 mb-2">{t('bar.billiardGames')} ({todayGames.length})</div>
                        {todayGames.length === 0 ? (
                          <div className="text-gray-500 text-center py-2">{t('bar.noGamesCompletedToday')}</div>
                        ) : (
                          <div className="space-y-1">
                            {todayGames.map((game) => (
                              <div key={game.id} className="flex justify-between">
                                <span>{getGameTableName(game.tableNumber)} - {Math.floor(game.duration / 60)}h {game.duration % 60}m</span>
                                <span>{formatCurrency(typeof game.cost === 'string' ? parseFloat(game.cost) : (game.cost || 0), currency)}</span>
                              </div>
                            ))}
                            <div className="flex justify-between font-bold border-t border-gray-300 pt-1 mt-2">
                              <span>{t('bar.gamesSubtotal')}</span>
                              <span>{formatCurrency(totalGamesCost, currency)}</span>
                            </div>
                          </div>
                        )}
                      </div>
                    )}

                    {/* Orders Section */}
                    {reportIncludes.orders && (
                      <div className="mb-4">
                        <div className="font-bold border-b border-gray-300 pb-1 mb-2">{t('bar.barOrders')} ({todayOrders.length})</div>
                        {todayOrders.length === 0 ? (
                          <div className="text-gray-500 text-center py-2">{t('bar.noOrdersCompletedToday')}</div>
                        ) : (
                          <div className="space-y-2 max-h-40 overflow-y-auto">
                            {todayOrders.sort((a, b) => {
                              const tableA = getBarTableName(a.table_number)
                              const tableB = getBarTableName(b.table_number)
                              return tableA.localeCompare(tableB, undefined, { numeric: true, sensitivity: 'base' })
                            }).map((order) => (
                              <div key={order.id} className="border-b border-gray-200 pb-2 last:border-b-0">
                                <div className="flex justify-between font-medium">
                                  <span className="font-bold">{getBarTableName(order.table_number)}</span>
                                  <span>{formatCurrency(typeof order.total === 'string' ? parseFloat(order.total) : (order.total || 0), currency)}</span>
                                </div>
                                <div className="text-xs text-gray-600 ml-2">
                                  {order.items?.map((item: any, index: number) => (
                                    <div key={index} className="flex justify-between">
                                      <span>{item.quantity}x {item.name}</span>
                                      <span>{formatCurrency(item.quantity * item.price, currency)}</span>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            ))}
                            <div className="flex justify-between font-bold border-t border-gray-300 pt-1 mt-2">
                              <span>{t('bar.ordersSubtotal')}</span>
                              <span>{formatCurrency(totalOrdersCost, currency)}</span>
                            </div>
                          </div>
                        )}
                      </div>
                    )}

                    <div className="flex justify-between font-bold text-lg border-t-2 border-gray-400 pt-2">
                      <span>{t('bar.dailyTotal')}</span>
                      <span>{formatCurrency(grandTotal, currency)}</span>
                    </div>
                  </div>

                  {/* Print Button */}
                  <div className="flex justify-center">
                    <Button
                      onClick={() => {
                        // Generate and print daily report
                        // Pre-evaluate translations to avoid template literal issues
                        const translations = {
                          dailyReport: t('bar.dailyReport'),
                          billiardGames: t('bar.billiardGames'),
                          barOrders: t('bar.barOrders'),
                          noGamesCompletedToday: t('bar.noGamesCompletedToday'),
                          noOrdersCompletedToday: t('bar.noOrdersCompletedToday'),
                          gamesSubtotal: t('bar.gamesSubtotal'),
                          ordersSubtotal: t('bar.ordersSubtotal'),
                          dailyTotal: t('bar.dailyTotal'),
                          reportGenerated: t('bar.reportGenerated'),
                          address: t('bar.address'),
                          vat: t('bar.vat')
                        }

                        const reportHtml = `
                          <html>
                            <head>
                              <title>${translations.dailyReport}</title>
                              <style>
                                @page { size: 80mm auto; margin: 0; }
                                body {
                                  font-family: 'Courier New', monospace;
                                  width: 80mm;
                                  margin: 0;
                                  padding: 5mm;
                                  font-size: 12px;
                                  line-height: 1.2;
                                }
                                .header { text-align: center; margin-bottom: 10px; }
                                .header h2 { margin: 0; font-size: 14px; }
                                .header p { margin: 2px 0; }
                                .section { margin: 10px 0; }
                                .section-title { font-weight: bold; border-bottom: 1px solid #000; padding-bottom: 2px; margin-bottom: 5px; }
                                .item { margin: 3px 0; display: flex; justify-content: space-between; }
                                .subtotal { margin-top: 5px; border-top: 1px dashed #000; padding-top: 3px; font-weight: bold; }
                                .total { margin-top: 10px; border-top: 2px solid #000; padding-top: 5px; font-weight: bold; font-size: 14px; }
                                .footer { text-align: center; margin-top: 10px; font-size: 10px; }
                              </style>
                            </head>
                            <body>
                              <div class="header">
                                <h2>${businessInfo?.name || 'BILLIARD CLUB'}</h2>
                                <p>${translations.dailyReport}</p>
                                <p>${(() => {
                                  const today = new Date()
                                  const day = today.getDate().toString().padStart(2, '0')
                                  const month = (today.getMonth() + 1).toString().padStart(2, '0')
                                  const year = today.getFullYear()
                                  return `${day}/${month}/${year}`
                                })()}</p>
                                ${businessInfo?.phone ? `<p>Tel: ${businessInfo.phone}</p>` : ''}
                                ${businessInfo?.address ? `<p>${translations.address}: ${businessInfo.address}</p>` : ''}
                              </div>

                              ${reportIncludes.games ? `
                                <div class="section">
                                  <div class="section-title">${translations.billiardGames} (${todayGames.length})</div>
                                  ${todayGames.length === 0 ? `<p style="text-align: center; color: #666;">${translations.noGamesCompletedToday}</p>` :
                                    todayGames.map(game => `
                                      <div class="item">
                                        <span>${getGameTableName(game.tableNumber)} - ${Math.floor(game.duration / 60)}h ${game.duration % 60}m</span>
                                        <span>${formatCurrency(typeof game.cost === 'string' ? parseFloat(game.cost) : (game.cost || 0), currency)}</span>
                                      </div>
                                    `).join('') + `
                                      <div class="item subtotal">
                                        <span>${translations.gamesSubtotal}</span>
                                        <span>${formatCurrency(totalGamesCost, currency)}</span>
                                      </div>
                                    `
                                  }
                                </div>
                              ` : ''}

                              ${reportIncludes.orders ? `
                                <div class="section">
                                  <div class="section-title">${translations.barOrders} (${todayOrders.length})</div>
                                  ${todayOrders.length === 0 ? `<p style="text-align: center; color: #666;">${translations.noOrdersCompletedToday}</p>` :
                                    todayOrders.sort((a, b) => {
                                      const tableA = getBarTableName(a.table_number)
                                      const tableB = getBarTableName(b.table_number)
                                      return tableA.localeCompare(tableB, undefined, { numeric: true, sensitivity: 'base' })
                                    }).map(order => `
                                      <div class="item">
                                        <span style="font-weight: bold;">${getBarTableName(order.table_number)}</span>
                                        <span>${formatCurrency(typeof order.total === 'string' ? parseFloat(order.total) : (order.total || 0), currency)}</span>
                                      </div>
                                      ${order.items?.map((item: any) => `
                                        <div class="item" style="margin-left: 10px; font-size: 10px;">
                                          <span>${item.quantity}x ${item.name}</span>
                                          <span>${formatCurrency(item.quantity * item.price, currency)}</span>
                                        </div>
                                      `).join('')}
                                    `).join('') + `
                                      <div class="item subtotal">
                                        <span>${translations.ordersSubtotal}</span>
                                        <span>${formatCurrency(totalOrdersCost, currency)}</span>
                                      </div>
                                    `
                                  }
                                </div>
                              ` : ''}

                              <div class="item total">
                                <span>${translations.dailyTotal}</span>
                                <span>${formatCurrency(grandTotal, currency)}</span>
                              </div>

                              <div class="footer">
                                <p>${translations.reportGenerated} ${new Date().toLocaleString()}</p>
                                ${businessInfo?.vat_number ? `<p>${translations.vat}: ${businessInfo.vat_number}</p>` : ''}
                              </div>
                            </body>
                          </html>
                        `
                        const printWindow = window.open('', '_blank', 'width=300,height=400')
                        if (printWindow) {
                          printWindow.document.documentElement.innerHTML = reportHtml
                          printWindow.print()
                          printWindow.close()
                        }
                      }}
                      className="flex items-center gap-2"
                    >
                      <Printer className="h-4 w-4" />
                      {t('bar.printDailyReport')}
                    </Button>
                  </div>
                </>
              )
            })()}
          </div>
        </DialogContent>
      </Dialog>

      {/* Monthly Report Dialog */}
      <Dialog open={showMonthlyReport} onOpenChange={setShowMonthlyReport}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              📅 {t('bar.monthlyBarReport')} - {(() => {
                const today = new Date()
                const day = today.getDate().toString().padStart(2, '0')
                const month = (today.getMonth() + 1).toString().padStart(2, '0')
                const year = today.getFullYear()
                return `${day}/${month}/${year}`
              })()}
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-6">
            {(() => {
              const totalGamesCost = monthGames.reduce((sum, game) => {
                const cost = typeof game.cost === 'string' ? parseFloat(game.cost) : (game.cost || 0)
                return sum + cost
              }, 0)

              const totalOrdersCost = monthOrders.reduce((sum, order) => {
                const orderTotal = typeof order.total === 'string' ? parseFloat(order.total) : (order.total || 0)
                return sum + orderTotal
              }, 0)
              const grandTotal = (reportIncludes.games ? totalGamesCost : 0) + (reportIncludes.orders ? totalOrdersCost : 0)

              const currency = currencySettings || {
                currency: t('hardcoded.albanianLek'),
                symbol: 'L',
                showDecimals: false,
                taxIncluded: false,
                taxEnabled: true,
                taxRate: 20,
                qrCodeEnabled: false,
                qrCodeUrl: '',
              }

              return (
                <>
                  {/* Monthly Report Preview */}
                  <div className="bg-gray-50 p-6 rounded-lg font-mono text-sm">
                    <div className="text-center border-b border-gray-300 pb-4 mb-4">
                      <div className="font-bold text-lg">{businessInfo?.name || 'BILLIARD CLUB'}</div>
                      <div className="text-sm">{t('bar.monthlyReport')}</div>
                      <div className="text-xs">{(() => {
                        const today = new Date()
                        const day = today.getDate().toString().padStart(2, '0')
                        const month = (today.getMonth() + 1).toString().padStart(2, '0')
                        const year = today.getFullYear()
                        return `${day}/${month}/${year}`
                      })()}</div>
                      {businessInfo?.phone && <div className="text-xs">Tel: {businessInfo.phone}</div>}
                      {businessInfo?.address && <div className="text-xs">Address: {businessInfo.address}</div>}
                    </div>

                    {/* Games Section */}
                    {reportIncludes.games && (
                      <div className="mb-4">
                        <div className="font-bold border-b border-gray-300 pb-1 mb-2">{t('bar.billiardGames')} ({monthGames.length})</div>
                        {monthGames.length === 0 ? (
                          <div className="text-gray-500 text-center py-2">{t('bar.noGamesCompletedThisMonth')}</div>
                        ) : (
                          <div className="space-y-1 max-h-40 overflow-y-auto">
                            {monthGames.map((game) => (
                              <div key={game.id} className="flex justify-between">
                                <span>{getGameTableName(game.tableNumber)} - {Math.floor(game.duration / 60)}h {game.duration % 60}m</span>
                                <span>{formatCurrency(typeof game.cost === 'string' ? parseFloat(game.cost) : (game.cost || 0), currency)}</span>
                              </div>
                            ))}
                            <div className="flex justify-between font-bold border-t border-gray-300 pt-1 mt-2">
                              <span>{t('bar.gamesSubtotal')}</span>
                              <span>{formatCurrency(totalGamesCost, currency)}</span>
                            </div>
                          </div>
                        )}
                      </div>
                    )}

                    {/* Orders Section */}
                    {reportIncludes.orders && (
                      <div className="mb-4">
                        <div className="font-bold border-b border-gray-300 pb-1 mb-2">{t('bar.barOrders')} ({monthOrders.length})</div>
                        {monthOrders.length === 0 ? (
                          <div className="text-gray-500 text-center py-2">{t('bar.noOrdersCompletedThisMonth')}</div>
                        ) : (
                          <div className="space-y-2 max-h-40 overflow-y-auto">
                            {monthOrders.sort((a, b) => {
                              const tableA = getBarTableName(a.table_number)
                              const tableB = getBarTableName(b.table_number)
                              return tableA.localeCompare(tableB, undefined, { numeric: true, sensitivity: 'base' })
                            }).map((order) => (
                              <div key={order.id} className="border-b border-gray-200 pb-2 last:border-b-0">
                                <div className="flex justify-between font-medium">
                                  <span className="font-bold">{getBarTableName(order.table_number)}</span>
                                  <span>{formatCurrency(typeof order.total === 'string' ? parseFloat(order.total) : (order.total || 0), currency)}</span>
                                </div>
                                <div className="text-xs text-gray-600 ml-2">
                                  {order.items?.map((item: any, index: number) => (
                                    <div key={index} className="flex justify-between">
                                      <span>{item.quantity}x {item.name}</span>
                                      <span>{formatCurrency(item.quantity * item.price, currency)}</span>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            ))}
                            <div className="flex justify-between font-bold border-t border-gray-300 pt-1 mt-2">
                              <span>{t('bar.ordersSubtotal')}</span>
                              <span>{formatCurrency(totalOrdersCost, currency)}</span>
                            </div>
                          </div>
                        )}
                      </div>
                    )}

                    <div className="flex justify-between font-bold text-lg border-t-2 border-gray-400 pt-2">
                      <span>{t('bar.monthlyTotal')}</span>
                      <span>{formatCurrency(grandTotal, currency)}</span>
                    </div>
                  </div>

                  {/* Print Button */}
                  <div className="flex justify-center">
                    <Button
                      onClick={() => {
                        // Generate and print monthly report
                        // Pre-evaluate translations to avoid template literal issues
                        const translations = {
                          monthlyReport: t('bar.monthlyReport'),
                          billiardGames: t('bar.billiardGames'),
                          barOrders: t('bar.barOrders'),
                          noGamesCompletedThisMonth: t('bar.noGamesCompletedThisMonth'),
                          noOrdersCompletedThisMonth: t('bar.noOrdersCompletedThisMonth'),
                          gamesSubtotal: t('bar.gamesSubtotal'),
                          ordersSubtotal: t('bar.ordersSubtotal'),
                          monthlyTotal: t('bar.monthlyTotal'),
                          reportGenerated: t('bar.reportGenerated'),
                          address: t('bar.address'),
                          vat: t('bar.vat')
                        }

                        const reportHtml = `
                          <html>
                            <head>
                              <title>${translations.monthlyReport}</title>
                              <style>
                                @page { size: 80mm auto; margin: 0; }
                                body {
                                  font-family: 'Courier New', monospace;
                                  width: 80mm;
                                  margin: 0;
                                  padding: 5mm;
                                  font-size: 12px;
                                  line-height: 1.2;
                                }
                                .header { text-align: center; margin-bottom: 10px; }
                                .header h2 { margin: 0; font-size: 14px; }
                                .header p { margin: 2px 0; }
                                .section { margin: 10px 0; }
                                .section-title { font-weight: bold; border-bottom: 1px solid #000; padding-bottom: 2px; margin-bottom: 5px; }
                                .item { margin: 3px 0; display: flex; justify-content: space-between; }
                                .subtotal { margin-top: 5px; border-top: 1px dashed #000; padding-top: 3px; font-weight: bold; }
                                .total { margin-top: 10px; border-top: 2px solid #000; padding-top: 5px; font-weight: bold; font-size: 14px; }
                                .footer { text-align: center; margin-top: 10px; font-size: 10px; }
                              </style>
                            </head>
                            <body>
                              <div class="header">
                                <h2>${businessInfo?.name || 'BILLIARD CLUB'}</h2>
                                <p>${translations.monthlyReport}</p>
                                <p>${(() => {
                                  const today = new Date()
                                  const day = today.getDate().toString().padStart(2, '0')
                                  const month = (today.getMonth() + 1).toString().padStart(2, '0')
                                  const year = today.getFullYear()
                                  return `${day}/${month}/${year}`
                                })()}</p>
                                ${businessInfo?.phone ? `<p>Tel: ${businessInfo.phone}</p>` : ''}
                                ${businessInfo?.address ? `<p>${translations.address}: ${businessInfo.address}</p>` : ''}
                              </div>

                              ${reportIncludes.games ? `
                                <div class="section">
                                  <div class="section-title">${translations.billiardGames} (${monthGames.length})</div>
                                  ${monthGames.length === 0 ? `<p style="text-align: center; color: #666;">${translations.noGamesCompletedThisMonth}</p>` :
                                    monthGames.map(game => `
                                      <div class="item">
                                        <span>${getGameTableName(game.tableNumber)} - ${Math.floor(game.duration / 60)}h ${game.duration % 60}m</span>
                                        <span>${formatCurrency(typeof game.cost === 'string' ? parseFloat(game.cost) : (game.cost || 0), currency)}</span>
                                      </div>
                                    `).join('') + `
                                      <div class="item subtotal">
                                        <span>${translations.gamesSubtotal}</span>
                                        <span>${formatCurrency(totalGamesCost, currency)}</span>
                                      </div>
                                    `
                                  }
                                </div>
                              ` : ''}

                              ${reportIncludes.orders ? `
                                <div class="section">
                                  <div class="section-title">${translations.barOrders} (${monthOrders.length})</div>
                                  ${monthOrders.length === 0 ? `<p style="text-align: center; color: #666;">${translations.noOrdersCompletedThisMonth}</p>` :
                                    monthOrders.sort((a, b) => {
                                      const tableA = getBarTableName(a.table_number)
                                      const tableB = getBarTableName(b.table_number)
                                      return tableA.localeCompare(tableB, undefined, { numeric: true, sensitivity: 'base' })
                                    }).map(order => `
                                      <div class="item">
                                        <span style="font-weight: bold;">${getBarTableName(order.table_number)}</span>
                                        <span>${formatCurrency(typeof order.total === 'string' ? parseFloat(order.total) : (order.total || 0), currency)}</span>
                                      </div>
                                      ${order.items?.map((item: any) => `
                                        <div class="item" style="margin-left: 10px; font-size: 10px;">
                                          <span>${item.quantity}x ${item.name}</span>
                                          <span>${formatCurrency(item.quantity * item.price, currency)}</span>
                                        </div>
                                      `).join('')}
                                    `).join('') + `
                                      <div class="item subtotal">
                                        <span>${translations.ordersSubtotal}</span>
                                        <span>${formatCurrency(totalOrdersCost, currency)}</span>
                                      </div>
                                    `
                                  }
                                </div>
                              ` : ''}

                              <div class="item total">
                                <span>${translations.monthlyTotal}</span>
                                <span>${formatCurrency(grandTotal, currency)}</span>
                              </div>

                              <div class="footer">
                                <p>${translations.reportGenerated} ${new Date().toLocaleString()}</p>
                                ${businessInfo?.vat_number ? `<p>${translations.vat}: ${businessInfo.vat_number}</p>` : ''}
                              </div>
                            </body>
                          </html>
                        `
                        const printWindow = window.open('', '_blank', 'width=300,height=400')
                        if (printWindow) {
                          printWindow.document.documentElement.innerHTML = reportHtml
                          printWindow.print()
                          printWindow.close()
                        }
                      }}
                      className="flex items-center gap-2"
                    >
                      <Printer className="h-4 w-4" />
                      {t('bar.printMonthlyReport')}
                    </Button>
                  </div>
                </>
              )
            })()}
          </div>
        </DialogContent>
      </Dialog>






    </div>
  )
}
