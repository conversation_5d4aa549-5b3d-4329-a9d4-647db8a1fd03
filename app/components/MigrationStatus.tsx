"use client"

import { useState, useEffect } from 'react'
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Button } from "@/components/ui/button"
import { CheckCircle, AlertTriangle, Loader2, Database } from 'lucide-react'

export function MigrationStatus() {
  const [status, setStatus] = useState<{
    checking: boolean
    hasColumn: boolean
    migrationNeeded: boolean
    error?: string
  }>({
    checking: true,
    hasColumn: false,
    migrationNeeded: false
  })

  const [isRunningMigration, setIsRunningMigration] = useState(false)

  const checkMigrationStatus = async () => {
    try {
      setStatus(prev => ({ ...prev, checking: true }))
      
      const response = await fetch('/api/auto-migrate', {
        credentials: 'include'
      })
      
      const result = await response.json()
      
      setStatus({
        checking: false,
        hasColumn: result.hasColumn || false,
        migrationNeeded: result.migrationNeeded || false,
        error: result.success ? undefined : result.message
      })
    } catch (error) {
      setStatus({
        checking: false,
        hasColumn: false,
        migrationNeeded: true,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  const runMigration = async () => {
    setIsRunningMigration(true)
    try {
      const response = await fetch('/api/auto-migrate', {
        method: 'POST',
        credentials: 'include'
      })
      
      const result = await response.json()
      
      if (result.success) {
        setStatus({
          checking: false,
          hasColumn: true,
          migrationNeeded: false
        })
      } else {
        setStatus(prev => ({
          ...prev,
          error: result.message
        }))
      }
    } catch (error) {
      setStatus(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Migration failed'
      }))
    } finally {
      setIsRunningMigration(false)
    }
  }

  useEffect(() => {
    checkMigrationStatus()
  }, [])

  // Don't show anything if database is ready
  if (status.hasColumn && !status.error) {
    return null
  }

  // Don't show during initial check
  if (status.checking) {
    return null
  }

  // Show migration needed alert
  if (status.migrationNeeded || status.error) {
    return (
      <Alert className="mb-4 border-orange-200 bg-orange-50">
        <AlertTriangle className="h-4 w-4 text-orange-600" />
        <AlertDescription className="flex items-center justify-between">
          <div>
            <strong className="text-orange-900">Database Migration Required</strong>
            <p className="text-orange-800 text-sm mt-1">
              Time limits won't persist across page refreshes until the database is updated.
              {status.error && (
                <span className="block mt-1 text-red-700">Error: {status.error}</span>
              )}
            </p>
          </div>
          <Button
            onClick={runMigration}
            disabled={isRunningMigration}
            size="sm"
            className="ml-4"
          >
            {isRunningMigration ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Migrating...
              </>
            ) : (
              <>
                <Database className="w-4 h-4 mr-2" />
                Fix Now
              </>
            )}
          </Button>
        </AlertDescription>
      </Alert>
    )
  }

  return null
}
