"use client"

import { useState, useEffect } from "react"
import { useSafeTranslation } from '../hooks/useSafeTranslation'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Plus, Edit, Trash2, Save, Search } from 'lucide-react'
import { toast } from "sonner"
// Define BarTable interface locally since we removed hardcoded data
interface BarTable {
  id: number
  number: number
  name: string
  isActive: boolean
  assignedUserId?: number
  assignedUsername?: string
}

// User interface for user assignment
interface User {
  id: number
  username: string
  displayName: string
  role: string
}

interface BarSettingsProps {
  onSettingsChange?: () => void
}

// Auto-Generate Unique 4-Digit Table Numbers (0001 format) for Bar Tables
const generateUniqueBarTableNumber = (existingNumbers: number[]): number => {
  console.log('🎲 Generating unique bar table number. Existing numbers:', existingNumbers)

  let attempts = 0
  const maxAttempts = 100

  while (attempts < maxAttempts) {
    // Generate random 4-digit number (1-9999)
    const candidate = Math.floor(Math.random() * 9999) + 1

    // Check if this number is already used (globally unique across all users)
    if (!existingNumbers.includes(candidate)) {
      console.log('✅ Generated unique bar table number:', candidate, 'formatted as:', candidate.toString().padStart(4, '0'))
      return candidate
    }

    attempts++
  }

  console.log('⚠️ Random generation failed after', maxAttempts, 'attempts, using fallback method')

  // Fallback: find next available number starting from 1
  let fallback = 1
  while (existingNumbers.includes(fallback)) {
    fallback++
  }

  console.log('✅ Fallback generated unique bar table number:', fallback, 'formatted as:', fallback.toString().padStart(4, '0'))
  return fallback
}

// Format number with leading zeros (0001, 0002, etc.)
const formatBarTableNumber = (num: number): string => {
  return num.toString().padStart(4, '0')
}

export function BarSettings({ onSettingsChange }: BarSettingsProps) {
  const { t } = useSafeTranslation()
  const [tables, setTables] = useState<BarTable[]>([])
  const [users, setUsers] = useState<User[]>([])
  const [isAddTableOpen, setIsAddTableOpen] = useState(false)
  const [editingTable, setEditingTable] = useState<BarTable | null>(null)
  const [loadingUsers, setLoadingUsers] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterByUser, setFilterByUser] = useState<string>('all')
  const [newTable, setNewTable] = useState<Partial<BarTable>>({
    number: 1, // Will be auto-generated
    name: '',
    isActive: true,
    assignedUserId: undefined,
    assignedUsername: undefined
  })
  const [isGenerating, setIsGenerating] = useState(false)
  const [bulkCreate, setBulkCreate] = useState(false)
  const [bulkCount, setBulkCount] = useState(10)
  const [bulkPrefix, setBulkPrefix] = useState('T')

  // Load tables and users data from database
  useEffect(() => {
    loadTablesData()
    loadUsers()
  }, [])

  // Auto-generate unique table number when tables load
  useEffect(() => {
    const generateAutoNumber = () => {
      // Get all existing table numbers (global uniqueness)
      const existingNumbers = tables.map((table: BarTable) => table.number)

      // Generate a unique number
      const newNumber = generateUniqueBarTableNumber(existingNumbers)

      // Update form with the new number
      setNewTable(prev => ({
        ...prev,
        number: newNumber,
        name: `Table ${formatBarTableNumber(newNumber)}`
      }))
    }

    // Only generate if we have tables loaded and don't have a number yet or it's the default
    if (tables.length >= 0 && newTable.number === 1) {
      generateAutoNumber()
    }
  }, [tables])

  const loadTablesData = async () => {
    try {
      const response = await fetch('/api/tables', {
        credentials: 'include'
      })

      if (response.ok) {
        const tablesData = await response.json()
        const mappedTables = tablesData.map((t: any) => ({
          id: t.id,
          number: t.number,
          name: t.name,
          isActive: t.is_active,
          assignedUserId: t.assigned_user_id,
          assignedUsername: t.assigned_username
        }))
        setTables(mappedTables)
        console.log('✅ Loaded bar tables from database')
      } else {
        console.error('❌ Failed to load bar tables from database')
        // No fallback - database is required
        setTables([])
      }
    } catch (error) {
      console.error('❌ Failed to load bar table settings:', error)
      // No fallback - database is required
      setTables([])
    }
  }

  const loadUsers = async () => {
    try {
      const response = await fetch('/api/users', {
        credentials: 'include'
      })
      if (response.ok) {
        const usersData = await response.json()
        setUsers(usersData)
      } else {
        console.error('Failed to load users')
      }
    } catch (error) {
      console.error('Error loading users:', error)
    } finally {
      setLoadingUsers(false)
    }
  }

  // Handle user selection for new table
  const handleNewTableUserChange = (userId: string) => {
    if (userId === "none") {
      setNewTable(prev => ({
        ...prev,
        assignedUserId: undefined,
        assignedUsername: undefined
      }))
    } else {
      const selectedUser = users.find(u => u.id.toString() === userId)
      if (selectedUser) {
        setNewTable(prev => ({
          ...prev,
          assignedUserId: selectedUser.id,
          assignedUsername: selectedUser.username
        }))
      }
    }
  }

  // Handle user selection for editing table
  const handleEditTableUserChange = (userId: string) => {
    if (!editingTable) return

    if (userId === "none") {
      setEditingTable(prev => prev ? ({
        ...prev,
        assignedUserId: undefined,
        assignedUsername: undefined
      }) : null)
    } else {
      const selectedUser = users.find(u => u.id.toString() === userId)
      if (selectedUser) {
        setEditingTable(prev => prev ? ({
          ...prev,
          assignedUserId: selectedUser.id,
          assignedUsername: selectedUser.username
        }) : null)
      }
    }
  }

  // Generate a new unique table number
  const generateNewBarTableNumber = async () => {
    setIsGenerating(true)

    try {
      // Get all existing table numbers (global uniqueness)
      const existingNumbers = tables.map((table: BarTable) => table.number)

      // Generate a new unique number
      const newNumber = generateUniqueBarTableNumber(existingNumbers)

      // Update form with the new number and auto-generated name
      setNewTable(prev => ({
        ...prev,
        number: newNumber,
        name: `Table ${formatBarTableNumber(newNumber)}`
      }))
    } catch (error) {
      console.error('Failed to generate new bar table number:', error)
    } finally {
      setIsGenerating(false)
    }
  }

  const handleAddTable = async () => {
    if (bulkCreate) {
      return handleBulkAddTables()
    }

    if (!newTable.name || !newTable.number) {
      toast.error(t('settings.fillRequiredFields'))
      return
    }

    // Check for duplicate table numbers GLOBALLY (no two tables can have the same number)
    if (tables.some(table => table.number === newTable.number)) {
      toast.error(t('bar.tableNumberAlreadyInUse', { number: newTable.number!.toString().padStart(4, '0') }))
      return
    }

    try {
      const response = await fetch('/api/tables', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          number: newTable.number,
          name: newTable.name,
          isActive: newTable.isActive,
          hourlyRate: 0, // Default hourly rate for bar tables
          assignedUserId: newTable.assignedUserId,
          assignedUsername: newTable.assignedUsername
        }),
      })

      if (response.ok) {
        const createdTable = await response.json()
        const mappedTable: BarTable = {
          id: createdTable.id,
          number: createdTable.number,
          name: createdTable.name,
          isActive: createdTable.is_active,
          assignedUserId: createdTable.assigned_user_id,
          assignedUsername: createdTable.assigned_username
        }

        setTables([...tables, mappedTable])

        // Generate a new unique number for the next table
        const existingNumbers = [...tables, mappedTable].map((table: BarTable) => table.number)
        const newNumber = generateUniqueBarTableNumber(existingNumbers)

        setNewTable({
          number: newNumber,
          name: `Table ${formatBarTableNumber(newNumber)}`,
          isActive: true,
          assignedUserId: undefined,
          assignedUsername: undefined
        })
        setIsAddTableOpen(false)
        toast.success(t('settings.tableAddedSuccessfully', { name: mappedTable.name }))
        onSettingsChange?.()
      } else {
        const errorData = await response.json()
        toast.error(`Failed to add table: ${errorData.error}`)
      }
    } catch (error) {
      console.error('Failed to add table:', error)
      toast.error(t('settings.failedToAddTable'))
    }
  }

  const handleBulkAddTables = async () => {
    if (bulkCount < 1 || bulkCount > 50) {
      toast.error(t('settings.bulkCountMustBeBetween1And50'))
      return
    }

    if (!bulkPrefix.trim()) {
      toast.error(t('settings.tablePrefixRequiredForBulkCreation'))
      return
    }

    try {
      const existingNumbers = tables.map((table: BarTable) => table.number)
      const tablesToCreate = []

      for (let i = 1; i <= bulkCount; i++) {
        const uniqueNumber = generateUniqueBarTableNumber([...existingNumbers, ...tablesToCreate.map(t => t.number)])
        tablesToCreate.push({
          number: uniqueNumber,
          name: `${bulkPrefix}${i}`,
          isActive: newTable.isActive,
          hourlyRate: 0,
          assignedUserId: newTable.assignedUserId,
          assignedUsername: newTable.assignedUsername
        })
      }

      // Create all tables
      const createdTables = []
      for (const tableData of tablesToCreate) {
        const response = await fetch('/api/tables', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify(tableData),
        })

        if (response.ok) {
          const createdTable = await response.json()
          createdTables.push({
            id: createdTable.id,
            number: createdTable.number,
            name: createdTable.name,
            isActive: createdTable.is_active,
            assignedUserId: createdTable.assigned_user_id,
            assignedUsername: createdTable.assigned_username
          })
        } else {
          const errorData = await response.json()
          console.error(`Failed to create table ${tableData.name}:`, errorData.error)
        }
      }

      if (createdTables.length > 0) {
        setTables([...tables, ...createdTables])

        // Reset form
        const existingNumbers = [...tables, ...createdTables].map((table: BarTable) => table.number)
        const newNumber = generateUniqueBarTableNumber(existingNumbers)
        setNewTable({
          number: newNumber,
          name: `Table ${formatBarTableNumber(newNumber)}`,
          isActive: true,
          assignedUserId: undefined,
          assignedUsername: undefined
        })

        setIsAddTableOpen(false)
        setBulkCreate(false)
        toast.success(t('bar.successfullyCreatedGameTables', { count: createdTables.length }))
        onSettingsChange?.()
      } else {
        toast.error(t('bar.failedToCreateAnyTables'))
      }
    } catch (error) {
      console.error('Failed to bulk create tables:', error)
      toast.error(t('bar.failedToCreateTables'))
    }
  }

  const handleEditTable = async (table: BarTable) => {
    // Validate required fields
    if (!table.name || !table.number) {
      toast.error(t('settings.fillRequiredFields'))
      return
    }

    // Check for duplicate table numbers GLOBALLY (excluding current table)
    if (tables.some(t =>
      t.id !== table.id && t.number === table.number
    )) {
      toast.error(t('bar.tableNumberAlreadyInUse', { number: table.number.toString().padStart(4, '0') }))
      return
    }

    try {
      const response = await fetch('/api/tables', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          id: table.id,
          number: table.number,
          name: table.name,
          isActive: table.isActive,
          hourlyRate: 0, // Default hourly rate for bar tables
          assignedUserId: table.assignedUserId,
          assignedUsername: table.assignedUsername
        }),
      })

      if (response.ok) {
        const responseTable = await response.json()
        const mappedTable: BarTable = {
          id: responseTable.id,
          number: responseTable.number,
          name: responseTable.name,
          isActive: responseTable.is_active,
          assignedUserId: responseTable.assigned_user_id,
          assignedUsername: responseTable.assigned_username
        }

        setTables(tables.map(t => t.id === table.id ? mappedTable : t))
        setEditingTable(null)
        toast.success(t('settings.tableUpdatedSuccessfully', { name: mappedTable.name }))
        onSettingsChange?.()
      } else {
        const errorData = await response.json()
        toast.error(`Failed to update table: ${errorData.error}`)
      }
    } catch (error) {
      console.error('Failed to update table:', error)
      toast.error(t('settings.failedToUpdateTable'))
    }
  }

  const handleDeleteTable = async (id: number) => {
    const tableToDelete = tables.find(t => t.id === id)
    if (!tableToDelete) return

    // Add confirmation dialog
    if (!confirm(t('bar.confirmDeleteTable', { name: tableToDelete.name }))) {
      return
    }

    try {
      const response = await fetch(`/api/tables?id=${id}`, {
        method: 'DELETE',
        credentials: 'include',
      })

      if (response.ok) {
        setTables(tables.filter(table => table.id !== id))
        toast.success(t('bar.tableDeletedSuccessfully', { name: tableToDelete.name }))
        onSettingsChange?.()
      } else {
        const errorData = await response.json()
        toast.error(t('bar.failedToDeleteTable', { error: errorData.error }))
      }
    } catch (error) {
      console.error('Failed to delete table:', error)
      toast.error(t('bar.failedToDeleteTableGeneric'))
    }
  }

  // Filter and sort tables
  const filteredAndSortedTables = tables
    .filter(table => {
      // Filter by search term (name or number)
      const matchesSearch = searchTerm === '' ||
        table.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        formatBarTableNumber(table.number).includes(searchTerm)

      // Filter by assigned user
      const matchesUser = filterByUser === 'all' ||
        (filterByUser === 'unassigned' && !table.assignedUserId) ||
        (filterByUser !== 'unassigned' && table.assignedUserId?.toString() === filterByUser)

      return matchesSearch && matchesUser
    })
    .sort((a, b) => {
      // Sort by name alphabetically
      return a.name.localeCompare(b.name)
    })

  return (
    <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg">
      <CardHeader className="pb-3">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
          <div className="flex items-center gap-2">
            <div className="p-2 bg-blue-600 rounded-lg">
              <Plus className="h-4 w-4 text-white" />
            </div>
            <div>
              <CardTitle className="text-base font-semibold text-gray-900 dark:text-white">
                {t('bar.barTables')} ({filteredAndSortedTables.length}{tables.length !== filteredAndSortedTables.length ? ` ${t('common.of')} ${tables.length}` : ''})
              </CardTitle>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {t('settings.active')}: {filteredAndSortedTables.filter(t => t.isActive).length}
              </p>
            </div>
          </div>
          <Dialog open={isAddTableOpen} onOpenChange={setIsAddTableOpen}>
            <DialogTrigger asChild>
              <Button size="sm" className="bg-blue-600 hover:bg-blue-700 text-white">
                <Plus className="w-4 h-4 mr-1" />
                {t('bar.addTable')}
              </Button>
            </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>{t('bar.addNewBarTable')}</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  {/* Bulk Creation Toggle */}
                  <div className="flex items-center space-x-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <Switch
                      id="bulkCreate"
                      checked={bulkCreate}
                      onCheckedChange={setBulkCreate}
                    />
                    <div>
                      <Label htmlFor="bulkCreate" className="text-sm font-medium">{t('settings.bulkCreateTables')}</Label>
                      <p className="text-xs text-gray-500">{t('settings.createMultipleTablesAtOnce')}</p>
                    </div>
                  </div>

                  {bulkCreate ? (
                    /* Bulk Creation Form */
                    <>
                      <div>
                        <Label htmlFor="bulkCount" className="text-sm font-medium">{t('settings.numberOfTables')}</Label>
                        <Input
                          id="bulkCount"
                          type="number"
                          min="1"
                          max="50"
                          value={bulkCount}
                          onChange={(e) => setBulkCount(parseInt(e.target.value) || 1)}
                          className="h-9"
                        />
                        <p className="text-xs text-gray-500 mt-1">{t('settings.create1To50TablesMax50')}</p>
                      </div>

                      <div>
                        <Label htmlFor="bulkPrefix" className="text-sm font-medium">{t('bar.tableNamePrefix')}</Label>
                        <Input
                          id="bulkPrefix"
                          value={bulkPrefix}
                          onChange={(e) => setBulkPrefix(e.target.value)}
                          placeholder="T"
                          className="h-9"
                        />
                        <p className="text-xs text-gray-500 mt-1">Will create: {bulkPrefix}1, {bulkPrefix}2, {bulkPrefix}3, ...</p>
                      </div>
                    </>
                  ) : (
                    /* Single Table Form */
                    <>
                      <div>
                        <Label htmlFor="number" className="text-sm font-medium">{t('bar.tableNumber')}</Label>
                        <div className="relative">
                          <Input
                            id="number"
                            type="text"
                            value={formatBarTableNumber(newTable.number || 1)}
                            readOnly
                            className="pr-8 bg-gray-50 font-mono text-center font-bold h-9"
                          />
                          <button
                            type="button"
                            onClick={generateNewBarTableNumber}
                            disabled={isGenerating}
                            className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-blue-600 disabled:opacity-50"
                            title={t('settings.generateNewNumber')}
                          >
                            {isGenerating ? '⏳' : '🔄'}
                          </button>
                        </div>
                        <p className="text-xs text-gray-500 mt-1">{t('settings.autoGeneratedUniqueNumber')}</p>
                      </div>

                      <div>
                        <Label htmlFor="tableName" className="text-sm font-medium">{t('bar.tableName')}</Label>
                        <Input
                          id="tableName"
                          value={newTable.name}
                          onChange={(e) => setNewTable(prev => ({ ...prev, name: e.target.value }))}
                          placeholder={t('bar.enterTableName')}
                          className="h-9"
                        />
                      </div>
                    </>
                  )}

                  <div>
                    <Label htmlFor="assignedUser" className="text-sm font-medium">{t('bar.assignToUser')}</Label>
                    <Select
                      value={newTable.assignedUserId?.toString() || "none"}
                      onValueChange={handleNewTableUserChange}
                      disabled={loadingUsers}
                    >
                      <SelectTrigger className="h-9">
                        <SelectValue placeholder={loadingUsers ? t('common.loading') : t('bar.selectUser')} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">{t('bar.sharedNoSpecificUser')}</SelectItem>
                        {users.map((user) => (
                          <SelectItem key={user.id} value={user.id.toString()}>
                            {user.displayName}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <p className="text-xs text-gray-500 mt-1">{t('bar.onlyAssignedUserCanSeeTable')}</p>
                  </div>

                  <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
                    <Switch
                      id="tableActive"
                      checked={newTable.isActive}
                      onCheckedChange={(checked) => setNewTable(prev => ({ ...prev, isActive: checked }))}
                    />
                    <Label htmlFor="tableActive" className="text-sm font-medium">{t('settings.active')}</Label>
                  </div>

                  <Button onClick={handleAddTable} className="w-full bg-blue-600 hover:bg-blue-700">
                    <Save className="w-4 h-4 mr-2" />
                    {bulkCreate ? t('bar.createTables', { count: bulkCount }) : t('bar.addTable')}
                  </Button>
                </div>
              </DialogContent>
            </Dialog>
        </div>

        {/* Search and Filter Controls */}
        <div className="flex flex-col sm:flex-row gap-3 mt-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder={t('bar.searchTablesByNameOrNumber')}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-9 h-9"
            />
          </div>
          <Select value={filterByUser} onValueChange={setFilterByUser}>
            <SelectTrigger className="w-full sm:w-48 h-9">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('bar.allUsers')}</SelectItem>
              <SelectItem value="unassigned">{t('bar.unassigned')}</SelectItem>
              {users.map((user) => (
                <SelectItem key={user.id} value={user.id.toString()}>
                  {user.displayName}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </CardHeader>

      <CardContent>
        {tables.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Plus className="h-8 w-8 mx-auto mb-2 text-gray-300" />
            <p className="text-sm font-medium mb-1">{t('bar.noBarTablesFound')}</p>
            <p className="text-xs">{t('bar.addFirstTableToGetStarted')}</p>
          </div>
        ) : filteredAndSortedTables.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Search className="h-8 w-8 mx-auto mb-2 text-gray-300" />
            <p className="text-sm font-medium mb-1">{t('settings.noTablesMatchSearch')}</p>
            <p className="text-xs">{t('settings.tryAdjustingSearch')}</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
            {filteredAndSortedTables.map(table => (
              <div key={table.id} className={`p-3 border rounded-lg transition-colors ${table.isActive ? 'border-green-200 bg-green-50/30' : 'border-gray-200 bg-gray-50'}`}>
                <div className="flex items-start justify-between mb-2">
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-sm text-gray-900 dark:text-white truncate">{table.name}</h4>
                    <p className="text-xs text-gray-500">#{formatBarTableNumber(table.number)}</p>
                  </div>
                  <Badge variant={table.isActive ? "default" : "secondary"} className="text-xs ml-2">
                    {table.isActive ? t('settings.active') : t('settings.inactive')}
                  </Badge>
                </div>

                <div className="text-xs text-gray-600 mb-3">
                  <span className="font-medium">{t('bar.assignedTo')}:</span>
                  <br />
                  <span className={table.assignedUsername ? "text-blue-600" : "text-gray-500"}>
                    {table.assignedUsername || t('settings.shared')}
                  </span>
                </div>

                <div className="flex gap-1">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setEditingTable(table)}
                    className="flex-1 h-7 text-xs"
                  >
                    <Edit className="w-3 h-3 mr-1" />
                    {t('common.edit')}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDeleteTable(table.id)}
                    className="h-7 px-2 text-xs text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="w-3 h-3" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>

      {/* Edit Table Dialog */}
      {editingTable && (
        <Dialog open={!!editingTable} onOpenChange={() => setEditingTable(null)}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{t('bar.editBarTable')}</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="editTableNumber" className="text-sm font-medium">{t('bar.tableNumber')}</Label>
                <Input
                  id="editTableNumber"
                  type="text"
                  value={formatBarTableNumber(editingTable.number)}
                  onChange={(e) => {
                    const num = parseInt(e.target.value.replace(/^0+/, '')) || 1
                    setEditingTable(prev => prev ? { ...prev, number: num } : null)
                  }}
                  className="font-mono text-center h-9"
                />
                <p className="text-xs text-gray-500 mt-1">{t('settings.mustBeGloballyUnique')}</p>
              </div>

              <div>
                <Label htmlFor="editTableName" className="text-sm font-medium">{t('bar.tableName')}</Label>
                <Input
                  id="editTableName"
                  value={editingTable.name}
                  onChange={(e) => setEditingTable(prev => prev ? { ...prev, name: e.target.value } : null)}
                  className="h-9"
                />
              </div>

              <div>
                <Label htmlFor="editAssignedUser" className="text-sm font-medium">{t('bar.assignToUser')}</Label>
                <Select
                  value={editingTable.assignedUserId?.toString() || "none"}
                  onValueChange={handleEditTableUserChange}
                  disabled={loadingUsers}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder={loadingUsers ? t('common.loading') : t('bar.selectUser')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">{t('bar.sharedNoSpecificUser')}</SelectItem>
                    {users.map((user) => (
                      <SelectItem key={user.id} value={user.id.toString()}>
                        {user.displayName}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <p className="text-xs text-gray-500 mt-1">{t('bar.onlyAssignedUserCanSeeTable')}</p>
              </div>

              <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
                <Switch
                  checked={editingTable.isActive}
                  onCheckedChange={(checked) => setEditingTable(prev => prev ? { ...prev, isActive: checked } : null)}
                />
                <Label className="text-sm font-medium">{t('settings.active')}</Label>
              </div>

              <Button onClick={() => handleEditTable(editingTable)} className="w-full bg-blue-600 hover:bg-blue-700">
                <Save className="w-4 h-4 mr-2" />
                {t('common.saveChanges')}
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </Card>
  )
}
