"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useSafeTranslation } from '../hooks/useSafeTranslation'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  SettingsIcon,
  Plus,
  Trash2,
  Edit,
  Save,
  Coffee,
  Users,
  Clock,
  Building,
  Search,
  Eye,
  EyeOff,
  DollarSign,
  CheckCircle,
  XCircle,
  LogIn,
  <PERSON>fresh<PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>old<PERSON><PERSON><PERSON>
} from "lucide-react"
import { Checkbox } from "@/components/ui/checkbox"
import { Permissions } from "./Permissions"
import { BarSettings } from "./BarSettings"
import { UserManagement } from "./UserManagement"
import { SoundUploader } from "./SoundUploader"
import { usePermissions } from "../hooks/usePermissions"
import { useDataRefresh } from "../contexts/DataRefreshContext"
import { useAuth } from "../contexts/AuthContext"
import { Shield } from "lucide-react"
import { GAME_TABLES } from "../constants/gameTables"
import { toast } from "sonner"
// import { CacheManager } from "./CacheManager"



interface GameTable {
  id: string
  number: number
  name: string
  isActive: boolean
  hourlyRate: number
  tableType: string
  assignedUserId?: number
  assignedUsername?: string
}

interface User {
  id: number
  username: string
  fullName: string
  role: string
  displayName: string
}

interface Product {
  id: string
  name: string
  price: number
  category: string
  isActive: boolean
  description?: string
}

interface GameSettings {
  defaultHourlyRate: number
  minimumGameTime: number
  autoEndAfterHours: number
  autoPrintReceipts: boolean
  taxRate: number
}

interface BusinessInfo {
  name: string
  address: string
  phone: string
  email: string
  website?: string
  taxId?: string
  vatNumber: string
}

interface CurrencySettings {
  currency: string
  symbol: string
  showDecimals: boolean
  taxIncluded: boolean
  taxEnabled: boolean
  taxRate: number
  qrCodeEnabled: boolean
  qrCodeUrl: string
}

interface Category {
  id: string
  key: string
  label: string
  icon: string
  isActive: boolean
  order: number
}

interface Waiter {
  id: string
  display_name: string
  username: string
  password: string
  enabled: boolean
}

interface LoginSettings {
  waiters_section_enabled: boolean
  waiters_section_title: string
  waiters: Waiter[]
}

export function Settings() {
  const { t } = useSafeTranslation()
  const { canAccessSettings } = usePermissions()
  const { refreshGames } = useDataRefresh()
  const { user } = useAuth()
  // Initialize activeTab from localStorage or default to "categories"
  const [activeTab, setActiveTab] = useState<"categories" | "barMenu" | "products" | "game" | "business" | "currency" | "permissions" | "login" | "users">(() => {
    if (typeof window !== 'undefined') {
      const savedTab = localStorage.getItem('bbm_settings_active_tab')
      const validTabs = ["categories", "barMenu", "products", "game", "business", "currency", "permissions", "login", "users"]
      if (savedTab && validTabs.includes(savedTab)) {
        return savedTab as "categories" | "barMenu" | "products" | "game" | "business" | "currency" | "permissions" | "login" | "users"
      }
    }
    return "categories"
  })

  const [gameTables, setGameTables] = useState<GameTable[]>([]) // Game tables
  const [products, setProducts] = useState<Product[]>([])
  const [gameSettings, setGameSettings] = useState<GameSettings>({
    defaultHourlyRate: 400,
    minimumGameTime: 15,
    autoEndAfterHours: 8,
    autoPrintReceipts: true,
    taxRate: 20,
  })
  const [businessInfo, setBusinessInfo] = useState<BusinessInfo>({
    name: t('settings.billiardClub'),
    address: t('settings.tiranAlbania'),
    phone: t('settings.phoneNumber'),
    email: t('settings.emailAddress'),
    website: t('settings.website'),
    taxId: t('settings.taxId'),
    vatNumber: t('settings.taxId'),
  })
  const [currencySettings, setCurrencySettings] = useState<CurrencySettings>({
    currency: t('settings.albanianLek'),
    symbol: "L",
    showDecimals: false,
    taxIncluded: false,
    taxEnabled: true,
    taxRate: 20,
    qrCodeEnabled: false,
    qrCodeUrl: "",
  })
  const [loginSettings, setLoginSettings] = useState<LoginSettings>({
    waiters_section_enabled: true,
    waiters_section_title: t('settings.waitersAccounts'),
    waiters: [
      {
        id: '1',
        display_name: t('settings.waiterOne'),
        username: t('settings.waiter1Placeholder'),
        password: t('settings.waiter1Placeholder'),
        enabled: true,
      },
      {
        id: '2',
        display_name: t('settings.waiterTwo'),
        username: t('settings.waiter2Placeholder'),
        password: t('settings.waiter2Placeholder'),
        enabled: true,
      }
    ]
  })
  const [loginSettingsSource, setLoginSettingsSource] = useState<'database' | 'localStorage' | 'default'>('default')

  // Password visibility state - now dynamic for all waiters
  const [passwordVisibility, setPasswordVisibility] = useState<Record<string, boolean>>({})

  // Waiter management functions
  const addWaiter = () => {
    const newId = (Math.max(...loginSettings.waiters.map(w => parseInt(w.id)), 0) + 1).toString()
    const newWaiter: Waiter = {
      id: newId,
      display_name: t('settings.waiterNew', { number: newId }),
      username: `waiter${newId}`,
      password: `waiter${newId}`,
      enabled: true,
    }
    setLoginSettings(prev => ({
      ...prev,
      waiters: [...prev.waiters, newWaiter]
    }))
  }

  const removeWaiter = (waiterId: string) => {
    console.log('🗑️ removeWaiter called with ID:', waiterId)
    console.log('📊 Current waiters count:', loginSettings.waiters.length)
    console.log('📋 Current waiters:', loginSettings.waiters)

    if (loginSettings.waiters.length <= 1) {
      console.log('⚠️ Cannot remove last waiter')
      alert(t('settings.cannotRemoveLastWaiter'))
      return
    }

    const waiter = loginSettings.waiters.find(w => w.id === waiterId)
    console.log('👤 Found waiter to remove:', waiter)

    const confirmMessage = t('settings.confirmRemoveWaiter', { name: waiter?.display_name || 'Unknown' })
    console.log('💬 Confirmation message:', confirmMessage)

    const confirmed = window.confirm(confirmMessage)
    console.log('✅ User confirmed removal:', confirmed)

    if (confirmed) {
      console.log('🔄 Removing waiter from state...')
      setLoginSettings(prev => {
        const newWaiters = prev.waiters.filter(w => w.id !== waiterId)
        console.log('📝 New waiters list:', newWaiters)
        return {
          ...prev,
          waiters: newWaiters
        }
      })

      // Remove password visibility state for this waiter
      setPasswordVisibility(prev => {
        const newState = { ...prev }
        delete newState[waiterId]
        console.log('🔐 Updated password visibility state:', newState)
        return newState
      })

      console.log('✅ Waiter removal completed')
    }
  }

  const updateWaiter = (waiterId: string, updates: Partial<Waiter>) => {
    setLoginSettings(prev => ({
      ...prev,
      waiters: prev.waiters.map(w =>
        w.id === waiterId ? { ...w, ...updates } : w
      )
    }))
  }

  const togglePasswordVisibility = (waiterId: string) => {
    setPasswordVisibility(prev => ({
      ...prev,
      [waiterId]: !prev[waiterId]
    }))
  }

  const [categories, setCategories] = useState<Category[]>([])
  const [editingCategory, setEditingCategory] = useState<Category | null>(null)
  const [isAddCategoryOpen, setIsAddCategoryOpen] = useState(false)

  const [editingGameTable, setEditingGameTable] = useState<GameTable | null>(null)
  const [editingProduct, setEditingProduct] = useState<Product | null>(null)
  const [isAddGameTableOpen, setIsAddGameTableOpen] = useState(false)
  const [isAddProductOpen, setIsAddProductOpen] = useState(false)
  const [isUserManagementOpen, setIsUserManagementOpen] = useState(false)
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false)

  // Enhanced game table management state
  const [selectedGameTables, setSelectedGameTables] = useState<string[]>([])
  const [gameTableSearchTerm, setGameTableSearchTerm] = useState("")
  const [gameTableFilterStatus, setGameTableFilterStatus] = useState<"all" | "active" | "inactive">("all")

  // Enhanced product management state
  const [selectedProducts, setSelectedProducts] = useState<string[]>([])
  const [productSearchTerm, setProductSearchTerm] = useState("")
  const [productFilterCategory, setProductFilterCategory] = useState<string>("all")
  const [productFilterStatus, setProductFilterStatus] = useState<"all" | "active" | "inactive">("all")

  // Load settings from API
  useEffect(() => {
    const loadSettings = async () => {
      try {
        // Load business info
        const businessInfoResponse = await fetch('/api/business-info')
        if (businessInfoResponse.ok) {
          const businessData = await businessInfoResponse.json()
          if (businessData) {
            setBusinessInfo({
              name: businessData.name || '',
              address: businessData.address || '',
              phone: businessData.phone || '',
              email: businessData.email || '',
              vatNumber: businessData.vat_number || ''
            })
          }
        }



        // Load game tables from database, seed with hardcoded data if empty
        const gameTablesResponse = await fetch('/api/gametables')
        if (gameTablesResponse.ok) {
          const gameTablesData = await gameTablesResponse.json()

          // If no tables in database, seed with hardcoded data
          if (gameTablesData.length === 0) {
            console.log("No game tables found in database, seeding with hardcoded data...")

            // Insert hardcoded tables into database
            for (const table of GAME_TABLES) {
              try {
                await fetch('/api/gametables', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                  credentials: 'include', // Use cookies for authentication
                  body: JSON.stringify({
                    number: table.number,
                    name: table.name,
                    is_active: table.isActive,
                    hourly_rate: table.hourlyRate,
                    table_type: table.tableType
                  }),
                })
              } catch (error) {
                console.error(`Failed to seed table ${table.name}:`, error)
              }
            }

            // Reload tables after seeding
            const reloadResponse = await fetch('/api/gametables')
            if (reloadResponse.ok) {
              const reloadedData = await reloadResponse.json()
              setGameTables(reloadedData.map((t: any) => ({
                id: t.id.toString(),
                number: t.number,
                name: t.name,
                isActive: t.is_active,
                hourlyRate: t.hourly_rate,
                tableType: t.table_type,
                assignedUserId: t.assigned_user_id,
                assignedUsername: t.assigned_username,
                customSoundUrl: t.custom_sound_url
              })))
            }
          } else {
            // Use existing database data
            setGameTables(gameTablesData.map((t: any) => ({
              id: t.id.toString(),
              number: t.number,
              name: t.name,
              isActive: t.is_active,
              hourlyRate: t.hourly_rate,
              tableType: t.table_type,
              assignedUserId: t.assigned_user_id,
              assignedUsername: t.assigned_username,
              customSoundUrl: t.custom_sound_url
            })))
          }
        }

        // Load products
        const productsResponse = await fetch('/api/menu-items')
        if (productsResponse.ok) {
          const productsData = await productsResponse.json()
          setProducts(productsData.map((p: any) => ({
            id: p.id.toString(),
            name: p.name,
            price: p.price,
            category: p.category,
            isActive: p.available
          })))
        }

        // Load game settings from API
        const gameSettingsResponse = await fetch('/api/game-settings')
        if (gameSettingsResponse.ok) {
          const gameSettingsData = await gameSettingsResponse.json()
          setGameSettings(gameSettingsData)
        }

        // Load categories from database
        const categoriesResponse = await fetch('/api/categories')
        if (categoriesResponse.ok) {
          const categoriesData = await categoriesResponse.json()
          setCategories(categoriesData.map((c: any) => ({
            id: c.id.toString(),
            key: c.key,
            label: c.name,
            icon: c.icon,
            isActive: c.is_active,
            order: c.display_order
          })))
        }

        // Load currency settings
        const currencyResponse = await fetch('/api/currency-settings')
        if (currencyResponse.ok) {
          const currencyData = await currencyResponse.json()
          if (currencyData) {
            setCurrencySettings({
              currency: currencyData.currency || t('settings.albanianLek'),
              symbol: currencyData.symbol || 'Leke',
              showDecimals: currencyData.show_decimals ?? false,
              taxIncluded: currencyData.tax_included ?? false,
              taxEnabled: currencyData.tax_enabled ?? true,
              taxRate: currencyData.tax_rate || 20,
              qrCodeEnabled: currencyData.qr_code_enabled ?? false,
              qrCodeUrl: currencyData.qr_code_url || '',
            })
          }
        }

        // Load login settings
        try {
          const loginResponse = await fetch('/api/login-settings', {
            credentials: 'include' // Include cookies for authentication
          })
          if (loginResponse.ok) {
            const loginData = await loginResponse.json()
            if (loginData) {
              // Handle both old and new format
              if (loginData.waiters && Array.isArray(loginData.waiters)) {
                // New format with dynamic waiters
                setLoginSettings({
                  waiters_section_enabled: loginData.waiters_section_enabled ?? true,
                  waiters_section_title: loginData.waiters_section_title || t('settings.waitersAccounts'),
                  waiters: loginData.waiters
                })
              } else {
                // Legacy format - convert to new format
                const waiters: Waiter[] = []
                if (loginData.waiter1_enabled !== false) {
                  waiters.push({
                    id: '1',
                    display_name: loginData.waiter1_display_name || t('settings.waiterOne'),
                    username: loginData.waiter1_username || t('settings.waiter1Placeholder'),
                    password: loginData.waiter1_password || t('settings.waiter1Placeholder'),
                    enabled: loginData.waiter1_enabled ?? true,
                  })
                }
                if (loginData.waiter2_enabled !== false) {
                  waiters.push({
                    id: '2',
                    display_name: loginData.waiter2_display_name || t('settings.waiterTwo'),
                    username: loginData.waiter2_username || t('settings.waiter2Placeholder'),
                    password: loginData.waiter2_password || t('settings.waiter2Placeholder'),
                    enabled: loginData.waiter2_enabled ?? true,
                  })
                }
                setLoginSettings({
                  waiters_section_enabled: loginData.waiters_section_enabled ?? true,
                  waiters_section_title: loginData.waiters_section_title || t('settings.waitersAccounts'),
                  waiters
                })
              }
              setLoginSettingsSource('database')
              console.log('✅ Login settings loaded from database')
            }
          } else {
            throw new Error('Database request failed')
          }
        } catch (error) {
          console.warn('❌ Failed to load login settings from database, trying localStorage:', error)

          // Fallback to localStorage
          try {
            const backupSettings = localStorage.getItem('login_settings_backup')
            if (backupSettings) {
              const parsedSettings = JSON.parse(backupSettings)
              // Convert legacy format if needed
              if (parsedSettings.waiter1_display_name && !parsedSettings.waiters) {
                const waiters: Waiter[] = []
                if (parsedSettings.waiter1_enabled !== false) {
                  waiters.push({
                    id: '1',
                    display_name: parsedSettings.waiter1_display_name || t('settings.waiterOne'),
                    username: parsedSettings.waiter1_username || t('settings.waiter1Placeholder'),
                    password: parsedSettings.waiter1_password || t('settings.waiter1Placeholder'),
                    enabled: parsedSettings.waiter1_enabled ?? true,
                  })
                }
                if (parsedSettings.waiter2_enabled !== false) {
                  waiters.push({
                    id: '2',
                    display_name: parsedSettings.waiter2_display_name || t('settings.waiterTwo'),
                    username: parsedSettings.waiter2_username || t('settings.waiter2Placeholder'),
                    password: parsedSettings.waiter2_password || t('settings.waiter2Placeholder'),
                    enabled: parsedSettings.waiter2_enabled ?? true,
                  })
                }
                setLoginSettings({
                  waiters_section_enabled: parsedSettings.waiters_section_enabled ?? true,
                  waiters_section_title: parsedSettings.waiters_section_title || t('settings.waitersAccounts'),
                  waiters
                })
              } else {
                setLoginSettings(parsedSettings)
              }
              setLoginSettingsSource('localStorage')
              console.log('✅ Login settings loaded from localStorage backup')
            }
          } catch (localError) {
            console.warn('❌ Failed to load from localStorage, using defaults:', localError)
            setLoginSettingsSource('default')
          }
        }
      } catch (error) {
        console.error("Failed to load settings:", error)
      }
    }

    loadSettings()
  }, [])

  // Save activeTab to localStorage whenever it changes
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('bbm_settings_active_tab', activeTab)
    }
  }, [activeTab])

  // Function to load game tables from database
  const loadGameTables = async () => {
    try {
      const gameTablesResponse = await fetch('/api/gametables')
      if (gameTablesResponse.ok) {
        const gameTablesData = await gameTablesResponse.json()
        setGameTables(gameTablesData.map((t: any) => ({
          id: t.id.toString(),
          number: t.number,
          name: t.name,
          isActive: t.is_active,
          hourlyRate: t.hourly_rate,
          tableType: t.table_type,
          assignedUserId: t.assigned_user_id,
          assignedUsername: t.assigned_username,
          customSoundUrl: t.custom_sound_url
        })))
        console.log('✅ Game tables reloaded successfully')
      }
    } catch (error) {
      console.error('Failed to load game tables:', error)
    }
  }



  const filteredGameTables = gameTables.filter((table) => {
    const matchesSearch = table.name.toLowerCase().includes(gameTableSearchTerm.toLowerCase()) ||
                         table.number.toString().includes(gameTableSearchTerm)
    const matchesStatus = gameTableFilterStatus === "all" ||
                         (gameTableFilterStatus === "active" && table.isActive) ||
                         (gameTableFilterStatus === "inactive" && !table.isActive)
    return matchesSearch && matchesStatus
  })

  const filteredProducts = products.filter((product) => {
    const matchesSearch = product.name.toLowerCase().includes(productSearchTerm.toLowerCase())
    const matchesCategory = productFilterCategory === "all" || product.category === productFilterCategory
    const matchesStatus = productFilterStatus === "all" ||
                         (productFilterStatus === "active" && product.isActive) ||
                         (productFilterStatus === "inactive" && !product.isActive)
    return matchesSearch && matchesCategory && matchesStatus
  })



  // Bulk operations for game tables
  const toggleAllGameTables = () => {
    if (selectedGameTables.length === filteredGameTables.length) {
      setSelectedGameTables([])
    } else {
      setSelectedGameTables(filteredGameTables.map(t => t.id))
    }
  }

  const bulkUpdateGameTableStatus = async (isActive: boolean) => {
    try {
      const promises = selectedGameTables.map(tableId => {
        const table = gameTables.find(t => t.id === tableId)
        if (table) {
          return updateGameTable(tableId, { ...table, isActive })
        }
        return Promise.resolve()
      })
      await Promise.all(promises)
      setSelectedGameTables([])
    } catch (error) {
      console.error("Failed to bulk update game tables:", error)
    }
  }

  const bulkDeleteGameTables = async () => {
    if (selectedGameTables.length === 0) return

    const tableNames = selectedGameTables.map(id => {
      const table = gameTables.find(t => t.id === id)
      return table ? `${table.name} (#${table.number})` : t('bar.unknown')
    }).join(', ')

    const confirmed = window.confirm(
      t('settings.confirmDeleteGameTables', { count: selectedGameTables.length, tables: tableNames })
    )

    if (!confirmed) return

    try {
      const promises = selectedGameTables.map(async (tableId) => {
        const response = await fetch(`/api/gametables?id=${tableId}`, {
          method: 'DELETE',
          credentials: 'include', // Use cookies for authentication
        })
        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(t('settings.failedToDeleteGameTable', { tableId, error: errorData.error }))
        }
        return tableId
      })

      const deletedIds = await Promise.all(promises)
      setGameTables(gameTables.filter(table => !deletedIds.includes(table.id)))
      setSelectedGameTables([])

      // Trigger data refresh to update Games window
      refreshGames()
    } catch (error) {
      console.error("Failed to bulk delete game tables:", error)
      alert(t('settings.someTablesCouldNotBeDeleted'))
    }
  }

  // Function to reset/reseed game tables with updated names
  const reseedGameTables = async () => {
    const confirmed = window.confirm(
      `This will reset all game tables to default configuration and update their names.\n\nAny custom table settings will be lost. Continue?`
    )

    if (!confirmed) return

    try {
      // First, delete all existing tables
      const existingTables = await fetch('/api/gametables')
      if (existingTables.ok) {
        const tables = await existingTables.json()
        for (const table of tables) {
          await fetch(`/api/gametables?id=${table.id}`, {
            method: 'DELETE',
            credentials: 'include',
          })
        }
      }

      // Then, insert the updated hardcoded tables
      for (const table of GAME_TABLES) {
        try {
          await fetch('/api/gametables', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            credentials: 'include',
            body: JSON.stringify({
              number: table.number,
              name: table.name,
              is_active: table.isActive,
              hourly_rate: table.hourlyRate,
              table_type: table.tableType
            }),
          })
        } catch (error) {
          console.error(`Failed to reseed table ${table.name}:`, error)
        }
      }

      // Reload the tables
      await loadGameTables()

      // Trigger data refresh to update Games window
      refreshGames()

      alert('Game tables have been reset to default configuration!')
    } catch (error) {
      console.error('Failed to reseed game tables:', error)
      alert('Failed to reset game tables. Please try again.')
    }
  }

  // Function to fix existing table names to match consistent naming
  const fixTableNames = async () => {
    const confirmed = window.confirm(
      `This will update all existing table names to use consistent "Table X" format.\n\nFor example: "T1" → "Table 1", "T2" → "Table 2", etc.\n\nContinue?`
    )

    if (!confirmed) return

    try {
      // Get current tables
      const response = await fetch('/api/gametables')
      if (!response.ok) throw new Error('Failed to fetch tables')

      const tables = await response.json()
      let updatedCount = 0

      // Update each table's name to consistent format
      for (const table of tables) {
        const newName = `Table ${table.number}`

        // Only update if the name is different
        if (table.name !== newName) {
          try {
            const updateResponse = await fetch(`/api/gametables/${table.number}`, {
              method: 'PUT',
              headers: {
                'Content-Type': 'application/json',
              },
              credentials: 'include',
              body: JSON.stringify({
                name: newName,
                isActive: table.is_active,
                hourlyRate: table.hourly_rate,
                tableType: table.table_type
              }),
            })

            if (updateResponse.ok) {
              updatedCount++
              console.log(`✅ Updated "${table.name}" → "${newName}"`)
            } else {
              console.error(`❌ Failed to update table ${table.number}`)
            }
          } catch (error) {
            console.error(`❌ Error updating table ${table.number}:`, error)
          }
        }
      }

      // Reload the tables and refresh games
      await loadGameTables()
      refreshGames()

      alert(t('settings.successfullyUpdatedTableNames', { count: updatedCount }))
    } catch (error) {
      console.error('Failed to fix table names:', error)
      alert(t('settings.failedToFixTableNames'))
    }
  }

  // Bulk operations for products
  const toggleAllProducts = () => {
    if (selectedProducts.length === filteredProducts.length) {
      setSelectedProducts([])
    } else {
      setSelectedProducts(filteredProducts.map(p => p.id))
    }
  }

  const bulkUpdateProductStatus = async (isActive: boolean) => {
    try {
      const promises = selectedProducts.map(productId => {
        const product = products.find(p => p.id === productId)
        if (product) {
          return updateProduct(productId, { ...product, isActive })
        }
        return Promise.resolve()
      })
      await Promise.all(promises)
      setSelectedProducts([])
    } catch (error) {
      console.error("Failed to bulk update products:", error)
    }
  }

  const bulkDeleteProducts = async () => {
    if (selectedProducts.length === 0) return

    const productNames = selectedProducts.map(id => {
      const product = products.find(p => p.id === id)
      return product ? product.name : t('bar.unknown')
    }).join(', ')

    const confirmed = window.confirm(
      t('settings.confirmDeleteProducts', {
        count: selectedProducts.length,
        products: productNames
      })
    )

    if (!confirmed) return

    try {
      // Delete products from API
      const promises = selectedProducts.map(async (productId) => {
        const response = await fetch(`/api/menu-items?id=${productId}`, {
          method: 'DELETE',
          credentials: 'include', // Use cookies for authentication
        })
        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(t('settings.failedToDeleteProductWithError', { productId, error: errorData.error }))
        }
        return productId
      })

      const deletedIds = await Promise.all(promises)

      // Update local state by filtering out deleted products
      setProducts(products.filter(product => !deletedIds.includes(product.id)))
      setSelectedProducts([])
    } catch (error) {
      console.error("Failed to bulk delete products:", error)
      alert(t('settings.someProductsCouldNotBeDeleted'))
    }
  }

  // Save game settings
  const saveGameSettings = async (updateExistingTables: boolean = false) => {
    try {
      const response = await fetch('/api/game-settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Use cookies for authentication
        body: JSON.stringify({
          ...gameSettings,
          updateExistingTables
        }),
      })

      if (!response.ok) {
        throw new Error(t('settings.failedToSaveGameSettings'))
      }

      // If we updated existing tables, refresh the game tables list
      if (updateExistingTables) {
        const gameTablesResponse = await fetch('/api/gametables')
        if (gameTablesResponse.ok) {
          const gameTablesData = await gameTablesResponse.json()
          setGameTables(gameTablesData.map((t: any) => ({
            id: t.id.toString(),
            number: t.number,
            name: t.name,
            isActive: t.is_active,
            hourlyRate: t.hourly_rate,
            tableType: t.table_type,
            assignedUserId: t.assigned_user_id,
            assignedUsername: t.assigned_username,
            customSoundUrl: t.custom_sound_url
          })))
        }
      }

      // Trigger data refresh to update all components
      refreshGames()

      // Show success message
      alert(t('settings.gameSettingsSaved'))
    } catch (error) {
      console.error('Failed to save game settings:', error)
      alert(t('settings.gameSettingsError'))
    }
  }

  // Save business info
  const saveBusinessInfo = async () => {
    try {
      const response = await fetch('/api/business-info', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Use cookies for authentication
        body: JSON.stringify({
          name: businessInfo.name,
          address: businessInfo.address,
          phone: businessInfo.phone,
          email: businessInfo.email,
          vat_number: businessInfo.vatNumber
        }),
      })

      if (!response.ok) {
        throw new Error(t('settings.failedToSaveBusinessInfo'))
      }

      // Show success message
      alert(t('settings.businessInfoSaved'))
    } catch (error) {
      console.error('Failed to save business info:', error)
      alert(t('settings.businessInfoError'))
    }
  }

  // Update business info handler
  const handleBusinessInfoChange = (field: keyof typeof businessInfo, value: string) => {
    setBusinessInfo(prev => ({
      ...prev,
      [field]: value
    }))
  }

  // Save currency settings
  const saveCurrencySettings = async () => {
    try {
      const response = await fetch('/api/currency-settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Use cookies for authentication
        body: JSON.stringify({
          currency: currencySettings.currency,
          symbol: currencySettings.symbol,
          showDecimals: currencySettings.showDecimals,
          taxIncluded: currencySettings.taxIncluded,
          taxEnabled: currencySettings.taxEnabled,
          taxRate: currencySettings.taxRate,
          qrCodeEnabled: currencySettings.qrCodeEnabled,
          qrCodeUrl: currencySettings.qrCodeUrl,
        }),
      })

      if (!response.ok) {
        throw new Error(t('settings.failedToSaveCurrencySettings'))
      }

      // Show success message
      alert(t('settings.currencySettingsSaved'))
    } catch (error) {
      console.error('Failed to save currency settings:', error)
      alert(t('settings.currencySettingsError'))
    }
  }



  // Game Table management
  const addGameTable = async (tableData: Omit<GameTable, "id"> | Omit<GameTable, "id">[]) => {
    // Handle bulk creation
    if (Array.isArray(tableData)) {
      return addBulkGameTables(tableData)
    }

    // Handle single table creation
    // Check for duplicate table numbers GLOBALLY (no two tables can have the same number)
    if (gameTables.some(table => table.number === tableData.number)) {
      toast.error(`Table number ${tableData.number.toString().padStart(4, '0')} is already in use. Each table must have a unique number globally.`)
      return
    }

    try {
      const response = await fetch('/api/gametables', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Use cookies for authentication
        body: JSON.stringify({
          number: tableData.number,
          name: tableData.name,
          isActive: tableData.isActive,
          hourlyRate: tableData.hourlyRate,
          tableType: tableData.tableType,
          assignedUserId: tableData.assignedUserId,
          assignedUsername: tableData.assignedUsername,
          customSoundUrl: tableData.customSoundUrl
        }),
      })

      if (response.ok) {
        const newTable = await response.json()
        setGameTables([...gameTables, {
          id: newTable.id.toString(),
          number: newTable.number,
          name: newTable.name,
          isActive: newTable.is_active,
          hourlyRate: newTable.hourly_rate,
          tableType: newTable.table_type,
          assignedUserId: newTable.assigned_user_id,
          assignedUsername: newTable.assigned_username,
          customSoundUrl: newTable.custom_sound_url
        }])
        setIsAddGameTableOpen(false)

        // Trigger data refresh to update Games window
        refreshGames()

        // Show success message
        toast.success(t('settings.tableAddedSuccessfully', { name: newTable.name }))
      } else {
        const errorData = await response.json()
        console.error("Failed to add game table:", errorData.error)
        toast.error(t('settings.failedToAddGameTableWithError', { error: errorData.error }))
      }
    } catch (error) {
      console.error("Failed to add game table:", error)
      toast.error(t('settings.failedToAddTable'))
    }
  }

  const addBulkGameTables = async (tablesData: Omit<GameTable, "id">[]) => {
    try {
      const createdTables = []

      for (const tableData of tablesData) {
        // Check for duplicate table numbers
        const existingNumbers = [...gameTables, ...createdTables].map(t => t.number)
        if (existingNumbers.includes(tableData.number)) {
          console.warn(`Skipping table ${tableData.name} - number ${tableData.number} already exists`)
          continue
        }

        const response = await fetch('/api/gametables', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({
            number: tableData.number,
            name: tableData.name,
            isActive: tableData.isActive,
            hourlyRate: tableData.hourlyRate,
            tableType: tableData.tableType,
            assignedUserId: tableData.assignedUserId,
            assignedUsername: tableData.assignedUsername,
            customSoundUrl: tableData.customSoundUrl
          }),
        })

        if (response.ok) {
          const newTable = await response.json()
          createdTables.push({
            id: newTable.id.toString(),
            number: newTable.number,
            name: newTable.name,
            isActive: newTable.is_active,
            hourlyRate: newTable.hourly_rate,
            tableType: newTable.table_type,
            assignedUserId: newTable.assigned_user_id,
            assignedUsername: newTable.assigned_username,
            customSoundUrl: newTable.custom_sound_url
          })
        } else {
          const errorData = await response.json()
          console.error(`Failed to create table ${tableData.name}:`, errorData.error)
        }
      }

      if (createdTables.length > 0) {
        setGameTables([...gameTables, ...createdTables])
        setIsAddGameTableOpen(false)
        refreshGames()
        toast.success(t('settings.successfullyCreatedGameTables', { count: createdTables.length }))
      } else {
        toast.error(t('settings.failedToCreateAnyTables'))
      }
    } catch (error) {
      console.error('Failed to bulk create game tables:', error)
      toast.error(t('settings.failedToCreateTables'))
    }
  }

  const updateGameTable = async (id: string, updates: Partial<GameTable>) => {
    try {
      // Find the table to get the table number for the API call
      const table = gameTables.find(t => t.id === id)
      if (!table) {
        console.error("Table not found for update")
        return
      }

      // Check for duplicate table numbers GLOBALLY (excluding current table)
      if (updates.number && gameTables.some(t =>
        t.id !== id && t.number === updates.number
      )) {
        toast.error(`Table number ${updates.number.toString().padStart(4, '0')} is already in use. Each table must have a unique number globally.`)
        return
      }

      // Use the actual table ID instead of table number for user-specific updates
      const response = await fetch(`/api/gametables/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Use cookies for authentication
        body: JSON.stringify({
          name: updates.name || table.name,
          isActive: updates.isActive !== undefined ? updates.isActive : table.isActive,
          hourlyRate: updates.hourlyRate || table.hourlyRate,
          tableType: updates.tableType || table.tableType,
          assignedUserId: updates.assignedUserId !== undefined ? updates.assignedUserId : table.assignedUserId,
          assignedUsername: updates.assignedUsername !== undefined ? updates.assignedUsername : table.assignedUsername,
          customSoundUrl: updates.customSoundUrl !== undefined ? updates.customSoundUrl : table.customSoundUrl
        }),
      })

      if (response.ok) {
        const updatedTable = await response.json()
        setGameTables(gameTables.map((table) =>
          table.id === id ? {
            id: updatedTable.id.toString(),
            number: updatedTable.number,
            name: updatedTable.name,
            isActive: updatedTable.is_active,
            hourlyRate: updatedTable.hourly_rate,
            tableType: updatedTable.table_type,
            assignedUserId: updatedTable.assigned_user_id,
            assignedUsername: updatedTable.assigned_username,
            customSoundUrl: updatedTable.custom_sound_url
          } : table
        ))
        setEditingGameTable(null)

        // Trigger data refresh to update Games window
        refreshGames()

        // Also refresh the local game tables list to ensure UI consistency
        await loadGameTables()

        // Show success message
        alert(t('settings.tableUpdatedSuccessfully', { name: updates.name || table.name }))
      } else {
        const errorData = await response.json()
        console.error("Failed to update game table:", errorData.error)
        alert(t('settings.failedToUpdateGameTableWithError', { error: errorData.error }))
      }
    } catch (error) {
      console.error("Failed to update game table:", error)
    }
  }

  const deleteGameTable = async (id: string) => {
    const table = gameTables.find(t => t.id === id)
    if (!table) return

    // Confirm deletion
    const confirmed = window.confirm(
      t('settings.confirmDeleteGameTable', { name: table.name, number: table.number })
    )

    if (!confirmed) return

    try {
      const response = await fetch(`/api/gametables?id=${id}`, {
        method: 'DELETE',
        credentials: 'include', // Use cookies for authentication
      })

      if (response.ok) {
        setGameTables(gameTables.filter((table) => table.id !== id))
        // Also remove from selected tables if it was selected
        setSelectedGameTables(selectedGameTables.filter(selectedId => selectedId !== id))

        // Trigger data refresh to update Games window
        refreshGames()
      } else {
        const errorData = await response.json()
        console.error("Failed to delete game table:", errorData.error)
        alert(t('settings.failedToDeleteGameTableWithError', { error: errorData.error }))
      }
    } catch (error) {
      console.error("Failed to delete game table:", error)
      alert(t('settings.failedToDeleteTable'))
    }
  }

  // Product management
  const addProduct = async (productData: Omit<Product, "id">) => {
    try {
      const response = await fetch('/api/menu-items', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: productData.name,
          price: productData.price,
          category: productData.category,
          available: productData.isActive
        }),
      })

      if (response.ok) {
        const newProduct = await response.json()
        setProducts([...products, {
          id: newProduct.id.toString(),
          name: newProduct.name,
          price: newProduct.price,
          category: newProduct.category,
          isActive: newProduct.available
        }])
        setIsAddProductOpen(false)
      }
    } catch (error) {
      console.error("Failed to add product:", error)
    }
  }

  const updateProduct = async (id: string, updates: Partial<Product>) => {
    try {
      const response = await fetch('/api/menu-items', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id,
          name: updates.name,
          price: updates.price,
          category: updates.category,
          available: updates.isActive
        }),
      })

      if (response.ok) {
        const updatedProduct = await response.json()
        setProducts(products.map((product) =>
          product.id === id ? {
            id: updatedProduct.id.toString(),
            name: updatedProduct.name,
            price: updatedProduct.price,
            category: updatedProduct.category,
            isActive: updatedProduct.available
          } : product
        ))
        setEditingProduct(null)
      }
    } catch (error) {
      console.error("Failed to update product:", error)
    }
  }

  const deleteProduct = async (id: string) => {
    const product = products.find(p => p.id === id)
    if (!product) return

    // Confirm deletion
    const confirmed = window.confirm(
      t('settings.confirmDeleteProduct', { name: product.name })
    )

    if (!confirmed) return

    try {
      const response = await fetch(`/api/menu-items?id=${id}`, {
        method: 'DELETE',
        credentials: 'include', // Use cookies for authentication
      })

      if (response.ok) {
        setProducts(products.filter((product) => product.id !== id))
        // Also remove from selected products if it was selected
        setSelectedProducts(selectedProducts.filter(selectedId => selectedId !== id))
      } else {
        const errorData = await response.json()
        console.error("Failed to delete product:", errorData.error)
        alert(t('settings.failedToDeleteProductWithError', { error: errorData.error }))
      }
    } catch (error) {
      console.error("Failed to delete product:", error)
      alert(t('settings.failedToDeleteProduct'))
    }
  }

  const addCategory = async (categoryData: Omit<Category, "id">) => {
    try {
      const response = await fetch('/api/categories', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: categoryData.label,
          key: categoryData.key,
          icon: categoryData.icon,
          isActive: categoryData.isActive,
          displayOrder: categoryData.order
        }),
      })

      if (response.ok) {
        const newCategory = await response.json()
        setCategories([...categories, {
          id: newCategory.id.toString(),
          key: newCategory.key,
          label: newCategory.name,
          icon: newCategory.icon,
          isActive: newCategory.is_active,
          order: newCategory.display_order
        }])
        setIsAddCategoryOpen(false)
      }
    } catch (error) {
      console.error("Failed to add category:", error)
    }
  }

  const updateCategory = async (id: string, updates: Partial<Category>) => {
    try {
      const response = await fetch('/api/categories', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id,
          name: updates.label,
          key: updates.key,
          icon: updates.icon,
          isActive: updates.isActive,
          displayOrder: updates.order
        }),
      })

      if (response.ok) {
        const updatedCategory = await response.json()
        setCategories(categories.map((category) =>
          category.id === id ? {
            id: updatedCategory.id.toString(),
            key: updatedCategory.key,
            label: updatedCategory.name,
            icon: updatedCategory.icon,
            isActive: updatedCategory.is_active,
            order: updatedCategory.display_order
          } : category
        ))
        setEditingCategory(null)
      }
    } catch (error) {
      console.error("Failed to update category:", error)
    }
  }

  const deleteCategory = async (id: string) => {
    try {
      const response = await fetch(`/api/categories?id=${id}`, {
        method: 'DELETE',
        credentials: 'include', // Use cookies for authentication
      })

      if (response.ok) {
        setCategories(categories.filter((category) => category.id !== id))
      }
    } catch (error) {
      console.error("Failed to delete category:", error)
    }
  }

  // Save login settings
  const saveLoginSettings = async () => {
    try {
      // First try to save to database
      const response = await fetch('/api/login-settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Include cookies for authentication
        body: JSON.stringify(loginSettings),
      })

      if (response.ok) {
        // Also save to localStorage as backup
        localStorage.setItem('login_settings_backup', JSON.stringify(loginSettings))
        toast.success(t('settings.loginSettingsSavedSuccessfully'))
        console.log('✅ Login settings saved successfully to database and localStorage')
      } else {
        let errorData = {}
        try {
          errorData = await response.json()
        } catch (parseError) {
          console.warn('Failed to parse error response:', parseError)
        }

        console.error('❌ Database save failed:', {
          status: response.status,
          statusText: response.statusText,
          errorData
        })

        if (response.status === 401) {
          toast.error('Authentication failed. Please log in again.')
          console.error('🔒 Authentication failed - user may need to log in again')
        } else if (response.status === 403) {
          toast.error('Access denied. Admin privileges required.')
          console.error('🔒 Access denied - admin privileges required')
        } else {
          // Fallback to localStorage if database fails
          localStorage.setItem('login_settings_backup', JSON.stringify(loginSettings))
          toast.success('Settings saved locally (database unavailable)')
          console.log('✅ Login settings saved to localStorage as fallback')
        }
      }
    } catch (error) {
      console.error("Failed to save login settings to database:", error)

      // Fallback to localStorage
      try {
        localStorage.setItem('login_settings_backup', JSON.stringify(loginSettings))
        toast.success('Settings saved locally (database unavailable)')
        console.log('✅ Login settings saved to localStorage as fallback')
      } catch (localError) {
        console.error("Failed to save to localStorage:", localError)
        toast.error(t('settings.failedToSaveLoginSettings'))
      }
    }
  }

  const tabs = [
    { key: "categories", label: t('settings.barCategories'), icon: Coffee },
    { key: "barMenu", label: t('bar.barTables'), icon: Coffee },
    { key: "products", label: t('settings.barMenu'), icon: Coffee },
    { key: "game", label: t('settings.gameSettings'), icon: Clock },
    { key: "business", label: t('settings.businessInfo'), icon: Building },
    { key: "currency", label: t('settings.currency'), icon: DollarSign },
    { key: "login", label: t('settings.loginSettings'), icon: LogIn },
    { key: "users", label: t('settings.userManagement'), icon: Users },
    { key: "permissions", label: t('settings.permissions'), icon: SettingsIcon },
  ]

  // Check permissions after all hooks are called - allow admin users always
  if (user?.role !== 'admin' && !canAccessSettings()) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <div className="text-lg text-gray-600 mb-2">{t('settings.accessDenied')}</div>
          <div className="text-sm text-gray-500">{t('settings.noPermission')}</div>
          <div className="text-xs text-gray-400 mt-2">{t('settings.adminOnly')}</div>
        </div>
      </div>
    )
  }

  const getTabDescription = (tabKey: string) => {
    switch (tabKey) {
      case 'categories': return t('settings.organizeMenuItemsIntoCategories')
      case 'barMenu': return t('settings.manageBarTablesAndSeating')
      case 'products': return t('settings.manageMenuItemsPricingAvailability')
      case 'game': return t('settings.configureBilliardGameSettings')
      case 'business': return 'Configure your business details for receipts'
      case 'currency': return 'Set up currency, tax, and pricing preferences'
      case 'login': return 'Configure waiter login settings and accounts'
      case 'users': return 'Manage user accounts, roles, and permissions'
      case 'permissions': return 'Control what different user roles can access'
      default: return 'Configure application settings'
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Mobile Sidebar Overlay */}
      {isMobileSidebarOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-40 md:hidden"
          onClick={() => setIsMobileSidebarOpen(false)}
        />
      )}

      <div className="md:flex md:h-screen">
        {/* Simple Sidebar */}
        <div className={`${
          isMobileSidebarOpen ? 'translate-x-0' : '-translate-x-full'
        } md:translate-x-0 fixed md:static inset-y-0 left-0 z-50 w-64 md:w-60 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col transition-transform duration-200`}>
          {/* Simple Header */}
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="p-2 bg-blue-600 rounded-lg">
                  <SettingsIcon className="h-4 w-4 text-white" />
                </div>
                <div>
                  <h1 className="text-base font-semibold text-gray-900 dark:text-white">
                    {t('settings.title')}
                  </h1>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Configuration
                  </p>
                </div>
              </div>
              {/* Close button for mobile */}
              <button
                onClick={() => setIsMobileSidebarOpen(false)}
                className="md:hidden p-1 rounded text-gray-400 hover:text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
          </div>

          {/* Simple Navigation */}
          <nav className="flex-1 p-2 space-y-1 overflow-y-auto">
            {tabs.map((tab) => (
              <button
                key={tab.key}
                onClick={() => {
                  setActiveTab(tab.key as any)
                  setIsMobileSidebarOpen(false)
                }}
                className={`w-full flex items-center gap-2 px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                  activeTab === tab.key
                    ? "bg-blue-600 text-white"
                    : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                }`}
              >
                <tab.icon className="h-4 w-4 flex-shrink-0" />
                <span className="truncate text-left">{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex flex-col min-h-screen md:overflow-hidden">
          {/* Simple Header */}
          <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3 flex-1">
                {/* Mobile menu button */}
                <button
                  onClick={() => setIsMobileSidebarOpen(true)}
                  className="md:hidden p-2 rounded text-gray-500 hover:text-gray-700 hover:bg-gray-100 dark:hover:bg-gray-700"
                >
                  <Menu className="h-4 w-4" />
                </button>
                <div className="flex-1">
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                    {tabs.find(tab => tab.key === activeTab)?.label}
                  </h2>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {getTabDescription(activeTab)}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-2 px-2 py-1 bg-green-50 dark:bg-green-900/20 rounded border border-green-200 dark:border-green-700">
                <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                <span className="text-xs font-medium text-green-700 dark:text-green-300">Live</span>
              </div>
            </div>
          </div>

          {/* Simple Content Area */}
          <div className="flex-1 overflow-auto p-4 space-y-4">

            {/* Bar Categories Management */}
            {activeTab === "categories" && (
              <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <FolderOpen className="h-5 w-5 text-blue-600" />
                      <div>
                        <CardTitle className="text-base font-semibold text-gray-900 dark:text-white">
                          {t('settings.categories')} ({categories.length})
                        </CardTitle>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {t('settings.organizeMenuItems')}
                        </p>
                      </div>
                    </div>
                    <Dialog open={isAddCategoryOpen} onOpenChange={setIsAddCategoryOpen}>
                      <DialogTrigger asChild>
                        <Button size="sm" className="bg-blue-600 hover:bg-blue-700 text-white">
                          <Plus className="h-4 w-4 mr-1" />
                          {t('common.add')}
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>{t('settings.addNewBarCategory')}</DialogTitle>
                          <DialogDescription>{t('settings.createNewCategory')}</DialogDescription>
                        </DialogHeader>
                        <AddCategoryForm onSubmit={addCategory} />
                      </DialogContent>
                    </Dialog>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                    {categories
                      .sort((a, b) => a.order - b.order)
                      .map((category) => (
                        <div key={category.id} className="p-3 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">{category.label}</h4>
                            <Badge variant={category.isActive ? "default" : "secondary"} className="text-xs">
                              {category.isActive ? t('bar.active') : t('bar.inactive')}
                            </Badge>
                          </div>
                          <div className="space-y-1 text-xs text-gray-500 dark:text-gray-400 mb-3">
                            <div className="flex justify-between">
                              <span>Key:</span>
                              <code className="bg-gray-200 dark:bg-gray-600 px-1 rounded text-xs">{category.key}</code>
                            </div>
                            <div className="flex justify-between">
                              <span>{t('settings.order')}:</span>
                              <span>#{category.order}</span>
                            </div>
                          </div>
                          <div className="flex gap-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => setEditingCategory(category)}
                              className="flex-1 h-7 text-xs"
                            >
                              <Edit className="h-3 w-3 mr-1" />
                              {t('settings.edit')}
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => deleteCategory(category.id)}
                              className="h-7 px-2 text-xs text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      ))}
                  </div>
                </CardContent>
              </Card>
            )}



            {/* Bar Tables Management */}
            {activeTab === "barMenu" && (
              <BarSettings onSettingsChange={refreshGames} />
            )}

            {/* Bar Menu Management */}
            {activeTab === "products" && (
              <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg">
                <CardHeader className="pb-3">
                  <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
                    <div className="flex items-center gap-2">
                      <Coffee className="h-5 w-5 text-blue-600" />
                      <div>
                        <CardTitle className="text-base font-semibold text-gray-900 dark:text-white">
                          Bar Menu ({filteredProducts.length})
                        </CardTitle>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          Manage menu items
                        </p>
                      </div>
                    </div>
                    <Dialog open={isAddProductOpen} onOpenChange={setIsAddProductOpen}>
                      <DialogTrigger asChild>
                        <Button size="sm" className="bg-blue-600 hover:bg-blue-700 text-white">
                          <Plus className="h-4 w-4 mr-1" />
                          {t('settings.addItem')}
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>{t('settings.addNewBarMenuItem')}</DialogTitle>
                          <DialogDescription>{t('settings.addNewMenuItemDescription')}</DialogDescription>
                        </DialogHeader>
                        <AddProductForm onSubmit={addProduct} />
                      </DialogContent>
                    </Dialog>
                  </div>

                  {/* Simple Search and Filter Controls */}
                  <div className="flex flex-col sm:flex-row gap-3 mt-4">
                    <div className="relative flex-1">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                      <Input
                        placeholder={t('bar.searchProducts')}
                        value={productSearchTerm}
                        onChange={(e) => setProductSearchTerm(e.target.value)}
                        className="pl-9 h-9"
                      />
                    </div>
                    <div className="flex gap-2">
                      <Select value={productFilterCategory} onValueChange={setProductFilterCategory}>
                        <SelectTrigger className="w-full sm:w-32 h-9">
                          <SelectValue placeholder={t('bar.category')} />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">{t('common.all')}</SelectItem>
                          {categories
                            .filter((cat) => cat.isActive)
                            .map((category) => (
                              <SelectItem key={category.key} value={category.key}>
                                {category.label}
                              </SelectItem>
                            ))}
                        </SelectContent>
                      </Select>
                      <Select value={productFilterStatus} onValueChange={(value: any) => setProductFilterStatus(value)}>
                        <SelectTrigger className="w-full sm:w-24 h-9">
                          <SelectValue placeholder={t('bar.status')} />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">{t('settings.all')}</SelectItem>
                          <SelectItem value="active">{t('settings.active')}</SelectItem>
                          <SelectItem value="inactive">{t('settings.inactive')}</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  {/* Bulk Actions */}
                  {selectedProducts.length > 0 && (
                    <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-700">
                      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
                        <span className="text-sm font-medium text-blue-900 dark:text-blue-100">
                          {selectedProducts.length} item{selectedProducts.length > 1 ? 's' : ''} selected
                        </span>
                        <div className="flex flex-wrap gap-2">
                          <Button size="sm" variant="outline" onClick={() => bulkUpdateProductStatus(true)} className="text-xs">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            {t('settings.activate')}
                          </Button>
                          <Button size="sm" variant="outline" onClick={() => bulkUpdateProductStatus(false)} className="text-xs">
                            <XCircle className="h-3 w-3 mr-1" />
                            {t('settings.deactivate')}
                          </Button>
                          <Button size="sm" variant="outline" onClick={bulkDeleteProducts} className="text-xs text-red-600">
                            <Trash2 className="h-3 w-3 mr-1" />
                            {t('settings.delete')}
                          </Button>
                          <Button size="sm" variant="outline" onClick={() => setSelectedProducts([])} className="text-xs">
                            {t('settings.clear')}
                          </Button>
                        </div>
                      </div>
                    </div>
                  )}

                  {filteredProducts.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      <Coffee className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                      <p className="text-sm font-medium mb-1">
                        {productSearchTerm || productFilterCategory !== "all" || productFilterStatus !== "all"
                          ? t('bar.noProductsMatch')
                          : t('bar.noProductsFound')}
                      </p>
                      <p className="text-xs">
                        {productSearchTerm || productFilterCategory !== "all" || productFilterStatus !== "all"
                          ? t('bar.tryAdjustingSearch')
                          : t('bar.addFirstMenuItem')}
                      </p>
                    </div>
                  ) : (
                    <>
                      {/* Select All */}
                      <div className="flex items-center gap-2 mb-4 pb-3 border-b border-gray-200 dark:border-gray-700">
                        <Checkbox
                          checked={selectedProducts.length === filteredProducts.length && filteredProducts.length > 0}
                          onCheckedChange={toggleAllProducts}
                        />
                        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          {t('settings.selectAll')} ({filteredProducts.length})
                        </span>
                      </div>

                      {/* Products Grid */}
                      {productFilterCategory === "all" ? (
                        // Show grouped by category
                        categories
                          .filter((cat) => cat.isActive)
                          .map((category) => {
                            const categoryProducts = filteredProducts.filter((p) => p.category === category.key)
                            if (categoryProducts.length === 0) return null

                            return (
                              <div key={category.key} className="mb-6">
                                <h3 className="text-sm font-semibold mb-3 text-gray-900 dark:text-white">
                                  {category.label} ({categoryProducts.length})
                                </h3>
                                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
                                  {categoryProducts.map((product) => (
                                    <ProductCard
                                      key={product.id}
                                      product={product}
                                      isSelected={selectedProducts.includes(product.id)}
                                      onSelect={(checked) => {
                                        if (checked) {
                                          setSelectedProducts([...selectedProducts, product.id])
                                        } else {
                                          setSelectedProducts(selectedProducts.filter(id => id !== product.id))
                                        }
                                      }}
                                      onEdit={() => setEditingProduct(product)}
                                      onDelete={() => deleteProduct(product.id)}
                                      onToggleStatus={() => updateProduct(product.id, { ...product, isActive: !product.isActive })}
                                    />
                                  ))}
                                </div>
                              </div>
                            )
                          })
                      ) : (
                        // Show all filtered products in a grid
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
                          {filteredProducts.map((product) => (
                            <ProductCard
                              key={product.id}
                              product={product}
                              isSelected={selectedProducts.includes(product.id)}
                              onSelect={(checked) => {
                                if (checked) {
                                  setSelectedProducts([...selectedProducts, product.id])
                                } else {
                                  setSelectedProducts(selectedProducts.filter(id => id !== product.id))
                                }
                              }}
                              onEdit={() => setEditingProduct(product)}
                              onDelete={() => deleteProduct(product.id)}
                              onToggleStatus={() => updateProduct(product.id, { ...product, isActive: !product.isActive })}
                            />
                          ))}
                        </div>
                      )}
                    </>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Game Settings */}
            {activeTab === "game" && (
              <div className="space-y-4">
                <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg">
                  <CardHeader className="pb-3">
                    <div className="flex items-center gap-2">
                      <Clock className="h-5 w-5 text-blue-600" />
                      <div>
                        <CardTitle className="text-base font-semibold text-gray-900 dark:text-white">
                          {t('settings.gameSettings')}
                        </CardTitle>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {t('settings.configureGameRulesAndPricing')}
                        </p>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Settings Grid */}
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="hourlyRate" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          {t('settings.defaultHourlyRate')}
                        </Label>
                        <Input
                          id="hourlyRate"
                          type="number"
                          value={gameSettings.defaultHourlyRate}
                          onChange={(e) =>
                            setGameSettings({ ...gameSettings, defaultHourlyRate: Number.parseInt(e.target.value) })
                          }
                          className="mt-1 h-9"
                        />
                      </div>
                      <div>
                        <Label htmlFor="minimumTime" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          {t('settings.minimumGameTime')}
                        </Label>
                        <Input
                          id="minimumTime"
                          type="number"
                          value={gameSettings.minimumGameTime}
                          onChange={(e) =>
                            setGameSettings({ ...gameSettings, minimumGameTime: Number.parseInt(e.target.value) })
                          }
                          className="mt-1 h-9"
                        />
                      </div>
                      <div>
                        <Label htmlFor="autoEnd" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          {t('settings.autoEndAfterHours')}
                        </Label>
                        <Input
                          id="autoEnd"
                          type="number"
                          value={gameSettings.autoEndAfterHours}
                          onChange={(e) =>
                            setGameSettings({ ...gameSettings, autoEndAfterHours: Number.parseInt(e.target.value) })
                          }
                          className="mt-1 h-9"
                        />
                      </div>
                      <div>
                        <Label htmlFor="taxRate" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          {t('settings.taxRate')}
                        </Label>
                        <Input
                          id="taxRate"
                          type="number"
                          value={gameSettings.taxRate}
                          onChange={(e) => setGameSettings({ ...gameSettings, taxRate: Number.parseInt(e.target.value) })}
                          className="mt-1 h-9"
                        />
                      </div>
                    </div>

                    {/* Auto-print Setting */}
                    <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600">
                      <div className="flex items-center space-x-2">
                        <Switch
                          id="autoPrint"
                          checked={gameSettings.autoPrintReceipts}
                          onCheckedChange={(checked) => setGameSettings({ ...gameSettings, autoPrintReceipts: checked })}
                        />
                        <Label htmlFor="autoPrint" className="text-sm font-medium text-gray-900 dark:text-white">
                          {t('settings.autoPrintReceipts')}
                        </Label>
                      </div>
                    </div>

                    {/* Save Buttons - Responsive */}
                    <div className="flex flex-col sm:flex-row gap-3 pt-2">
                      <Button
                        onClick={() => saveGameSettings(false)}
                        className="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white h-10"
                      >
                        <Save className="h-4 w-4 mr-2" />
                        {t('settings.saveSettings')}
                      </Button>
                      <Button
                        onClick={() => saveGameSettings(true)}
                        variant="outline"
                        className="w-full sm:w-auto h-10"
                      >
                        <Save className="h-4 w-4 mr-2" />
                        {t('settings.saveUpdateAllTables')}
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* Game Tables Management */}
              <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Coffee className="h-5 w-5 text-blue-600" />
                      <div>
                        <CardTitle className="text-base font-semibold text-gray-900 dark:text-white">
                          {t('settings.gameTables')} ({filteredGameTables.length})
                        </CardTitle>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {t('settings.manageBilliardTables')}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={fixTableNames}
                        className="text-xs"
                      >
                        <CheckCircle className="h-3 w-3 mr-1" />
                        {t('settings.fix')}
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={reseedGameTables}
                        className="text-xs"
                      >
                        <RefreshCw className="h-3 w-3 mr-1" />
                        {t('settings.reset')}
                      </Button>
                      <Dialog open={isAddGameTableOpen} onOpenChange={setIsAddGameTableOpen}>
                        <DialogTrigger asChild>
                          <Button size="sm" className="bg-blue-600 hover:bg-blue-700 text-white">
                            <Plus className="h-4 w-4 mr-1" />
                            {t('settings.add')}
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>{t('settings.addNewGameTable')}</DialogTitle>
                            <DialogDescription>{t('settings.createNewGameTableDescription')}</DialogDescription>
                          </DialogHeader>
                          <AddGameTableForm
                            onSubmit={addGameTable}
                            defaultHourlyRate={gameSettings.defaultHourlyRate}
                            existingTables={gameTables}
                          />
                        </DialogContent>
                      </Dialog>
                    </div>
                  </div>

                  {/* Simple Search and Filter */}
                  <div className="flex gap-3 mt-4">
                    <div className="relative flex-1">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                      <Input
                        placeholder={t('settings.searchTables')}
                        value={gameTableSearchTerm}
                        onChange={(e) => setGameTableSearchTerm(e.target.value)}
                        className="pl-9 h-9"
                      />
                    </div>
                    <Select value={gameTableFilterStatus} onValueChange={(value: any) => setGameTableFilterStatus(value)}>
                      <SelectTrigger className="w-32 h-9">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">{t('settings.all')}</SelectItem>
                        <SelectItem value="active">{t('settings.active')}</SelectItem>
                        <SelectItem value="inactive">{t('settings.inactive')}</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </CardHeader>
                <CardContent>
                  {/* Bulk Actions */}
                  {selectedGameTables.length > 0 && (
                    <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-700">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-blue-900 dark:text-blue-100">
                          {selectedGameTables.length} {t('settings.selected')}
                        </span>
                        <div className="flex gap-2">
                          <Button size="sm" variant="outline" onClick={() => bulkUpdateGameTableStatus(true)} className="text-xs">
                            <Eye className="h-3 w-3 mr-1" />
                            {t('settings.activate')}
                          </Button>
                          <Button size="sm" variant="outline" onClick={() => bulkUpdateGameTableStatus(false)} className="text-xs">
                            <EyeOff className="h-3 w-3 mr-1" />
                            {t('settings.deactivate')}
                          </Button>
                          <Button size="sm" variant="outline" onClick={bulkDeleteGameTables} className="text-xs text-red-600">
                            <Trash2 className="h-3 w-3 mr-1" />
                            {t('settings.delete')}
                          </Button>
                          <Button size="sm" variant="outline" onClick={() => setSelectedGameTables([])} className="text-xs">
                            {t('settings.clear')}
                          </Button>
                        </div>
                      </div>
                    </div>
                  )}

                  {filteredGameTables.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      <Coffee className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                      <p className="text-sm font-medium mb-1">
                        {gameTableSearchTerm || gameTableFilterStatus !== "all" ? t('settings.noTablesMatchSearch') : t('settings.noGameTablesFound')}
                      </p>
                      <p className="text-xs">
                        {gameTableSearchTerm || gameTableFilterStatus !== "all" ? t('settings.tryAdjustingSearch') : t('settings.addFirstTableToStart')}
                      </p>
                    </div>
                  ) : (
                    <>
                      {/* Select All */}
                      <div className="flex items-center gap-2 mb-4 pb-3 border-b border-gray-200 dark:border-gray-700">
                        <Checkbox
                          checked={selectedGameTables.length === filteredGameTables.length && filteredGameTables.length > 0}
                          onCheckedChange={toggleAllGameTables}
                        />
                        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          {t('settings.selectAll')} ({filteredGameTables.length})
                        </span>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                        {filteredGameTables.map((table) => (
                          <GameTableCard
                            key={table.id}
                            table={table}
                            isSelected={selectedGameTables.includes(table.id)}
                            onSelect={(checked) => {
                              if (checked) {
                                setSelectedGameTables([...selectedGameTables, table.id])
                              } else {
                                setSelectedGameTables(selectedGameTables.filter(id => id !== table.id))
                              }
                            }}
                            onEdit={() => setEditingGameTable(table)}
                            onDelete={() => deleteGameTable(table.id)}
                            onToggleStatus={() => updateGameTable(table.id, { ...table, isActive: !table.isActive })}
                          />
                        ))}
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>
              </div>
            )}

            {/* Business Information */}
            {activeTab === "business" && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white">Business Information</h3>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Configure your business details</p>
                  </div>
                </div>

                <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
                  <CardContent className="p-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-3">
                        <div>
                          <Label htmlFor="businessName" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                            {t('settings.businessName')}
                          </Label>
                          <Input
                            id="businessName"
                            value={businessInfo.name}
                            onChange={(e) => handleBusinessInfoChange('name', e.target.value)}
                            placeholder={t('settings.enterBusinessName')}
                            className="mt-1 h-9"
                          />
                        </div>
                        <div>
                          <Label htmlFor="businessAddress" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                            {t('settings.address')}
                          </Label>
                          <Input
                            id="businessAddress"
                            value={businessInfo.address}
                            onChange={(e) => handleBusinessInfoChange('address', e.target.value)}
                            placeholder={t('settings.enterBusinessAddress')}
                            className="mt-1 h-9"
                          />
                        </div>
                        <div>
                          <Label htmlFor="businessPhone" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                            {t('settings.phone')}
                          </Label>
                          <Input
                            id="businessPhone"
                            value={businessInfo.phone}
                            onChange={(e) => handleBusinessInfoChange('phone', e.target.value)}
                            placeholder={t('settings.enterPhoneNumber')}
                            className="mt-1 h-9"
                          />
                        </div>
                      </div>
                      <div className="space-y-3">
                        <div>
                          <Label htmlFor="businessEmail" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                            {t('settings.email')}
                          </Label>
                          <Input
                            id="businessEmail"
                            value={businessInfo.email}
                            onChange={(e) => handleBusinessInfoChange('email', e.target.value)}
                            placeholder={t('settings.enterEmailAddress')}
                            className="mt-1 h-9"
                          />
                        </div>
                        <div>
                          <Label htmlFor="businessVat" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                            {t('settings.vatNumber')}
                          </Label>
                          <Input
                            id="businessVat"
                            value={businessInfo.vatNumber}
                            onChange={(e) => handleBusinessInfoChange('vatNumber', e.target.value)}
                            placeholder={t('settings.enterVatNumber')}
                            className="mt-1 h-9"
                          />
                        </div>
                      </div>
                    </div>
                    <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                      <Button
                        onClick={saveBusinessInfo}
                        size="sm"
                        className="bg-blue-600 hover:bg-blue-700 text-white"
                      >
                        <Save className="h-4 w-4 mr-2" />
                        {t('settings.saveBusinessInformation')}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

        {/* Currency Settings */}
        {activeTab === "currency" && (
          <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg">
            <CardHeader className="pb-3">
              <div className="flex items-center gap-2">
                <DollarSign className="h-5 w-5 text-blue-600" />
                <div>
                  <CardTitle className="text-base font-semibold text-gray-900 dark:text-white">
                    {t('settings.currencySettings')}
                  </CardTitle>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    Configure currency and tax settings
                  </p>
                </div>
              </div>
            </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="currencyName" className="text-sm font-medium">{t('settings.currencyName')}</Label>
                <Select
                  value={currencySettings.currency}
                  onValueChange={(value) => setCurrencySettings(prev => ({ ...prev, currency: value }))}
                >
                  <SelectTrigger className="h-9">
                    <SelectValue placeholder={t('settings.selectCurrency')} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Albanian Lek">{t('settings.albanianLek')}</SelectItem>
                    <SelectItem value="Euro">{t('settings.euro')}</SelectItem>
                    <SelectItem value="US Dollar">{t('settings.usDollar')}</SelectItem>
                    <SelectItem value="British Pound">{t('settings.britishPound')}</SelectItem>
                    <SelectItem value="Swiss Franc">{t('settings.swissFranc')}</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-gray-500 mt-1">{t('settings.currencyUsedInBusiness')}</p>
              </div>

              <div>
                <Label htmlFor="currencySymbol" className="text-sm font-medium">{t('settings.currencySymbol')}</Label>
                <Input
                  id="currencySymbol"
                  value={currencySettings.symbol}
                  onChange={(e) => setCurrencySettings(prev => ({ ...prev, symbol: e.target.value }))}
                  placeholder="L, €, $, £, CHF"
                  maxLength={5}
                  className="h-9"
                />
                <p className="text-xs text-gray-500 mt-1">{t('settings.symbolDisplayedWithPrices')}</p>
              </div>

              <div>
                <Label htmlFor="taxRate" className="text-sm font-medium">{t('settings.taxRate')}</Label>
                <Input
                  id="taxRate"
                  type="number"
                  min="0"
                  max="100"
                  step="0.1"
                  value={currencySettings.taxRate}
                  onChange={(e) => setCurrencySettings(prev => ({ ...prev, taxRate: Number(e.target.value) }))}
                  className="h-9"
                />
                <p className="text-xs text-gray-500 mt-1">{t('settings.taxPercentageApplied')}</p>
              </div>
            </div>
            {/* Settings Switches */}
            <div className="space-y-3">
              <div className="flex items-center space-x-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <Switch
                  id="showDecimals"
                  checked={currencySettings.showDecimals}
                  onCheckedChange={(checked) => setCurrencySettings(prev => ({ ...prev, showDecimals: checked }))}
                />
                <div>
                  <Label htmlFor="showDecimals" className="text-sm font-medium">{t('settings.showDecimals')}</Label>
                  <p className="text-xs text-gray-500">{t('settings.displayPricesWithDecimals')}</p>
                </div>
              </div>

              <div className="flex items-center space-x-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <Switch
                  id="taxEnabled"
                  checked={currencySettings.taxEnabled}
                  onCheckedChange={(checked) => setCurrencySettings(prev => ({ ...prev, taxEnabled: checked }))}
                />
                <div>
                  <Label htmlFor="taxEnabled" className="text-sm font-medium">{t('settings.enableTax')}</Label>
                  <p className="text-xs text-gray-500">{t('settings.turnTaxCalculationOnOff')}</p>
                </div>
              </div>

              {currencySettings.taxEnabled && (
                <div className="flex items-center space-x-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-700">
                  <Switch
                    id="taxIncluded"
                    checked={currencySettings.taxIncluded}
                    onCheckedChange={(checked) => setCurrencySettings(prev => ({ ...prev, taxIncluded: checked }))}
                  />
                  <div>
                    <Label htmlFor="taxIncluded" className="text-sm font-medium">{t('settings.taxIncludedInPrices')}</Label>
                    <p className="text-xs text-gray-500">
                      {currencySettings.taxIncluded
                        ? t('settings.taxIncludedNote')
                        : t('settings.taxExcludedNote')
                      }
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* QR Code Settings */}
            <div className="space-y-3">
              <h3 className="text-sm font-semibold text-gray-900 dark:text-white">{t('settings.qrCodeSettings')}</h3>
              <div className="flex items-center space-x-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <Switch
                  id="qrCodeEnabled"
                  checked={currencySettings.qrCodeEnabled}
                  onCheckedChange={(checked) => setCurrencySettings(prev => ({ ...prev, qrCodeEnabled: checked }))}
                />
                <div>
                  <Label htmlFor="qrCodeEnabled" className="text-sm font-medium">{t('settings.includeQrCodeOnReceipts')}</Label>
                  <p className="text-xs text-gray-500">{t('settings.addQrCodeToReceipts')}</p>
                </div>
              </div>

              {currencySettings.qrCodeEnabled && (
                <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-700">
                  <Label htmlFor="qrCodeUrl" className="text-sm font-medium">{t('settings.qrCodeUrl')}</Label>
                  <p className="text-xs text-gray-500 mb-2">{t('settings.qrCodeUrlDescription')}</p>
                  <Input
                    id="qrCodeUrl"
                    type="url"
                    placeholder={t('settings.qrCodeUrlPlaceholder')}
                    value={currencySettings.qrCodeUrl}
                    onChange={(e) => setCurrencySettings(prev => ({ ...prev, qrCodeUrl: e.target.value }))}
                    className="h-9"
                  />
                  <p className="text-xs text-gray-400 mt-1">
                    Examples: Website URL, Google Reviews, Social Media, etc.
                  </p>
                </div>
              )}
            </div>

            {/* Preview Section */}
            <div className="border-t pt-4">
              <h3 className="text-sm font-semibold mb-3 text-gray-900 dark:text-white">Preview</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <h4 className="text-sm font-medium mb-2">Price Display</h4>
                  <div className="space-y-1 text-xs">
                    {(() => {
                      const basePrice = 50
                      const formatPrice = (amount: number) => currencySettings.showDecimals ? amount.toFixed(2) : Math.round(amount).toString()

                      if (!currencySettings.taxEnabled) {
                        return (
                          <>
                            <div>{t('settings.product')}: {formatPrice(basePrice)} {currencySettings.symbol}</div>
                            <div className="font-semibold border-t pt-1">
                              {t('settings.total')}: {formatPrice(basePrice)} {currencySettings.symbol}
                            </div>
                          </>
                        )
                      }

                      if (currencySettings.taxIncluded) {
                        const taxRate = currencySettings.taxRate / 100
                        const subtotalExcludingTax = basePrice / (1 + taxRate)
                        const taxAmount = basePrice - subtotalExcludingTax
                        return (
                          <>
                            <div>{t('settings.product')}: {formatPrice(basePrice)} {currencySettings.symbol}</div>
                            <div>{t('settings.subtotal')}: {formatPrice(subtotalExcludingTax)} {currencySettings.symbol}</div>
                            <div>{t('settings.tax')} ({currencySettings.taxRate}%): {formatPrice(taxAmount)} {currencySettings.symbol}</div>
                            <div className="font-semibold border-t pt-1">
                              {t('settings.total')}: {formatPrice(basePrice)} {currencySettings.symbol}
                            </div>
                          </>
                        )
                      } else {
                        const taxAmount = basePrice * currencySettings.taxRate / 100
                        const total = basePrice + taxAmount
                        return (
                          <>
                            <div>{t('settings.product')}: {formatPrice(basePrice)} {currencySettings.symbol}</div>
                            <div>{t('settings.tax')} ({currencySettings.taxRate}%): {formatPrice(taxAmount)} {currencySettings.symbol}</div>
                            <div className="font-semibold border-t pt-1">
                              {t('settings.total')}: {formatPrice(total)} {currencySettings.symbol}
                            </div>
                          </>
                        )
                      }
                    })()}
                  </div>
                </div>
                <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <h4 className="text-sm font-medium mb-2">Receipt Format</h4>
                  <div className="space-y-1 text-xs font-mono">
                    {(() => {
                      const formatPrice = (amount: number) => currencySettings.showDecimals ? amount.toFixed(2) : Math.round(amount).toString()
                      const orderSubtotal = 250

                      if (!currencySettings.taxEnabled) {
                        return (
                          <>
                            <div>Coffee x 1 = {formatPrice(50)} {currencySettings.symbol}</div>
                            <div>Beer x 2 = {formatPrice(200)} {currencySettings.symbol}</div>
                            <div className="border-t pt-1 font-bold">
                              Total: {formatPrice(orderSubtotal)} {currencySettings.symbol}
                            </div>
                          </>
                        )
                      }

                      if (currencySettings.taxIncluded) {
                        const taxRate = currencySettings.taxRate / 100
                        const subtotalExcludingTax = orderSubtotal / (1 + taxRate)
                        const taxAmount = orderSubtotal - subtotalExcludingTax
                        return (
                          <>
                            <div>Coffee x 1 = {formatPrice(50)} {currencySettings.symbol}</div>
                            <div>Beer x 2 = {formatPrice(200)} {currencySettings.symbol}</div>
                            <div className="border-t pt-1">
                              <div>Subtotal: {formatPrice(subtotalExcludingTax)} {currencySettings.symbol}</div>
                              <div>Tax ({currencySettings.taxRate}%): {formatPrice(taxAmount)} {currencySettings.symbol}</div>
                              <div className="font-bold">Total: {formatPrice(orderSubtotal)} {currencySettings.symbol}</div>
                            </div>
                          </>
                        )
                      } else {
                        const taxAmount = orderSubtotal * currencySettings.taxRate / 100
                        const total = orderSubtotal + taxAmount
                        return (
                          <>
                            <div>Coffee x 1 = {formatPrice(50)} {currencySettings.symbol}</div>
                            <div>Beer x 2 = {formatPrice(200)} {currencySettings.symbol}</div>
                            <div className="border-t pt-1">
                              <div>Subtotal: {formatPrice(orderSubtotal)} {currencySettings.symbol}</div>
                              <div>Tax ({currencySettings.taxRate}%): {formatPrice(taxAmount)} {currencySettings.symbol}</div>
                              <div className="font-bold">Total: {formatPrice(total)} {currencySettings.symbol}</div>
                            </div>
                          </>
                        )
                      }
                    })()}
                  </div>
                </div>
              </div>
            </div>

            <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
              <Button
                onClick={saveCurrencySettings}
                className="bg-blue-600 hover:bg-blue-700 text-white h-9 px-6"
              >
                <Save className="h-4 w-4 mr-2" />
                {t('settings.saveCurrencySettings')}
              </Button>
            </div>
          </CardContent>
        </Card>
        )}

        {/* Login Settings */}
        {activeTab === "login" && (
          <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg">
            <CardHeader className="pb-3">
              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
                <div className="flex items-center gap-2">
                  <div className="p-2 bg-blue-600 rounded-lg">
                    <LogIn className="h-4 w-4 text-white" />
                  </div>
                  <div>
                    <CardTitle className="text-base font-semibold text-gray-900 dark:text-white">
                      {t('settings.loginSettings')}
                    </CardTitle>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {t('settings.configureWaiterLoginAccounts')}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {loginSettingsSource === 'database' && (
                    <Badge variant="default" className="bg-green-500 text-white text-xs">
                      {t('settings.database')}
                    </Badge>
                  )}
                  {loginSettingsSource === 'localStorage' && (
                    <Badge variant="outline" className="border-orange-500 text-orange-600 text-xs">
                      {t('settings.localStorage')}
                    </Badge>
                  )}
                  {loginSettingsSource === 'default' && (
                    <Badge variant="secondary" className="text-xs">
                      {t('settings.default')}
                    </Badge>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Waiters Section Toggle */}
              <div className="flex items-center space-x-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <Switch
                  id="waitersSection"
                  checked={loginSettings.waiters_section_enabled}
                  onCheckedChange={(checked) => setLoginSettings(prev => ({ ...prev, waiters_section_enabled: checked }))}
                />
                <div>
                  <Label htmlFor="waitersSection" className="text-sm font-medium">{t('settings.enableWaitersAccountsSection')}</Label>
                  <p className="text-xs text-gray-500">{t('settings.showQuickLoginButtons')}</p>
                </div>
              </div>

              {loginSettings.waiters_section_enabled && (
                <div className="space-y-4">
                  {/* Section Title */}
                  <div>
                    <Label htmlFor="sectionTitle" className="text-sm font-medium">{t('settings.sectionTitle')}</Label>
                    <Input
                      id="sectionTitle"
                      value={loginSettings.waiters_section_title}
                      onChange={(e) => setLoginSettings(prev => ({ ...prev, waiters_section_title: e.target.value }))}
                      placeholder={t('settings.waitersAccounts')}
                      className="h-9"
                    />
                    <p className="text-xs text-gray-500 mt-1">{t('settings.titleDisplayedAboveButtons')}</p>
                  </div>

                  {/* Add Waiter Button */}
                  <div className="flex justify-between items-center">
                    <h3 className="text-sm font-semibold text-gray-900 dark:text-white">
                      {t('settings.waiters')} ({loginSettings.waiters.length})
                    </h3>
                    <Button
                      onClick={addWaiter}
                      size="sm"
                      className="bg-blue-600 hover:bg-blue-700 text-white"
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      {t('settings.addWaiter')}
                    </Button>
                  </div>

                  {/* Dynamic Waiter Settings */}
                  {loginSettings.waiters.map((waiter) => (
                    <div key={waiter.id} className="p-3 border border-gray-200 dark:border-gray-600 rounded-lg space-y-3">
                      <div className="flex items-center justify-between">
                        <h3 className="text-sm font-semibold text-gray-900 dark:text-white">
                          {t('settings.waiterSettings')}
                        </h3>
                        <div className="flex items-center gap-2">
                          <Switch
                            checked={waiter.enabled}
                            onCheckedChange={(checked) => updateWaiter(waiter.id, { enabled: checked })}
                          />
                          {loginSettings.waiters.length > 1 && (
                            <Button
                              onClick={() => removeWaiter(waiter.id)}
                              size="sm"
                              variant="outline"
                              className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </div>
                      {waiter.enabled && (
                        <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
                          <div>
                            <Label htmlFor={`waiter${waiter.id}Display`} className="text-sm">{t('settings.displayName')}</Label>
                            <Input
                              id={`waiter${waiter.id}Display`}
                              value={waiter.display_name}
                              onChange={(e) => updateWaiter(waiter.id, { display_name: e.target.value })}
                              placeholder={t('settings.waiterDisplayNamePlaceholder')}
                              className="h-9"
                            />
                          </div>
                          <div>
                            <Label htmlFor={`waiter${waiter.id}Username`} className="text-sm">{t('settings.username')}</Label>
                            <Input
                              id={`waiter${waiter.id}Username`}
                              value={waiter.username}
                              onChange={(e) => updateWaiter(waiter.id, { username: e.target.value })}
                              placeholder={t('settings.waiterUsernamePlaceholder')}
                              className="h-9"
                            />
                          </div>
                          <div>
                            <Label htmlFor={`waiter${waiter.id}Password`} className="text-sm">{t('settings.password')}</Label>
                            <div className="relative">
                              <Input
                                id={`waiter${waiter.id}Password`}
                                type={passwordVisibility[waiter.id] ? "text" : "password"}
                                value={waiter.password}
                                onChange={(e) => updateWaiter(waiter.id, { password: e.target.value })}
                                placeholder={t('settings.waiterPasswordPlaceholder')}
                                className="h-9 pr-10"
                              />
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                className="absolute right-0 top-0 h-9 w-9 px-0"
                                onClick={() => togglePasswordVisibility(waiter.id)}
                              >
                                {passwordVisibility[waiter.id] ? (
                                  <EyeOff className="h-4 w-4" />
                                ) : (
                                  <Eye className="h-4 w-4" />
                                )}
                              </Button>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}

                  {/* Preview Section */}
                  <div className="border-t pt-4">
                    <h3 className="text-sm font-semibold mb-3 text-gray-900 dark:text-white">{t('settings.preview')}</h3>
                    <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">{loginSettings.waiters_section_title}</p>
                      <div className="space-y-2">
                        {loginSettings.waiters.filter(w => w.enabled).map((waiter) => (
                          <div key={waiter.id} className="flex items-center justify-between p-2 border border-gray-200 dark:border-gray-600 rounded bg-white dark:bg-gray-800">
                            <span className="font-medium text-sm text-gray-900 dark:text-white">{waiter.display_name}</span>
                            <span className="text-gray-600 dark:text-gray-400 text-xs">{waiter.username}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                <Button
                  onClick={saveLoginSettings}
                  className="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white h-10"
                >
                  <Save className="h-4 w-4 mr-2" />
                  {t('settings.saveLoginSettings')}
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* User Management */}
        {activeTab === "users" && (
          <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg">
            <CardHeader className="pb-3">
              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
                <div className="flex items-center gap-2">
                  <div className="p-2 bg-blue-600 rounded-lg">
                    <Users className="h-4 w-4 text-white" />
                  </div>
                  <div>
                    <CardTitle className="text-base font-semibold text-gray-900 dark:text-white">
                      {t('settings.userManagement')}
                    </CardTitle>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {t('settings.manageUserAccountsAndRoles')}
                    </p>
                  </div>
                </div>
                <Button
                  onClick={() => setIsUserManagementOpen(true)}
                  size="sm"
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  <Users className="h-4 w-4 mr-1" />
                  {t('settings.manageUsers')}
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-700 inline-block mb-4">
                  <Users className="h-8 w-8 text-blue-600 mx-auto" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">{t('settings.userManagementCenter')}</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-6 max-w-sm mx-auto">
                  {t('settings.createEditManageUserAccounts')}
                </p>
                <Button
                  onClick={() => setIsUserManagementOpen(true)}
                  className="bg-blue-600 hover:bg-blue-700 text-white h-9 px-6"
                >
                  <Users className="h-4 w-4 mr-2" />
                  {t('settings.openUserManagement')}
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Permissions Management */}
        {activeTab === "permissions" && (
          <div className="space-y-6">
            <Permissions />
          </div>
        )}

          </div>
        </div>

        {/* Edit Game Table Dialog */}
      {editingGameTable && (
        <Dialog open={!!editingGameTable} onOpenChange={() => setEditingGameTable(null)}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{t('settings.editGameTable')}</DialogTitle>
            </DialogHeader>
            <EditGameTableForm table={editingGameTable} onSubmit={(updates) => updateGameTable(editingGameTable.id, updates)} />
          </DialogContent>
        </Dialog>
      )}

      {/* Edit Product Dialog */}
      {editingProduct && (
        <Dialog open={!!editingProduct} onOpenChange={() => setEditingProduct(null)}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{t('settings.editProduct')}</DialogTitle>
            </DialogHeader>
            <EditProductForm
              product={editingProduct}
              onSubmit={(updates) => updateProduct(editingProduct.id, updates)}
            />
          </DialogContent>
        </Dialog>
      )}

      {/* Edit Category Dialog */}
      {editingCategory && (
        <Dialog open={!!editingCategory} onOpenChange={() => setEditingCategory(null)}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{t('settings.editCategory')}</DialogTitle>
            </DialogHeader>
            <EditCategoryForm
              category={editingCategory}
              onSubmit={(updates) => updateCategory(editingCategory.id, updates)}
            />
          </DialogContent>
        </Dialog>
      )}

        {/* User Management Dialog */}
        <UserManagement
          isOpen={isUserManagementOpen}
          onClose={() => setIsUserManagementOpen(false)}
        />
      </div>
    </div>
  )
}

// Product Card Component
interface ProductCardProps {
  product: Product
  isSelected: boolean
  onSelect: (checked: boolean) => void
  onEdit: () => void
  onDelete: () => void
  onToggleStatus: () => void
}

function ProductCard({ product, isSelected, onSelect, onEdit, onDelete, onToggleStatus }: ProductCardProps) {
  const { t } = useSafeTranslation()

  return (
    <Card className={`${product.isActive ? "border-green-200 bg-green-50/30" : "border-gray-200 bg-gray-50"} ${isSelected ? "ring-2 ring-blue-500" : ""} transition-colors`}>
      <CardContent className="p-3">
        <div className="flex items-start gap-2 mb-2">
          <Checkbox
            checked={isSelected}
            onCheckedChange={onSelect}
            className="mt-0.5"
          />
          <div className="flex-1 min-w-0">
            <h4 className="font-medium text-sm text-gray-900 dark:text-white truncate">{product.name}</h4>
            <div className="flex items-center justify-between mt-1">
              <span className="text-lg font-bold text-blue-600">{product.price} L</span>
              <Badge variant={product.isActive ? "default" : "secondary"} className="text-xs">
                {product.isActive ? t('settings.active') : t('settings.inactive')}
              </Badge>
            </div>
          </div>
        </div>

        <div className="space-y-1 text-xs text-gray-600 dark:text-gray-400 mb-3">
          <div className="flex justify-between">
            <span>{t('settings.category')}:</span>
            <span className="capitalize font-medium">{product.category.replace('_', ' ')}</span>
          </div>
          {product.description && (
            <div className="text-xs text-gray-500 mt-1 line-clamp-2">
              {product.description}
            </div>
          )}
        </div>

        <div className="flex gap-1">
          <Button size="sm" variant="outline" onClick={onEdit} className="flex-1 h-7 text-xs">
            <Edit className="h-3 w-3 mr-1" />
            {t('settings.edit')}
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={onToggleStatus}
            className={`h-7 px-2 text-xs ${product.isActive ? 'text-orange-600 hover:text-orange-700' : 'text-green-600 hover:text-green-700'}`}
          >
            {product.isActive ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={onDelete}
            className="h-7 px-2 text-xs text-red-600 hover:text-red-700"
          >
            <Trash2 className="h-3 w-3" />
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

// Game Table Card Component (specialized for game settings)
interface GameTableCardProps {
  table: GameTable
  isSelected: boolean
  onSelect: (checked: boolean) => void
  onEdit: () => void
  onDelete: () => void
  onToggleStatus: () => void
}

function GameTableCard({ table, isSelected, onSelect, onEdit, onDelete, onToggleStatus }: GameTableCardProps) {
  const { t } = useSafeTranslation()

  return (
    <Card className={`${table.isActive ? "border-green-200 bg-green-50/30" : "border-gray-200 bg-gray-50"} ${isSelected ? "ring-2 ring-blue-500" : ""} transition-colors`}>
      <CardContent className="p-3">
        <div className="flex items-start gap-2 mb-2">
          <Checkbox
            checked={isSelected}
            onCheckedChange={onSelect}
            className="mt-0.5"
          />
          <div className="flex-1 min-w-0">
            <h4 className="font-medium text-sm text-gray-900 dark:text-white truncate">{table.name}</h4>
            <div className="flex items-center justify-between mt-1">
              <span className="text-xs text-gray-500">#{table.number.toString().padStart(4, '0')}</span>
              <Badge variant={table.isActive ? "default" : "secondary"} className="text-xs">
                {table.isActive ? t('settings.active') : t('settings.inactive')}
              </Badge>
            </div>
          </div>
        </div>

        <div className="space-y-1 text-xs text-gray-600 dark:text-gray-400 mb-3">
          <div className="flex justify-between">
            <span>{t('settings.rate')}:</span>
            <span className="font-bold text-blue-600">{table.hourlyRate} L/hr</span>
          </div>
          <div className="flex justify-between">
            <span>{t('settings.type')}:</span>
            <span className="capitalize font-medium">{table.tableType}</span>
          </div>
          <div className="flex justify-between">
            <span>{t('settings.assigned')}:</span>
            <span className={`font-medium ${table.assignedUsername ? "text-blue-600" : "text-gray-500"}`}>
              {table.assignedUsername || t('settings.shared')}
            </span>
          </div>
        </div>

        <div className="flex gap-1">
          <Button size="sm" variant="outline" onClick={onEdit} className="flex-1 h-7 text-xs">
            <Edit className="h-3 w-3 mr-1" />
            {t('settings.edit')}
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={onToggleStatus}
            className={`h-7 px-2 text-xs ${table.isActive ? 'text-orange-600 hover:text-orange-700' : 'text-green-600 hover:text-green-700'}`}
          >
            {table.isActive ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={onDelete}
            className="h-7 px-2 text-xs text-red-600 hover:text-red-700"
          >
            <Trash2 className="h-3 w-3" />
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}



// Auto-Generate Unique 4-Digit Table Numbers (0001 format)
const generateUniqueTableNumber = (existingNumbers: number[]): number => {
  console.log('🎲 Generating unique table number. Existing numbers:', existingNumbers)

  let attempts = 0
  const maxAttempts = 100

  while (attempts < maxAttempts) {
    // Generate random 4-digit number (1-9999)
    const candidate = Math.floor(Math.random() * 9999) + 1

    // Check if this number is already used (globally unique across all users)
    if (!existingNumbers.includes(candidate)) {
      console.log('✅ Generated unique table number:', candidate, 'formatted as:', candidate.toString().padStart(4, '0'))
      return candidate
    }

    attempts++
  }

  console.log('⚠️ Random generation failed after', maxAttempts, 'attempts, using fallback method')

  // Fallback: find next available number starting from 1
  let fallback = 1
  while (existingNumbers.includes(fallback)) {
    fallback++
  }

  console.log('✅ Fallback generated unique table number:', fallback, 'formatted as:', fallback.toString().padStart(4, '0'))
  return fallback
}

// Format number with leading zeros (0001, 0002, etc.)
const formatTableNumber = (num: number): string => {
  return num.toString().padStart(4, '0')
}

// Add Game Table Form Component (for billiard tables)
function AddGameTableForm({
  onSubmit,
  defaultHourlyRate = 400,
  existingTables = []
}: {
  onSubmit: (data: Omit<GameTable, "id"> | Omit<GameTable, "id">[]) => void;
  defaultHourlyRate?: number;
  existingTables?: GameTable[];
}) {
  const { t } = useSafeTranslation()
  const [formData, setFormData] = useState({
    number: 1,
    name: "",
    isActive: true,
    hourlyRate: defaultHourlyRate,
    tableType: "billiard",
    assignedUserId: undefined as number | undefined,
    assignedUsername: undefined as string | undefined,
    customSoundUrl: "",
  })
  const [isGenerating, setIsGenerating] = useState(false)
  const [bulkCreate, setBulkCreate] = useState(false)
  const [bulkCount, setBulkCount] = useState(4)
  const [bulkPrefix, setBulkPrefix] = useState('T')

  const [users, setUsers] = useState<User[]>([])
  const [loadingUsers, setLoadingUsers] = useState(true)

  // Load users when component mounts
  useEffect(() => {
    const loadUsers = async () => {
      try {
        console.log('🔄 Loading users for game table assignment...')
        const response = await fetch('/api/users', {
          credentials: 'include'
        })

        if (response.ok) {
          const usersData = await response.json()
          console.log('✅ Users loaded successfully:', usersData.length, 'users')
          setUsers(usersData)
        } else {
          const errorData = await response.json().catch(() => ({ error: 'Unknown error' }))
          console.error('❌ Failed to load users:', response.status, errorData)
          // Still allow the form to work without user assignment
          setUsers([])
        }
      } catch (error) {
        console.error('❌ Error loading users:', error)
        // Still allow the form to work without user assignment
        setUsers([])
      } finally {
        setLoadingUsers(false)
      }
    }

    loadUsers()
  }, [])

  // Auto-generate unique table number when form loads
  useEffect(() => {
    const generateAutoNumber = () => {
      // Get all existing table numbers (global uniqueness)
      const existingNumbers = existingTables.map((table: GameTable) => table.number)

      // Generate a unique number
      const newNumber = generateUniqueTableNumber(existingNumbers)

      // Update form with the new number
      setFormData(prev => ({
        ...prev,
        number: newNumber,
        name: `Table ${formatTableNumber(newNumber)}`
      }))
    }

    // Only generate if we don't have a number yet or it's the default
    if (formData.number === 1) {
      generateAutoNumber()
    }
  }, [existingTables])

  // Generate a new unique table number
  const generateNewNumber = async () => {
    setIsGenerating(true)

    try {
      // Get all existing table numbers (global uniqueness)
      const existingNumbers = existingTables.map((table: GameTable) => table.number)

      // Generate a new unique number
      const newNumber = generateUniqueTableNumber(existingNumbers)

      // Update form with the new number and auto-generated name
      setFormData(prev => ({
        ...prev,
        number: newNumber,
        name: `Table ${formatTableNumber(newNumber)}`
      }))
    } catch (error) {
      console.error('Failed to generate new number:', error)
    } finally {
      setIsGenerating(false)
    }
  }

  // Handle user selection
  const handleUserChange = (userId: string) => {
    if (userId === "none") {
      setFormData(prev => ({
        ...prev,
        assignedUserId: undefined,
        assignedUsername: undefined
      }))
    } else {
      const selectedUser = users.find(u => u.id.toString() === userId)
      if (selectedUser) {
        setFormData(prev => ({
          ...prev,
          assignedUserId: selectedUser.id,
          assignedUsername: selectedUser.username
        }))
      }
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    if (bulkCreate) {
      return handleBulkSubmit()
    }

    onSubmit(formData)

    // Generate a new unique number for the next table
    const existingNumbers = existingTables.map((table: GameTable) => table.number)
    existingNumbers.push(formData.number) // Include the current number we just submitted
    const newNumber = generateUniqueTableNumber(existingNumbers)

    setFormData({
      number: newNumber,
      name: `Table ${formatTableNumber(newNumber)}`,
      isActive: true,
      hourlyRate: defaultHourlyRate,
      tableType: "billiard",
      assignedUserId: undefined,
      assignedUsername: undefined,
      customSoundUrl: ""
    })
  }

  const handleBulkSubmit = () => {
    if (bulkCount < 1 || bulkCount > 50) {
      toast.error('Bulk count must be between 1 and 50')
      return
    }

    if (!bulkPrefix.trim()) {
      toast.error('Table prefix is required for bulk creation')
      return
    }

    // Generate tables data
    const tablesData = []
    const existingNumbers = existingTables.map(t => t.number)

    for (let i = 1; i <= bulkCount; i++) {
      // Generate unique number for each table
      let uniqueNumber
      do {
        uniqueNumber = Math.floor(Math.random() * 9999) + 1
      } while ([...existingNumbers, ...tablesData.map(t => t.number)].includes(uniqueNumber))

      tablesData.push({
        number: uniqueNumber,
        name: `${bulkPrefix}${i}`,
        isActive: formData.isActive,
        hourlyRate: formData.hourlyRate,
        tableType: formData.tableType,
        assignedUserId: formData.assignedUserId,
        assignedUsername: formData.assignedUsername,
        customSoundUrl: formData.customSoundUrl
      })
    }

    onSubmit(tablesData)
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {/* Bulk Creation Toggle */}
      <div className="flex items-center space-x-2 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
        <Switch
          id="bulkCreate"
          checked={bulkCreate}
          onCheckedChange={setBulkCreate}
        />
        <div>
          <Label htmlFor="bulkCreate" className="text-sm font-medium">{t('settings.bulkCreateTables')}</Label>
          <p className="text-xs text-gray-500">{t('settings.createMultipleTablesAtOnce')}</p>
        </div>
      </div>

      {bulkCreate ? (
        /* Bulk Creation Form */
        <>
          <div>
            <Label htmlFor="bulkCount" className="text-sm font-medium">Number of Tables</Label>
            <Input
              id="bulkCount"
              type="number"
              min="1"
              max="50"
              value={bulkCount}
              onChange={(e) => setBulkCount(parseInt(e.target.value) || 1)}
              className="h-9"
            />
            <p className="text-xs text-gray-500 mt-1">Create 1-50 tables (max 50)</p>
          </div>

          <div>
            <Label htmlFor="bulkPrefix" className="text-sm font-medium">Table Name Prefix</Label>
            <Input
              id="bulkPrefix"
              value={bulkPrefix}
              onChange={(e) => setBulkPrefix(e.target.value)}
              placeholder="T"
              className="h-9"
            />
            <p className="text-xs text-gray-500 mt-1">Will create: {bulkPrefix}1, {bulkPrefix}2, {bulkPrefix}3, ...</p>
          </div>
        </>
      ) : (
        /* Single Table Form */
        <>
          <div className="relative">
            <Label htmlFor="gameTableNumber">{t('settings.tableNumber')}</Label>
            <div className="relative">
              <Input
                id="gameTableNumber"
                type="text"
                value={formatTableNumber(formData.number)}
                readOnly
                className="pr-10 bg-gray-50 font-mono text-center text-lg font-bold"
              />
              <button
                type="button"
                onClick={generateNewNumber}
                disabled={isGenerating}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-blue-600 disabled:opacity-50"
                title="Generate new unique number"
              >
                {isGenerating ? '⏳' : '🔄'}
              </button>
            </div>
            <p className="text-xs text-gray-500 mt-1">
              {t('settings.autoGeneratedUniqueNumber')} • {t('settings.clickToGenerateNew')} 🔄
            </p>
          </div>

          <div>
            <Label htmlFor="gameTableName">{t('settings.tableName')}</Label>
            <Input
              id="gameTableName"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              placeholder={t('settings.gameTableNamePlaceholder')}
              required
            />
            <p className="text-xs text-gray-500 mt-1">{t('settings.displayNameForTable')}</p>
          </div>

          <div>
            <Label htmlFor="gameTableType">{t('settings.tableType')}</Label>
            <Select value={formData.tableType} onValueChange={(value) => setFormData({ ...formData, tableType: value })}>
              <SelectTrigger>
                <SelectValue placeholder={t('settings.selectTableType')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="billiard">{t('settings.billiard')}</SelectItem>
                <SelectItem value="pool">{t('settings.pool')}</SelectItem>
                <SelectItem value="snooker">{t('settings.snooker')}</SelectItem>
              </SelectContent>
            </Select>
            <p className="text-xs text-gray-500 mt-1">{t('settings.typeOfGameTable')}</p>
          </div>

          <div>
            <Label htmlFor="gameTableRate">{t('settings.hourlyRate')}</Label>
            <Input
              id="gameTableRate"
              type="number"
              min="0"
              step="50"
              value={formData.hourlyRate}
              onChange={(e) => setFormData({ ...formData, hourlyRate: Number.parseInt(e.target.value) || 0 })}
              required
            />
            <p className="text-xs text-gray-500 mt-1">{t('settings.costPerHourForTable')}</p>
          </div>

          <SoundUploader
            value={formData.customSoundUrl}
            onChange={(url) => setFormData({ ...formData, customSoundUrl: url })}
            label={t('settings.customAlertSound')}
            placeholder={t('settings.uploadSoundFile')}
          />
        </>
      )}

      {/* User Assignment - Common for both modes */}
      <div>
        <Label htmlFor="assignedUser">{t('settings.assignToUser')}</Label>
        <Select
          value={formData.assignedUserId?.toString() || "none"}
          onValueChange={handleUserChange}
          disabled={loadingUsers}
        >
          <SelectTrigger>
            <SelectValue placeholder={loadingUsers ? "Loading users..." : "Select a user"} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="none">{t('settings.noSpecificUserShared')}</SelectItem>
            {users.map((user) => (
              <SelectItem key={user.id} value={user.id.toString()}>
                {user.displayName}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <p className="text-xs text-gray-500 mt-1">
          {bulkCreate
            ? t('settings.allTablesWillBeAssigned')
            : t('settings.onlyAssignedUserWillSee')
          }
        </p>
      </div>

      {/* Active Status - Common for both modes */}
      <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded">
        <Switch
          id="gameTableActive"
          checked={formData.isActive}
          onCheckedChange={(checked) => setFormData({ ...formData, isActive: checked })}
        />
        <div>
          <Label htmlFor="gameTableActive" className="font-medium">{t('settings.activeTable')}</Label>
          <p className="text-xs text-gray-500">
            {bulkCreate
              ? t('settings.allTablesWillBeCreatedBasedOnSetting')
              : t('settings.tableAvailableWhenActive')
            }
          </p>
        </div>
      </div>

      <DialogFooter>
        <Button type="submit" className="w-full">
          <Plus className="h-4 w-4 mr-2" />
          {bulkCreate ? t('settings.createTables', { count: bulkCount }) : t('settings.addGameTable')}
        </Button>
      </DialogFooter>
    </form>
  )
}



// Edit Game Table Form Component
function EditGameTableForm({ table, onSubmit }: { table: GameTable; onSubmit: (updates: Partial<GameTable>) => void }) {
  const { t } = useSafeTranslation()
  const [formData, setFormData] = useState({
    number: table.number,
    name: table.name,
    isActive: table.isActive,
    hourlyRate: table.hourlyRate,
    tableType: table.tableType,
    assignedUserId: table.assignedUserId || undefined,
    assignedUsername: table.assignedUsername || undefined,
    customSoundUrl: table.customSoundUrl || "",
  })

  const [users, setUsers] = useState<User[]>([])
  const [loadingUsers, setLoadingUsers] = useState(true)

  // Update form data when table prop changes
  useEffect(() => {
    setFormData({
      number: table.number,
      name: table.name,
      isActive: table.isActive,
      hourlyRate: table.hourlyRate,
      tableType: table.tableType,
      assignedUserId: table.assignedUserId || undefined,
      assignedUsername: table.assignedUsername || undefined,
      customSoundUrl: table.customSoundUrl || "",
    })
  }, [table])

  // Load users when component mounts
  useEffect(() => {
    const loadUsers = async () => {
      try {
        console.log('🔄 Loading users for game table edit...')
        const response = await fetch('/api/users', {
          credentials: 'include'
        })

        if (response.ok) {
          const usersData = await response.json()
          console.log('✅ Users loaded successfully for edit:', usersData.length, 'users')
          setUsers(usersData)
        } else {
          const errorData = await response.json().catch(() => ({ error: 'Unknown error' }))
          console.error('❌ Failed to load users for edit:', response.status, errorData)
          setUsers([])
        }
      } catch (error) {
        console.error('❌ Error loading users for edit:', error)
        setUsers([])
      } finally {
        setLoadingUsers(false)
      }
    }

    loadUsers()
  }, [])

  // Handle user selection
  const handleUserChange = (userId: string) => {
    if (userId === "none") {
      setFormData(prev => ({
        ...prev,
        assignedUserId: undefined,
        assignedUsername: undefined
      }))
    } else {
      const selectedUser = users.find(u => u.id.toString() === userId)
      if (selectedUser) {
        setFormData(prev => ({
          ...prev,
          assignedUserId: selectedUser.id,
          assignedUsername: selectedUser.username
        }))
      }
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit(formData)
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <Label htmlFor="editGameTableNumber">{t('settings.tableNumber')}</Label>
        <Input
          id="editGameTableNumber"
          type="text"
          value={formatTableNumber(formData.number)}
          onChange={(e) => {
            // Parse the number from the formatted string
            const num = parseInt(e.target.value.replace(/^0+/, '')) || 1
            setFormData({ ...formData, number: num })
          }}
          className="font-mono text-center"
          required
        />
        <p className="text-xs text-gray-500 mt-1">
          Format: 0001, 0002, etc. Must be globally unique.
        </p>
      </div>
      <div>
        <Label htmlFor="editGameTableName">{t('settings.tableName')}</Label>
        <Input
          id="editGameTableName"
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          required
        />
      </div>
      <div>
        <Label htmlFor="editGameTableType">{t('settings.tableType')}</Label>
        <Select value={formData.tableType} onValueChange={(value) => setFormData({ ...formData, tableType: value })}>
          <SelectTrigger>
            <SelectValue placeholder={t('settings.selectTableType')} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="billiard">{t('settings.billiard')}</SelectItem>
            <SelectItem value="pool">{t('settings.pool')}</SelectItem>
            <SelectItem value="snooker">{t('settings.snooker')}</SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div>
        <Label htmlFor="editAssignedUser">{t('settings.assignToUser')}</Label>
        <Select
          value={formData.assignedUserId?.toString() || "none"}
          onValueChange={handleUserChange}
          disabled={loadingUsers}
        >
          <SelectTrigger>
            <SelectValue placeholder={loadingUsers ? "Loading users..." : "Select a user"} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="none">{t('settings.noSpecificUserShared')}</SelectItem>
            {users.map((user) => (
              <SelectItem key={user.id} value={user.id.toString()}>
                {user.displayName}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <p className="text-xs text-gray-500 mt-1">
          {t('settings.onlyAssignedUserWillSee')}
        </p>
      </div>
      <div>
        <Label htmlFor="editGameTableRate">{t('settings.hourlyRate')}</Label>
        <Input
          id="editGameTableRate"
          type="number"
          min="0"
          step="50"
          value={formData.hourlyRate}
          onChange={(e) => setFormData({ ...formData, hourlyRate: Number.parseInt(e.target.value) || 0 })}
          required
        />
      </div>

      <SoundUploader
        value={formData.customSoundUrl}
        onChange={(url) => setFormData({ ...formData, customSoundUrl: url })}
        label={t('settings.customAlertSound')}
        placeholder={t('settings.uploadSoundFile')}
      />
      <div className="flex items-center space-x-2">
        <Switch
          id="editGameTableActive"
          checked={formData.isActive}
          onCheckedChange={(checked) => setFormData({ ...formData, isActive: checked })}
        />
        <Label htmlFor="editGameTableActive">{t('settings.active')}</Label>
      </div>
      <DialogFooter>
        <Button type="submit">{t('settings.updateGameTable')}</Button>
      </DialogFooter>
    </form>
  )
}

// Add Product Form Component
function AddProductForm({ onSubmit }: { onSubmit: (data: Omit<Product, "id">) => void }) {
  const { t } = useSafeTranslation()
  const [formData, setFormData] = useState({
    name: "",
    price: "",
    category: "coffee",
    isActive: true,
    description: "",
  })

  const [availableCategories, setAvailableCategories] = useState<Category[]>([])

  useEffect(() => {
    const loadCategories = async () => {
      try {
        const response = await fetch('/api/categories')
        if (response.ok) {
          const categoriesData = await response.json()
          setAvailableCategories(categoriesData.map((c: any) => ({
            id: c.id.toString(),
            key: c.key,
            label: c.name,
            icon: c.icon,
            isActive: c.is_active,
            order: c.display_order
          })))
        }
      } catch (error) {
        console.error("Failed to load categories:", error)
      }
    }

    loadCategories()
  }, [])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit({
      ...formData,
      price: Number(formData.price)
    })
    setFormData({ name: "", price: "", category: "coffee", isActive: true, description: "" })
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <Label htmlFor="productName">{t('settings.productName')}</Label>
        <Input
          id="productName"
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          required
        />
      </div>
      <div>
        <Label htmlFor="productPrice">{t('settings.productPrice')}</Label>
        <Input
          id="productPrice"
          type="number"
          value={formData.price}
          onChange={(e) => setFormData({ ...formData, price: e.target.value })}
          required
        />
      </div>
      <div>
        <Label htmlFor="productCategory">{t('settings.productCategory')}</Label>
        <Select value={formData.category} onValueChange={(value) => setFormData({ ...formData, category: value })}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {availableCategories
              .filter((cat) => cat.isActive)
              .map((category) => (
                <SelectItem key={category.key} value={category.key}>
                  {category.label}
                </SelectItem>
              ))}
          </SelectContent>
        </Select>
      </div>
      <div>
        <Label htmlFor="productDescription">{t('settings.productDescription')}</Label>
        <Textarea
          id="productDescription"
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
          placeholder={t('settings.briefProductDescription')}
        />
      </div>
      <div className="flex items-center space-x-2">
        <Switch
          id="productActive"
          checked={formData.isActive}
          onCheckedChange={(checked) => setFormData({ ...formData, isActive: checked })}
        />
        <Label htmlFor="productActive">{t('settings.active')}</Label>
      </div>
      <DialogFooter>
        <Button type="submit">{t('settings.addProduct')}</Button>
      </DialogFooter>
    </form>
  )
}

// Edit Product Form Component
function EditProductForm({ product, onSubmit }: { product: Product; onSubmit: (updates: Partial<Product>) => void }) {
  const { t } = useSafeTranslation()
  const [formData, setFormData] = useState({
    name: product.name,
    price: product.price.toString(),
    category: product.category,
    isActive: product.isActive,
    description: product.description || "",
  })

  const [availableCategories, setAvailableCategories] = useState<Category[]>([])

  useEffect(() => {
    const loadCategories = async () => {
      try {
        const response = await fetch('/api/categories')
        if (response.ok) {
          const categoriesData = await response.json()
          setAvailableCategories(categoriesData.map((c: any) => ({
            id: c.id.toString(),
            key: c.key,
            label: c.name,
            icon: c.icon,
            isActive: c.is_active,
            order: c.display_order
          })))
        }
      } catch (error) {
        console.error("Failed to load categories:", error)
      }
    }

    loadCategories()
  }, [])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit({
      ...formData,
      price: Number(formData.price)
    })
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <Label htmlFor="editProductName">{t('settings.productName')}</Label>
        <Input
          id="editProductName"
          value={formData.name}
          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
          required
        />
      </div>
      <div>
        <Label htmlFor="editProductPrice">{t('settings.productPrice')}</Label>
        <Input
          id="editProductPrice"
          type="number"
          value={formData.price}
          onChange={(e) => setFormData({ ...formData, price: e.target.value })}
          required
        />
      </div>
      <div>
        <Label htmlFor="editProductCategory">{t('settings.productCategory')}</Label>
        <Select value={formData.category} onValueChange={(value) => setFormData({ ...formData, category: value })}>
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {availableCategories
              .filter((cat) => cat.isActive)
              .map((category) => (
                <SelectItem key={category.key} value={category.key}>
                  {category.label}
                </SelectItem>
              ))}
          </SelectContent>
        </Select>
      </div>
      <div>
        <Label htmlFor="editProductDescription">{t('settings.productDescription')}</Label>
        <Textarea
          id="editProductDescription"
          value={formData.description}
          onChange={(e) => setFormData({ ...formData, description: e.target.value })}
        />
      </div>
      <div className="flex items-center space-x-2">
        <Switch
          id="editProductActive"
          checked={formData.isActive}
          onCheckedChange={(checked) => setFormData({ ...formData, isActive: checked })}
        />
        <Label htmlFor="editProductActive">{t('settings.active')}</Label>
      </div>
      <DialogFooter>
        <Button type="submit">{t('settings.updateProduct')}</Button>
      </DialogFooter>
    </form>
  )
}

// Add Category Form Component
function AddCategoryForm({ onSubmit }: { onSubmit: (data: Omit<Category, "id">) => void }) {
  const { t } = useSafeTranslation()
  const [formData, setFormData] = useState({
    key: "",
    label: "",
    icon: "Coffee",
    isActive: true,
    order: 1,
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit(formData)
    setFormData({ key: "", label: "", icon: "Coffee", isActive: true, order: 1 })
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <Label htmlFor="categoryKey">{t('settings.categoryKey')}</Label>
        <Input
          id="categoryKey"
          value={formData.key}
          onChange={(e) => setFormData({ ...formData, key: e.target.value.toLowerCase().replace(/\s+/g, "_") })}
          placeholder={t('settings.categoryKeyPlaceholder')}
          required
        />
      </div>
      <div>
        <Label htmlFor="categoryLabel">{t('settings.categoryLabel')}</Label>
        <Input
          id="categoryLabel"
          value={formData.label}
          onChange={(e) => setFormData({ ...formData, label: e.target.value })}
          placeholder={t('settings.categoryLabelPlaceholder')}
          required
        />
      </div>
      <div>
        <Label htmlFor="categoryOrder">{t('settings.displayOrder')}</Label>
        <Input
          id="categoryOrder"
          type="number"
          value={formData.order}
          onChange={(e) => setFormData({ ...formData, order: Number.parseInt(e.target.value) })}
          required
        />
      </div>
      <div className="flex items-center space-x-2">
        <Switch
          id="categoryActive"
          checked={formData.isActive}
          onCheckedChange={(checked) => setFormData({ ...formData, isActive: checked })}
        />
        <Label htmlFor="categoryActive">{t('settings.active')}</Label>
      </div>
      <DialogFooter>
        <Button type="submit">{t('settings.addCategory')}</Button>
      </DialogFooter>
    </form>
  )
}

// Edit Category Form Component
function EditCategoryForm({
  category,
  onSubmit,
}: { category: Category; onSubmit: (updates: Partial<Category>) => void }) {
  const { t } = useSafeTranslation()
  const [formData, setFormData] = useState({
    key: category.key,
    label: category.label,
    icon: category.icon,
    isActive: category.isActive,
    order: category.order,
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit(formData)
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <Label htmlFor="editCategoryKey">{t('settings.categoryKey')}</Label>
        <Input
          id="editCategoryKey"
          value={formData.key}
          onChange={(e) => setFormData({ ...formData, key: e.target.value.toLowerCase().replace(/\s+/g, "_") })}
          required
        />
      </div>
      <div>
        <Label htmlFor="editCategoryLabel">{t('settings.categoryLabel')}</Label>
        <Input
          id="editCategoryLabel"
          value={formData.label}
          onChange={(e) => setFormData({ ...formData, label: e.target.value })}
          required
        />
      </div>
      <div>
        <Label htmlFor="editCategoryOrder">{t('settings.displayOrder')}</Label>
        <Input
          id="editCategoryOrder"
          type="number"
          value={formData.order}
          onChange={(e) => setFormData({ ...formData, order: Number.parseInt(e.target.value) })}
          required
        />
      </div>
      <div className="flex items-center space-x-2">
        <Switch
          id="editCategoryActive"
          checked={formData.isActive}
          onCheckedChange={(checked) => setFormData({ ...formData, isActive: checked })}
        />
        <Label htmlFor="editCategoryActive">{t('settings.active')}</Label>
      </div>
      <DialogFooter>
        <Button type="submit">{t('settings.updateCategory')}</Button>
      </DialogFooter>
    </form>
  )
}
