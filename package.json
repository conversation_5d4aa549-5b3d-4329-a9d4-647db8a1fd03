{"name": "bbm", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "migrate-translations": "node scripts/migrate-translations.js", "add-time-limit": "node scripts/add-time-limit-column.js", "clean": "rm -rf .next && npm run dev", "clean-full": "rm -rf .next node_modules package-lock.json && npm install && npm run dev"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "latest", "@radix-ui/react-alert-dialog": "latest", "@radix-ui/react-aspect-ratio": "latest", "@radix-ui/react-avatar": "latest", "@radix-ui/react-checkbox": "latest", "@radix-ui/react-collapsible": "latest", "@radix-ui/react-context-menu": "latest", "@radix-ui/react-dialog": "latest", "@radix-ui/react-dropdown-menu": "latest", "@radix-ui/react-hover-card": "latest", "@radix-ui/react-label": "latest", "@radix-ui/react-menubar": "latest", "@radix-ui/react-navigation-menu": "latest", "@radix-ui/react-popover": "latest", "@radix-ui/react-progress": "latest", "@radix-ui/react-radio-group": "latest", "@radix-ui/react-scroll-area": "latest", "@radix-ui/react-select": "latest", "@radix-ui/react-separator": "latest", "@radix-ui/react-slider": "latest", "@radix-ui/react-slot": "latest", "@radix-ui/react-switch": "latest", "@radix-ui/react-tabs": "latest", "@radix-ui/react-toast": "latest", "@radix-ui/react-toggle": "latest", "@radix-ui/react-toggle-group": "latest", "@radix-ui/react-tooltip": "latest", "@types/bcryptjs": "^2.4.6", "@types/crypto-js": "^4.2.2", "@types/jsonwebtoken": "^9.0.9", "@types/pg": "^8.15.2", "@types/qrcode": "^1.5.5", "@vercel/postgres": "^0.10.0", "autoprefixer": "^10.4.20", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "latest", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "embla-carousel-react": "latest", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.1.0", "input-otp": "latest", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.454.0", "next": "^15.3.3", "next-themes": "latest", "pg": "^8.16.0", "qrcode": "^1.5.4", "react": "^18.3.1", "react-day-picker": "latest", "react-dom": "^18.3.1", "react-error-boundary": "^6.0.0", "react-hook-form": "latest", "react-i18next": "^15.5.2", "react-resizable-panels": "latest", "recharts": "latest", "redis": "^5.5.6", "sonner": "latest", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "latest", "zod": "^3.24.1"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "postcss": "^8", "tailwindcss": "^3.4.17", "typescript": "^5"}}