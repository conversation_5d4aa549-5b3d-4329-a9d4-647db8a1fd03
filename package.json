{"name": "bbm", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "migrate-translations": "node scripts/migrate-translations.js", "clean": "rm -rf .next && npm run dev", "clean-full": "rm -rf .next node_modules package-lock.json && npm install && npm run dev"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@types/bcryptjs": "^2.4.6", "@types/crypto-js": "^4.2.2", "@types/jsonwebtoken": "^9.0.9", "@types/pg": "^8.15.2", "@types/qrcode": "^1.5.5", "@vercel/postgres": "^0.10.0", "autoprefixer": "^10.4.20", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^0.2.0", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.1.0", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.454.0", "next": "^15.3.3", "next-themes": "^0.2.1", "pg": "^8.16.0", "qrcode": "^1.5.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react-error-boundary": "^6.0.0", "react-hook-form": "^7.48.2", "react-i18next": "^15.5.2", "lightweight-charts": "^4.1.3", "redis": "^5.5.6", "sonner": "^1.4.0", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.1"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "postcss": "^8", "tailwindcss": "^3.4.17", "typescript": "^5"}}