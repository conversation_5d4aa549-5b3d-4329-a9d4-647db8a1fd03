import pool from './db'

export interface Migration {
  id: string
  name: string
  description: string
  sql: string
  rollback?: string
}

export const migrations: Migration[] = [
  {
    id: '001_add_time_limit_column',
    name: 'Add time_limit column to games table',
    description: 'Adds time_limit column for game time limit persistence across page refreshes',
    sql: `
      -- Add time_limit column to games table
      ALTER TABLE games ADD COLUMN IF NOT EXISTS time_limit INTEGER;
      
      -- Add index for better performance
      CREATE INDEX IF NOT EXISTS idx_games_time_limit ON games(time_limit);
      
      -- Add comment for documentation
      COMMENT ON COLUMN games.time_limit IS 'Time limit for the game in minutes. NULL means unlimited.';
      
      -- Add check constraint
      ALTER TABLE games ADD CONSTRAINT IF NOT EXISTS check_games_time_limit_positive 
      CHECK (time_limit IS NULL OR time_limit > 0);
      
      -- Update existing active games to have unlimited time limit
      UPDATE games SET time_limit = NULL WHERE time_limit IS NULL AND status = 'active';
    `,
    rollback: `
      -- Remove constraint
      ALTER TABLE games DROP CONSTRAINT IF EXISTS check_games_time_limit_positive;
      
      -- Remove index
      DROP INDEX IF EXISTS idx_games_time_limit;
      
      -- Remove column
      ALTER TABLE games DROP COLUMN IF EXISTS time_limit;
    `
  }
]

export async function createMigrationsTable() {
  try {
    await pool.query(`
      CREATE TABLE IF NOT EXISTS migrations (
        id VARCHAR(255) PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        success BOOLEAN DEFAULT true
      )
    `)
    console.log('✅ Migrations table ready')
  } catch (error) {
    console.error('❌ Failed to create migrations table:', error)
    throw error
  }
}

export async function isMigrationExecuted(migrationId: string): Promise<boolean> {
  try {
    const result = await pool.query(
      'SELECT id FROM migrations WHERE id = $1 AND success = true',
      [migrationId]
    )
    return result.rows.length > 0
  } catch (error) {
    console.error(`❌ Failed to check migration ${migrationId}:`, error)
    return false
  }
}

export async function executeMigration(migration: Migration): Promise<boolean> {
  try {
    console.log(`🔄 Executing migration: ${migration.name}`)
    
    // Execute the migration SQL
    await pool.query(migration.sql)
    
    // Record the migration as executed
    await pool.query(
      'INSERT INTO migrations (id, name, description) VALUES ($1, $2, $3) ON CONFLICT (id) DO UPDATE SET executed_at = CURRENT_TIMESTAMP',
      [migration.id, migration.name, migration.description]
    )
    
    console.log(`✅ Migration completed: ${migration.name}`)
    return true
  } catch (error) {
    console.error(`❌ Migration failed: ${migration.name}`, error)
    
    // Record the failed migration
    try {
      await pool.query(
        'INSERT INTO migrations (id, name, description, success) VALUES ($1, $2, $3, false) ON CONFLICT (id) DO UPDATE SET executed_at = CURRENT_TIMESTAMP, success = false',
        [migration.id, migration.name, migration.description]
      )
    } catch (recordError) {
      console.error('❌ Failed to record migration failure:', recordError)
    }
    
    return false
  }
}

export async function runPendingMigrations(): Promise<{ success: boolean; executed: string[]; failed: string[] }> {
  const executed: string[] = []
  const failed: string[] = []
  
  try {
    // Ensure migrations table exists
    await createMigrationsTable()
    
    // Run each migration if not already executed
    for (const migration of migrations) {
      const isExecuted = await isMigrationExecuted(migration.id)
      
      if (!isExecuted) {
        console.log(`📋 Pending migration: ${migration.name}`)
        const success = await executeMigration(migration)
        
        if (success) {
          executed.push(migration.id)
        } else {
          failed.push(migration.id)
        }
      } else {
        console.log(`⏭️ Skipping already executed migration: ${migration.name}`)
      }
    }
    
    if (executed.length > 0) {
      console.log(`🎉 Successfully executed ${executed.length} migrations:`, executed)
    }
    
    if (failed.length > 0) {
      console.log(`❌ Failed to execute ${failed.length} migrations:`, failed)
    }
    
    if (executed.length === 0 && failed.length === 0) {
      console.log('✅ All migrations are up to date')
    }
    
    return {
      success: failed.length === 0,
      executed,
      failed
    }
  } catch (error) {
    console.error('❌ Migration system failed:', error)
    return {
      success: false,
      executed,
      failed: ['system_error']
    }
  }
}

export async function checkTimeLimitColumnExists(): Promise<boolean> {
  try {
    const result = await pool.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'games' AND column_name = 'time_limit'
    `)
    return result.rows.length > 0
  } catch (error) {
    console.error('❌ Failed to check time_limit column:', error)
    return false
  }
}
