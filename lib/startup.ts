import { runPendingMigrations, checkTimeLimitColumnExists } from './migrations'

let migrationCheckCompleted = false
let migrationPromise: Promise<boolean> | null = null

export async function ensureDatabaseReady(): Promise<boolean> {
  // If migration check is already completed, return immediately
  if (migrationCheckCompleted) {
    return true
  }

  // If migration is already in progress, wait for it
  if (migrationPromise) {
    return migrationPromise
  }

  // Start migration check
  migrationPromise = performMigrationCheck()
  return migrationPromise
}

async function performMigrationCheck(): Promise<boolean> {
  try {
    console.log('🔄 BBM Startup: Checking database readiness...')
    
    // Check if time_limit column exists
    const hasColumn = await checkTimeLimitColumnExists()
    
    if (hasColumn) {
      console.log('✅ BBM Startup: Database is ready')
      migrationCheckCompleted = true
      return true
    }
    
    console.log('📋 BBM Startup: Database needs migration, running auto-migration...')
    
    // Run pending migrations
    const result = await runPendingMigrations()
    
    if (result.success) {
      console.log('🎉 BBM Startup: Auto-migration completed successfully')
      migrationCheckCompleted = true
      return true
    } else {
      console.error('❌ BBM Startup: Auto-migration failed')
      console.error('📋 Manual intervention required: Visit /migrate-time-limit')
      return false
    }
  } catch (error) {
    console.error('❌ BBM Startup: Migration check failed:', error)
    return false
  }
}

// Auto-run migration check on module load (server-side only)
if (typeof window === 'undefined') {
  // Only run on server-side
  ensureDatabaseReady().catch(error => {
    console.error('❌ BBM Startup: Failed to ensure database readiness:', error)
  })
}
