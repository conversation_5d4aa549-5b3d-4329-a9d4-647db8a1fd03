<!DOCTYPE html>
<html>
<head>
    <title>Debug Time Limits</title>
    <script src="https://cdn.jsdelivr.net/npm/js-cookie@3.0.5/dist/js.cookie.min.js"></script>
</head>
<body>
    <h1>Debug Time Limits</h1>
    <div>
        <button onclick="testSetCookie()">Set Table 1 Time Limit (30 min)</button>
        <button onclick="testGetCookies()">Get All Cookies</button>
        <button onclick="testLoadFunction()">Test Load Function</button>
        <button onclick="clearAllCookies()">Clear All Cookies</button>
    </div>
    <div id="result" style="margin-top: 20px; padding: 10px; background: #f0f0f0;"></div>

    <script>
        function log(message) {
            const result = document.getElementById('result');
            result.innerHTML += message + '<br>';
            console.log(message);
        }

        function testSetCookie() {
            log('🔄 Setting cookie...');
            
            const cookieKey = 'table_1_time_limit';
            const cookieValue = '30';
            
            Cookies.set(cookieKey, cookieValue, {
                expires: 7,
                sameSite: 'strict',
                secure: window.location.protocol === 'https:'
            });
            
            log(`✅ Cookie set: ${cookieKey} = ${cookieValue}`);
            
            // Verify it was set
            const readBack = Cookies.get(cookieKey);
            log(`📋 Read back: ${readBack}`);
        }

        function testGetCookies() {
            log('🔄 Getting all cookies...');
            
            const allCookies = Cookies.get();
            log(`🍪 All cookies: ${JSON.stringify(allCookies, null, 2)}`);
            
            // Test the loading function logic
            const cookieLimits = {};
            Object.keys(allCookies).forEach(cookieName => {
                if (cookieName.startsWith('table_') && cookieName.endsWith('_time_limit')) {
                    const tableNumberMatch = cookieName.match(/table_(\\d+)_time_limit/);
                    if (tableNumberMatch) {
                        const tableNumber = parseInt(tableNumberMatch[1]);
                        const cookieValue = allCookies[cookieName];
                        const limit = cookieValue === 'unlimited' ? 'unlimited' : parseInt(cookieValue);
                        cookieLimits[tableNumber] = limit;
                        log(`📋 Found table ${tableNumber} limit: ${limit}`);
                    }
                }
            });
            
            log(`🎱 Table limits: ${JSON.stringify(cookieLimits, null, 2)}`);
        }

        function testLoadFunction() {
            log('🔄 Testing load function...');
            
            // Exact copy of the loadTimeLimitsFromCookies function
            const cookieLimits = {};
            
            // Get all cookies and find table time limit cookies
            const allCookies = Cookies.get();
            Object.keys(allCookies).forEach(cookieName => {
                if (cookieName.startsWith('table_') && cookieName.endsWith('_time_limit')) {
                    const tableNumberMatch = cookieName.match(/table_(\\d+)_time_limit/);
                    if (tableNumberMatch) {
                        const tableNumber = parseInt(tableNumberMatch[1]);
                        const cookieValue = allCookies[cookieName];
                        const limit = cookieValue === 'unlimited' ? 'unlimited' : parseInt(cookieValue);
                        cookieLimits[tableNumber] = limit;
                        log(`📋 Loaded time limit from cookie for table ${tableNumber}: ${limit}`);
                    }
                }
            });
            
            log(`✅ Final cookie limits: ${JSON.stringify(cookieLimits, null, 2)}`);
            return cookieLimits;
        }

        function clearAllCookies() {
            log('🗑️ Clearing all cookies...');
            
            const allCookies = Cookies.get();
            Object.keys(allCookies).forEach(cookieName => {
                if (cookieName.startsWith('table_') && cookieName.endsWith('_time_limit')) {
                    Cookies.remove(cookieName);
                    log(`🗑️ Removed cookie: ${cookieName}`);
                }
            });
            
            log('✅ All table time limit cookies cleared');
        }

        // Test on page load
        window.onload = function() {
            log('🚀 Page loaded, testing cookies...');
            testGetCookies();
        };
    </script>
</body>
</html>
