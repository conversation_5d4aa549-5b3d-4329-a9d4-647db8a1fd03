-- Migration: Update login_settings table to support dynamic waiters
-- This migration converts the old waiter1_*/waiter2_* structure to a dynamic waiters JSONB array

-- Step 1: Add new columns for dynamic waiters
ALTER TABLE login_settings 
ADD COLUMN IF NOT EXISTS waiters JSONB DEFAULT '[]'::jsonb;

-- Step 2: Migrate existing data from old structure to new structure
UPDATE login_settings 
SET waiters = (
  SELECT jsonb_agg(waiter_data)
  FROM (
    SELECT jsonb_build_object(
      'id', '1',
      'display_name', waiter1_display_name,
      'username', waiter1_username,
      'password', waiter1_password,
      'enabled', waiter1_enabled
    ) as waiter_data
    WHERE waiter1_enabled = true
    
    UNION ALL
    
    SELECT jsonb_build_object(
      'id', '2',
      'display_name', waiter2_display_name,
      'username', waiter2_username,
      'password', waiter2_password,
      'enabled', waiter2_enabled
    ) as waiter_data
    WHERE waiter2_enabled = true
  ) waiters_subquery
)
WHERE waiters = '[]'::jsonb OR waiters IS NULL;

-- Step 3: Set default waiters if no data exists
UPDATE login_settings 
SET waiters = '[
  {
    "id": "1",
    "display_name": "Waiter One:",
    "username": "waiter1",
    "password": "waiter1",
    "enabled": true
  },
  {
    "id": "2", 
    "display_name": "Waiter Two:",
    "username": "waiter2",
    "password": "waiter2",
    "enabled": true
  }
]'::jsonb
WHERE waiters = '[]'::jsonb OR waiters IS NULL;

-- Step 4: Create backup of old columns (optional - for rollback purposes)
-- You can remove these columns later once you're confident the migration worked
-- ALTER TABLE login_settings DROP COLUMN waiter1_display_name;
-- ALTER TABLE login_settings DROP COLUMN waiter1_username;
-- ALTER TABLE login_settings DROP COLUMN waiter1_password;
-- ALTER TABLE login_settings DROP COLUMN waiter1_enabled;
-- ALTER TABLE login_settings DROP COLUMN waiter2_display_name;
-- ALTER TABLE login_settings DROP COLUMN waiter2_username;
-- ALTER TABLE login_settings DROP COLUMN waiter2_password;
-- ALTER TABLE login_settings DROP COLUMN waiter2_enabled;

-- Step 5: Add index for better performance on waiters JSONB column
CREATE INDEX IF NOT EXISTS idx_login_settings_waiters_gin ON login_settings USING gin(waiters);

-- Step 6: Insert default settings if table is empty
INSERT INTO login_settings (
    waiters_section_enabled,
    waiters_section_title,
    waiters
) 
SELECT 
    true,
    'Waiters Accounts:',
    '[
      {
        "id": "1",
        "display_name": "Waiter One:",
        "username": "waiter1", 
        "password": "waiter1",
        "enabled": true
      },
      {
        "id": "2",
        "display_name": "Waiter Two:",
        "username": "waiter2",
        "password": "waiter2", 
        "enabled": true
      }
    ]'::jsonb
WHERE NOT EXISTS (SELECT 1 FROM login_settings);
