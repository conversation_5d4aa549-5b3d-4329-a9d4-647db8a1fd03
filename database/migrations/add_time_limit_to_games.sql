-- Migration: Add time_limit column to games table
-- This allows storing time limits for individual games in the database
-- for persistence across page refreshes

-- Add time_limit column to games table
-- NULL means unlimited, integer value is time limit in minutes
ALTER TABLE games 
ADD COLUMN IF NOT EXISTS time_limit INTEGER;

-- Add index for better performance when querying by time_limit
CREATE INDEX IF NOT EXISTS idx_games_time_limit ON games(time_limit);

-- Add comment for documentation
COMMENT ON COLUMN games.time_limit IS 'Time limit for the game in minutes. NULL means unlimited.';

-- Update existing active games to have unlimited time limit if not set
UPDATE games 
SET time_limit = NULL 
WHERE time_limit IS NULL AND status = 'active';

-- Add a check constraint to ensure time_limit is positive when set
ALTER TABLE games 
ADD CONSTRAINT check_games_time_limit_positive 
CHECK (time_limit IS NULL OR time_limit > 0);

-- Create a function to get time remaining for a game
CREATE OR REPLACE FUNCTION get_game_time_remaining(game_id INTEGER)
RETURNS INTEGER AS $$
DECLARE
    game_start TIMESTAMP;
    game_limit INTEGER;
    elapsed_minutes INTEGER;
    remaining_minutes INTEGER;
BEGIN
    -- Get game start time and time limit
    SELECT start_time, time_limit 
    INTO game_start, game_limit
    FROM games 
    WHERE id = game_id AND status = 'active';
    
    -- If game not found or unlimited, return NULL
    IF game_start IS NULL OR game_limit IS NULL THEN
        RETURN NULL;
    END IF;
    
    -- Calculate elapsed time in minutes
    elapsed_minutes := EXTRACT(EPOCH FROM (NOW() - game_start)) / 60;
    
    -- Calculate remaining time
    remaining_minutes := game_limit - elapsed_minutes;
    
    -- Return remaining time (can be negative if over limit)
    RETURN remaining_minutes;
END;
$$ LANGUAGE plpgsql;

-- Create a function to check if a game has exceeded its time limit
CREATE OR REPLACE FUNCTION is_game_over_time_limit(game_id INTEGER)
RETURNS BOOLEAN AS $$
DECLARE
    remaining_time INTEGER;
BEGIN
    remaining_time := get_game_time_remaining(game_id);
    
    -- If unlimited or time remaining, return false
    IF remaining_time IS NULL OR remaining_time > 0 THEN
        RETURN FALSE;
    END IF;
    
    -- Time limit exceeded
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Add comments for the functions
COMMENT ON FUNCTION get_game_time_remaining(INTEGER) IS 'Returns remaining time in minutes for a game. NULL if unlimited or game not found.';
COMMENT ON FUNCTION is_game_over_time_limit(INTEGER) IS 'Returns true if the game has exceeded its time limit.';

-- Create a view for active games with time information
CREATE OR REPLACE VIEW active_games_with_time AS
SELECT 
    g.*,
    EXTRACT(EPOCH FROM (NOW() - g.start_time)) / 60 AS elapsed_minutes,
    get_game_time_remaining(g.id) AS remaining_minutes,
    is_game_over_time_limit(g.id) AS is_over_limit,
    u.username as created_by_username,
    u.full_name as created_by_name,
    u.role as created_by_role
FROM games g
LEFT JOIN users u ON g.created_by = u.id
WHERE g.status = 'active'
ORDER BY g.start_time DESC;

-- Add comment for the view
COMMENT ON VIEW active_games_with_time IS 'View showing active games with calculated time information including elapsed and remaining time.';

-- Grant permissions to the view (adjust role names as needed)
-- GRANT SELECT ON active_games_with_time TO your_app_role;
