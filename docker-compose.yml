version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: bbm_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: bbm_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./schema.sql:/docker-entrypoint-initdb.d/01-schema.sql
      - ./scripts/create-login-settings-table.sql:/docker-entrypoint-initdb.d/02-login-settings.sql
    networks:
      - bbm_network

  # Optional: pgAdmin for database management
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: bbm_pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
    ports:
      - "8080:80"
    depends_on:
      - postgres
    networks:
      - bbm_network

volumes:
  postgres_data:

networks:
  bbm_network:
    driver: bridge
